from typing import Annotated, List
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, Field
from app.utils.dependency import get_current_user
from app.utils.external_calls import verify_organization
from app.utils.qdrant_utils import get_knowledge_from_kb, ensure_qdrant_collection
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()


class KnowledgeSearchRequest(BaseModel):
    query: str = Field(..., min_length=1, max_length=1000)
    organization_id: str = Field(..., min_length=1)
    top: int = Field(default=5, ge=1, le=20)
    threshold: float = Field(default=0.7, ge=0.0, le=1.0)


class KnowledgeSearchResponse(BaseModel):
    results: List[str]
    total_found: int
    organization_id: str


class KnowledgeStatusResponse(BaseModel):
    has_data: bool
    organization_id: str
    collection_exists: bool
    points_count: int = 0


@router.post("/knowledge/search", response_model=KnowledgeSearchResponse)
async def search_knowledge(
    request: KnowledgeSearchRequest,
    token: Annotated[dict, Depends(get_current_user)],
):
    """
    Internal API endpoint for searching organization knowledgebase.
    Used by other microservices like deep_ellum.
    """
    try:
        # Verify the token and organization access
        user_id = token["decoded"].get("user_id")
        organization_id = request.organization_id
        
        logger.info(f"Internal knowledge search for org {organization_id} by user {user_id}")
        
        # Get knowledge from knowledgebase
        knowledge_results = await get_knowledge_from_kb(
            organization_id=organization_id,
            query=request.query,
            top=request.top
        )
        

        filtered_results = knowledge_results
        
        logger.info(f"Found {len(filtered_results)} knowledge results for query: {request.query}")
        
        return KnowledgeSearchResponse(
            results=filtered_results,
            total_found=len(filtered_results),
            organization_id=organization_id
        )
        
    except Exception as e:
        logger.error(f"Error in internal knowledge search: {e}")
        raise HTTPException(
            status_code=500,
            detail="Error searching knowledgebase"
        )


@router.get("/knowledge/status", response_model=KnowledgeStatusResponse)
async def get_knowledge_status(
    organization_id: str,
    token: Annotated[dict, Depends(get_current_user)],
):
    """
    Check if knowledgebase exists and has data for the organization.
    """
    try:
        user_id = token["decoded"].get("user_id")
        logger.info(f"Checking knowledge status for org {organization_id} by user {user_id}")
        
        # Check if collection exists and get point count
        try:
            from app.utils.qdrant_utils import qdrant_client
            collection_name = f"kb_{organization_id}"
            
            collection_info = qdrant_client.get_collection(collection_name=collection_name)
            has_data = collection_info.points_count > 0
            points_count = collection_info.points_count
            collection_exists = True
            
            logger.info(f"Collection {collection_name} exists with {points_count} points")
            
        except Exception as e:
            logger.info(f"Collection for org {organization_id} does not exist or is empty: {e}")
            has_data = False
            points_count = 0
            collection_exists = False
        
        return KnowledgeStatusResponse(
            has_data=has_data,
            organization_id=organization_id,
            collection_exists=collection_exists,
            points_count=points_count
        )
        
    except Exception as e:
        logger.error(f"Error checking knowledge status: {e}")
        raise HTTPException(
            status_code=500,
            detail="Error checking knowledgebase status"
        )


@router.get("/knowledge/info")
async def get_knowledge_info(
    organization_id: str,
    token: Annotated[dict, Depends(get_current_user)],
):
    """
    Get general information about the organization's knowledgebase.
    """
    try:
        user_id = token["decoded"].get("user_id")
        logger.info(f"Getting knowledge info for org {organization_id} by user {user_id}")
        
        # Get collection info
        try:
            from app.utils.qdrant_utils import qdrant_client
            collection_name = f"kb_{organization_id}"
            
            collection_info = qdrant_client.get_collection(collection_name=collection_name)
            
            return {
                "organization_id": organization_id,
                "collection_name": collection_name,
                "points_count": collection_info.points_count,
                "vector_size": collection_info.config.params.vectors.size,
                "distance": collection_info.config.params.vectors.distance.name,
                "status": "active" if collection_info.points_count > 0 else "empty"
            }
            
        except Exception as e:
            logger.info(f"Collection for org {organization_id} does not exist: {e}")
            return {
                "organization_id": organization_id,
                "collection_name": f"kb_{organization_id}",
                "points_count": 0,
                "status": "not_found"
            }
        
    except Exception as e:
        logger.error(f"Error getting knowledge info: {e}")
        raise HTTPException(
            status_code=500,
            detail="Error retrieving knowledgebase information"
        )
