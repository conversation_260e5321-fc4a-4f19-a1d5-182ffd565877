from contextlib import asynccontextmanager

import uvicorn
from app.utils.logger import get_logger
from fastapi import FastAP<PERSON>
from fastapi.openapi.utils import get_openapi
from starlette.middleware.cors import CORSMiddleware

from app.utils.external_calls import init_redis

# from app.config import REDIS_URL
from .database.database import Base, async_engine
# Import routers
from .routers import chat, file_upload, metrics, scrape, social_media

# import redis.asyncio as redis

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Initialize Redis
    # app.state.redis_client = redis.from_url(REDIS_URL, encoding="utf8", decode_responses=True)
    await init_redis()
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    # await create_buckets()
    logger.info("Startup complete.")
    yield
    # await app.state.redis_client.close()
    logger.info("Shutdown complete.")


app = FastAPI(
    lifespan=lifespan,
    title="File Chat & Knowledgebase API",
    openapi_url="/api/v1/chat/openapi.json",
    docs_url="/api/v1/chat/docs",
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Custom OpenAPI schema to include bearer token
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    openapi_schema = get_openapi(
        title="KIMEV",
        version="1.0.0",
        description="Do cool AI Stuffs",
        routes=app.routes,
    )
    openapi_schema["components"]["securitySchemes"] = {
        "Bearer": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}
    }
    for path in openapi_schema["paths"].values():
        for method in path.values():
            if "security" in method:
                method["security"].append({"Bearer": []})
            else:
                method["security"] = [{"Bearer": []}]
            # Ensure endpoint-level tags take precedence
            if "tags" in method and len(method["tags"]) > 1:
                if "Knowledge Base" in method["tags"]:
                    method["tags"] = ["Knowledge Base"]
                else:
                    method["tags"] = method["tags"][:1]

    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi

# Include routers
app.include_router(chat.router, prefix="/api/v1/chat", tags=["chat"])

# Add a debug route to check file info
@app.get("/api/v1/debug/file")
async def debug_file_info(file_id: str):
    """Debug endpoint to check file info"""
    from app.gen_models.gemini_model import client as gemini_client
    try:
        gemini_file = gemini_client.files.get(name=file_id)
        return {"file_id": gemini_file.name, "status": "found"}
    except Exception as e:
        return {"error": str(e)}
app.include_router(file_upload.router, prefix="/api/v1/file", tags=["file"])
app.include_router(scrape.router, prefix="/api/v1/url", tags=["scrape"])
app.include_router(metrics.router, prefix="/api/v1/view", tags=["metrics"])
app.include_router(social_media.router, prefix="/api/v1/social", tags=["social-media"])

# Internal API for microservice communication
from app.routers import internal
app.include_router(internal.router, prefix="/api/v1/internal", tags=["internal"])


@app.get("/health")
async def health_check():
    return {"status": "ok"}


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001, workers=4, reload=True)
