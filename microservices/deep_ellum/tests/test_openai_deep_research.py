"""
Tests for OpenAI Deep Research integration.
"""

import pytest
import async<PERSON>
from unittest.mock import Mock, patch, AsyncMock
from app.agents.openai_deep_research_agent import OpenAIDeepResearchAgent
from app.services.openai_deep_research_service import OpenAIDeepResearchService
from app.models.schemas import DeepResearchModel, DeepResearchRequest


class TestOpenAIDeepResearchAgent:
    """Test cases for the OpenAI Deep Research Agent."""
    
    @pytest.fixture
    def agent(self):
        """Create a test agent instance."""
        return OpenAIDeepResearchAgent()
    
    @pytest.fixture
    def mock_research_service(self):
        """Mock the research service."""
        with patch('app.agents.openai_deep_research_agent.openai_deep_research_service') as mock:
            yield mock
    
    def test_agent_initialization(self, agent):
        """Test that the agent initializes correctly."""
        assert agent.name == "OpenAI Deep Research Agent"
        assert "deep research" in agent.description.lower()
        assert "o3-deep-research" in agent.instructions
        assert "o4-mini-deep-research" in agent.instructions
    
    def test_extract_research_parameters(self, agent):
        """Test parameter extraction from messages."""
        # Test mini model preference
        params = agent._extract_research_parameters("Quick research on AI trends")
        assert params.get("prefer_mini") is True
        
        # Test synchronous execution
        params = agent._extract_research_parameters("I need immediate results")
        assert params.get("background") is False
        
        # Test tool preferences
        params = agent._extract_research_parameters("Research without code analysis")
        assert params.get("include_code_interpreter") is False
        
        # Test max tool calls
        params = agent._extract_research_parameters("Research with maximum 5 tool calls")
        assert params.get("max_tool_calls") == 5
    
    def test_select_research_model(self, agent):
        """Test model selection logic."""
        # Test mini model for simple requests
        model = agent._select_research_model("What is AI?", {"prefer_mini": True})
        assert model == DeepResearchModel.O4_MINI_DEEP_RESEARCH.value
        
        # Test full model for complex requests
        model = agent._select_research_model(
            "Conduct comprehensive analysis of AI impact on healthcare industry",
            {}
        )
        assert model == DeepResearchModel.O3_DEEP_RESEARCH.value
    
    def test_enhance_research_query(self, agent):
        """Test query enhancement with context."""
        context = {
            "conversation_history": [
                {"role": "user", "content": "I'm interested in healthcare technology"},
                {"role": "assistant", "content": "I can help with that"}
            ]
        }
        
        enhanced = agent._enhance_research_query("Research AI in healthcare", context)
        assert "healthcare technology" in enhanced
        assert "Research AI in healthcare" in enhanced
    
    @pytest.mark.asyncio
    async def test_process_message_success(self, agent, mock_research_service):
        """Test successful message processing."""
        # Mock successful research result
        mock_result = {
            "success": True,
            "output_text": "Research completed successfully",
            "citations": [{"url": "https://example.com", "title": "Test Source"}],
            "tool_calls": [{"type": "web_search_call", "status": "completed"}],
            "model": "o3-deep-research"
        }
        mock_research_service.conduct_deep_research.return_value = mock_result
        
        result = await agent.process_message("Research AI trends")
        
        assert "Research completed successfully" in result
        assert "Sources and Citations" in result
        assert "Research Methodology" in result
    
    @pytest.mark.asyncio
    async def test_process_message_error(self, agent, mock_research_service):
        """Test error handling in message processing."""
        # Mock research service error
        mock_research_service.conduct_deep_research.side_effect = Exception("API Error")
        
        result = await agent.process_message("Research AI trends")
        
        assert "error" in result.lower()
        assert "API Error" in result


class TestOpenAIDeepResearchService:
    """Test cases for the OpenAI Deep Research Service."""
    
    @pytest.fixture
    def mock_openai_client(self):
        """Mock the OpenAI client."""
        with patch('app.services.openai_deep_research_service.OpenAI') as mock:
            yield mock
    
    def test_service_initialization_success(self, mock_openai_client):
        """Test successful service initialization."""
        with patch('app.services.openai_deep_research_service.settings') as mock_settings:
            mock_settings.OPENAI_API_KEY = "test-key"
            service = OpenAIDeepResearchService()
            assert service.default_model == "o3-deep-research"
            assert service.mini_model == "o4-mini-deep-research"
    
    def test_service_initialization_no_key(self, mock_openai_client):
        """Test service initialization without API key."""
        with patch('app.services.openai_deep_research_service.settings') as mock_settings:
            mock_settings.OPENAI_API_KEY = None
            with pytest.raises(ValueError, match="OPENAI_API_KEY is required"):
                OpenAIDeepResearchService()
    
    def test_prepare_research_input(self, mock_openai_client):
        """Test research input preparation."""
        with patch('app.services.openai_deep_research_service.settings') as mock_settings:
            mock_settings.OPENAI_API_KEY = "test-key"
            service = OpenAIDeepResearchService()
            
            input_text = service._prepare_research_input(
                "Test query",
                "Additional instructions"
            )
            
            assert "Test query" in input_text
            assert "Additional instructions" in input_text
            assert "comprehensive research" in input_text.lower()
    
    def test_prepare_tools(self, mock_openai_client):
        """Test tools preparation."""
        with patch('app.services.openai_deep_research_service.settings') as mock_settings:
            mock_settings.OPENAI_API_KEY = "test-key"
            service = OpenAIDeepResearchService()
            
            tools = service._prepare_tools(
                include_web_search=True,
                include_code_interpreter=True
            )
            
            assert len(tools) == 2
            assert any(tool["type"] == "web_search_preview" for tool in tools)
            assert any(tool["type"] == "code_interpreter" for tool in tools)
    
    @pytest.mark.asyncio
    async def test_conduct_deep_research_sync(self, mock_openai_client):
        """Test synchronous deep research."""
        with patch('app.services.openai_deep_research_service.settings') as mock_settings:
            mock_settings.OPENAI_API_KEY = "test-key"
            
            # Mock OpenAI response
            mock_response = Mock()
            mock_response.output_text = "Test research result"
            mock_response.id = "test-response-id"
            mock_response.model = "o3-deep-research"
            
            mock_client_instance = Mock()
            mock_client_instance.responses.create.return_value = mock_response
            mock_openai_client.return_value = mock_client_instance
            
            service = OpenAIDeepResearchService()
            
            result = await service.conduct_deep_research(
                research_query="Test query",
                background=False
            )
            
            assert result["success"] is True
            assert result["output_text"] == "Test research result"
            assert result["response_id"] == "test-response-id"


class TestDeepResearchIntegration:
    """Integration tests for the complete deep research system."""
    
    @pytest.mark.asyncio
    async def test_end_to_end_research_flow(self):
        """Test the complete research flow from request to response."""
        # This would be a more comprehensive integration test
        # that tests the entire flow including database operations
        pass
    
    def test_research_request_validation(self):
        """Test request model validation."""
        # Valid request
        request = DeepResearchRequest(
            query="Test research query",
            model=DeepResearchModel.O3_DEEP_RESEARCH
        )
        assert request.query == "Test research query"
        assert request.model == DeepResearchModel.O3_DEEP_RESEARCH
        
        # Test defaults
        assert request.background is True
        assert request.include_web_search is True
        assert request.include_code_interpreter is True
    
    def test_model_enum_values(self):
        """Test that model enum has correct values."""
        assert DeepResearchModel.O3_DEEP_RESEARCH.value == "o3-deep-research"
        assert DeepResearchModel.O4_MINI_DEEP_RESEARCH.value == "o4-mini-deep-research"


# Fixtures for database testing (if needed)
@pytest.fixture
async def db_session():
    """Create a test database session."""
    # This would set up a test database session
    # Implementation depends on your test database setup
    pass


# Mock data for testing
@pytest.fixture
def sample_research_result():
    """Sample research result for testing."""
    return {
        "success": True,
        "response_id": "test-response-123",
        "model": "o3-deep-research",
        "output_text": "This is a comprehensive research result about AI trends...",
        "citations": [
            {
                "url": "https://example.com/ai-trends",
                "title": "AI Trends 2024",
                "start_index": 100,
                "end_index": 150
            }
        ],
        "tool_calls": [
            {
                "type": "web_search_call",
                "id": "ws_123",
                "status": "completed",
                "action": {"type": "search", "query": "AI trends"}
            }
        ],
        "reasoning_summary": "Conducted comprehensive web search and analysis",
        "metadata": {
            "created_at": "2024-01-01T00:00:00Z",
            "tool_calls_count": 1
        }
    }


if __name__ == "__main__":
    pytest.main([__file__])
