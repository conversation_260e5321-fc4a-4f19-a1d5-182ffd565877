#!/usr/bin/env python3
"""
Test script for MCP implementation.

This script tests the MCP server and client functionality for research
and calendar agents to ensure everything is working correctly.
"""

import asyncio
import json
import sys
import os
from typing import Dict, Any

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.mcp_server.client import MCPClient
from app.mcp_server.integration import MCPAgentIntegration
from app.utils.logger import get_logger

logger = get_logger(__name__)


async def test_mcp_server_health():
    """Test MCP server health check."""
    print("🔍 Testing MCP server health...")
    
    try:
        async with MCPClient() as client:
            healthy = await client.health_check()
            if healthy:
                print("✅ MCP server is healthy")
                return True
            else:
                print("❌ MCP server health check failed")
                return False
    except Exception as e:
        print(f"❌ Error checking MCP server health: {e}")
        return False


async def test_server_info():
    """Test getting server information."""
    print("\n📋 Testing server info...")
    
    try:
        async with MC<PERSON><PERSON>() as client:
            info = await client.get_server_info()
            if "error" not in info:
                print("✅ Server info retrieved successfully")
                print(f"   Server: {info.get('name', 'Unknown')}")
                print(f"   Version: {info.get('version', 'Unknown')}")
                return True
            else:
                print(f"❌ Error getting server info: {info['error']}")
                return False
    except Exception as e:
        print(f"❌ Error getting server info: {e}")
        return False


async def test_list_tools():
    """Test listing available tools."""
    print("\n🔧 Testing tool listing...")
    
    try:
        async with MCPClient() as client:
            tools = await client.list_tools()
            if tools:
                print(f"✅ Found {len(tools)} tools:")
                for tool in tools:
                    print(f"   - {tool.get('name', 'Unknown')}")
                return True
            else:
                print("❌ No tools found")
                return False
    except Exception as e:
        print(f"❌ Error listing tools: {e}")
        return False


async def test_list_resources():
    """Test listing available resources."""
    print("\n📚 Testing resource listing...")
    
    try:
        async with MCPClient() as client:
            resources = await client.list_resources()
            if resources:
                print(f"✅ Found {len(resources)} resources:")
                for resource in resources:
                    print(f"   - {resource.get('name', 'Unknown')}")
                return True
            else:
                print("❌ No resources found")
                return False
    except Exception as e:
        print(f"❌ Error listing resources: {e}")
        return False


async def test_research_tools():
    """Test research agent tools."""
    print("\n🔬 Testing research agent tools...")
    
    test_org_id = "test-org-123"
    test_user_id = "test-user-456"
    
    try:
        async with MCPClient() as client:
            # Test competitor analysis
            print("   Testing competitor analysis...")
            result = await client.competitor_analysis(
                competitor_name="Test Competitor",
                organization_id=test_org_id,
                user_id=test_user_id,
                keywords=["technology", "innovation"],
                time_period="1w"
            )
            
            if "error" not in result:
                print("   ✅ Competitor analysis completed")
            else:
                print(f"   ❌ Competitor analysis failed: {result['error']}")
            
            # Test trend analysis
            print("   Testing trend analysis...")
            result = await client.trend_analysis(
                topic="AI Technology",
                organization_id=test_org_id,
                user_id=test_user_id,
                keywords=["artificial intelligence", "machine learning"],
                time_period="1m"
            )
            
            if "error" not in result:
                print("   ✅ Trend analysis completed")
            else:
                print(f"   ❌ Trend analysis failed: {result['error']}")
            
            # Test market research
            print("   Testing market research...")
            result = await client.market_research(
                market_segment="Technology",
                organization_id=test_org_id,
                user_id=test_user_id,
                keywords=["market", "growth"],
                region="North America"
            )
            
            if "error" not in result:
                print("   ✅ Market research completed")
                return True
            else:
                print(f"   ❌ Market research failed: {result['error']}")
                return False
                
    except Exception as e:
        print(f"❌ Error testing research tools: {e}")
        return False


async def test_calendar_tools():
    """Test calendar agent tools."""
    print("\n📅 Testing calendar agent tools...")
    
    test_org_id = "test-org-123"
    test_user_id = "test-user-456"
    test_auth_token = "test-token-789"
    
    try:
        async with MCPClient() as client:
            # Test content planning
            print("   Testing content planning...")
            result = await client.content_planning(
                topic="Product Launch",
                organization_id=test_org_id,
                user_id=test_user_id,
                auth_token=test_auth_token,
                platforms=["twitter", "linkedin"],
                content_types=["post", "article"],
                time_period="1w"
            )
            
            if "error" not in result:
                print("   ✅ Content planning completed")
            else:
                print(f"   ❌ Content planning failed: {result['error']}")
            
            # Test content scheduling
            print("   Testing content scheduling...")
            result = await client.content_scheduling(
                content="Exciting news about our new product! 🚀",
                platform="twitter",
                organization_id=test_org_id,
                user_id=test_user_id,
                auth_token=test_auth_token,
                content_type="post",
                auto_optimize_timing=True
            )
            
            if "error" not in result:
                print("   ✅ Content scheduling completed")
                return True
            else:
                print(f"   ❌ Content scheduling failed: {result['error']}")
                return False
                
    except Exception as e:
        print(f"❌ Error testing calendar tools: {e}")
        return False


async def test_resources():
    """Test resource access."""
    print("\n📖 Testing resource access...")
    
    test_org_id = "test-org-123"
    
    try:
        async with MCPClient() as client:
            # Test research reports
            print("   Testing research reports resource...")
            result = await client.get_research_reports(test_org_id)
            
            if "error" not in result:
                print("   ✅ Research reports accessed")
            else:
                print(f"   ❌ Research reports failed: {result['error']}")
            
            # Test content calendar
            print("   Testing content calendar resource...")
            result = await client.get_content_calendar(test_org_id)
            
            if "error" not in result:
                print("   ✅ Content calendar accessed")
            else:
                print(f"   ❌ Content calendar failed: {result['error']}")
            
            # Test optimal timing
            print("   Testing optimal timing resource...")
            result = await client.get_optimal_timing(test_org_id, "twitter")
            
            if "error" not in result:
                print("   ✅ Optimal timing accessed")
                return True
            else:
                print(f"   ❌ Optimal timing failed: {result['error']}")
                return False
                
    except Exception as e:
        print(f"❌ Error testing resources: {e}")
        return False


async def test_integration():
    """Test MCP integration functionality."""
    print("\n🔗 Testing MCP integration...")
    
    try:
        integration = MCPAgentIntegration()
        await integration.initialize()
        
        # Test agent capabilities
        capabilities = await integration.get_agent_capabilities("deep_research_agent")
        if "error" not in capabilities:
            print("   ✅ Research agent capabilities retrieved")
            print(f"      Tools: {len(capabilities.get('mcp_tools', []))}")
            print(f"      Resources: {len(capabilities.get('mcp_resources', []))}")
        else:
            print(f"   ❌ Error getting capabilities: {capabilities['error']}")
        
        capabilities = await integration.get_agent_capabilities("content_calendar_agent")
        if "error" not in capabilities:
            print("   ✅ Calendar agent capabilities retrieved")
            print(f"      Tools: {len(capabilities.get('mcp_tools', []))}")
            print(f"      Resources: {len(capabilities.get('mcp_resources', []))}")
            return True
        else:
            print(f"   ❌ Error getting capabilities: {capabilities['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing integration: {e}")
        return False


async def run_all_tests():
    """Run all MCP tests."""
    print("🚀 Starting MCP Implementation Tests")
    print("=" * 50)
    
    tests = [
        ("Server Health", test_mcp_server_health),
        ("Server Info", test_server_info),
        ("List Tools", test_list_tools),
        ("List Resources", test_list_resources),
        ("Research Tools", test_research_tools),
        ("Calendar Tools", test_calendar_tools),
        ("Resources", test_resources),
        ("Integration", test_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! MCP implementation is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    # Run the tests
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)
