# MCP Implementation for EllumAI Agents

This document describes the Model Context Protocol (MCP) implementation for the EllumAI research and calendar agents, enabling them to be used as MCP tools by external clients.

## Overview

The MCP implementation provides:
- **Research Agent Tools**: Competitor analysis, trend analysis, market research, data aggregation, and general research
- **Calendar Agent Tools**: Content planning, scheduling, optimal timing analysis, content suggestions, and calendar management
- **Resource Access**: Structured access to research data, calendar data, and insights
- **REST API**: HTTP endpoints for easy integration
- **Client Library**: Python client for programmatic access

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MCP Client    │    │   REST API      │    │   MCP Server    │
│                 │◄──►│                 │◄──►│                 │
│ - Python SDK    │    │ - HTTP Routes   │    │ - FastMCP       │
│ - HTTP Client   │    │ - Validation    │    │ - Tool Registry │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │ Agent Framework │
                                               │                 │
                                               │ - Research      │
                                               │ - Calendar      │
                                               │ - Integration   │
                                               └─────────────────┘
```

## Installation and Setup

### 1. Dependencies

The MCP implementation requires FastMCP, which has been added to the requirements:

```bash
# Already added to requirements.txt
fastmcp
```

### 2. Configuration

Add MCP configuration to your `.env` file:

```bash
# MCP Server Configuration
MCP_ENABLED=true
MCP_HOST=localhost
MCP_PORT=8008
MCP_AUTO_START=true
```

### 3. Service Startup

The MCP server starts automatically with the Deep Ellum service when `MCP_ENABLED=true` and `MCP_AUTO_START=true`.

## Available Tools

### Research Agent Tools

#### 1. Competitor Analysis
- **Tool Name**: `competitor_analysis`
- **Description**: Analyze competitors using the research agent
- **Parameters**:
  - `competitor_name` (string): Name of competitor to analyze
  - `organization_id` (string): Organization ID for context
  - `user_id` (string): User ID for context
  - `keywords` (array): Keywords for analysis
  - `time_period` (string): Time period (e.g., "1w", "1m")
  - `region` (string): Geographic region
  - `focus_areas` (array): Specific focus areas

#### 2. Trend Analysis
- **Tool Name**: `trend_analysis`
- **Description**: Analyze market trends
- **Parameters**:
  - `topic` (string): Topic to analyze trends for
  - `organization_id` (string): Organization ID
  - `user_id` (string): User ID
  - `keywords` (array): Keywords for analysis
  - `time_period` (string): Time period
  - `region` (string): Geographic region
  - `sources` (array): Specific sources to analyze

#### 3. Market Research
- **Tool Name**: `market_research`
- **Description**: Perform comprehensive market research
- **Parameters**:
  - `market_segment` (string): Market segment to research
  - `organization_id` (string): Organization ID
  - `user_id` (string): User ID
  - `keywords` (array): Keywords for research
  - `region` (string): Geographic region
  - `focus_areas` (array): Specific focus areas

#### 4. Data Aggregation
- **Tool Name**: `data_aggregation`
- **Description**: Aggregate data from multiple sources
- **Parameters**:
  - `data_sources` (array): Data sources to aggregate from
  - `query` (string): Query for data aggregation
  - `organization_id` (string): Organization ID
  - `user_id` (string): User ID
  - `keywords` (array): Keywords for filtering
  - `time_period` (string): Time period

#### 5. General Research
- **Tool Name**: `general_research`
- **Description**: Perform general research queries
- **Parameters**:
  - `query` (string): Research query
  - `organization_id` (string): Organization ID
  - `user_id` (string): User ID
  - `keywords` (array): Keywords
  - `sources` (array): Specific sources

### Calendar Agent Tools

#### 1. Content Planning
- **Tool Name**: `content_planning`
- **Description**: Plan content using the calendar agent
- **Parameters**:
  - `topic` (string): Topic for content planning
  - `organization_id` (string): Organization ID
  - `user_id` (string): User ID
  - `auth_token` (string): Authentication token
  - `platforms` (array): Social media platforms
  - `content_types` (array): Types of content
  - `time_period` (string): Planning time period
  - `target_audience` (string): Target audience

#### 2. Content Scheduling
- **Tool Name**: `content_scheduling`
- **Description**: Schedule content across platforms
- **Parameters**:
  - `content` (string): Content to schedule
  - `platform` (string): Social media platform
  - `organization_id` (string): Organization ID
  - `user_id` (string): User ID
  - `auth_token` (string): Authentication token
  - `scheduled_time` (string): Scheduled time (ISO format)
  - `content_type` (string): Type of content
  - `auto_optimize_timing` (boolean): Auto-optimize timing

#### 3. Optimal Timing Analysis
- **Tool Name**: `optimal_timing_analysis`
- **Description**: Analyze optimal posting times
- **Parameters**:
  - `platform` (string): Social media platform
  - `organization_id` (string): Organization ID
  - `user_id` (string): User ID
  - `auth_token` (string): Authentication token
  - `content_type` (string): Type of content
  - `target_audience` (string): Target audience
  - `timezone` (string): Target timezone

#### 4. Content Suggestions
- **Tool Name**: `content_suggestions`
- **Description**: Get AI-powered content suggestions
- **Parameters**:
  - `topic` (string): Topic for suggestions
  - `organization_id` (string): Organization ID
  - `user_id` (string): User ID
  - `auth_token` (string): Authentication token
  - `platforms` (array): Social media platforms
  - `content_types` (array): Types of content
  - `use_knowledgebase` (boolean): Use organization knowledge
  - `use_social_history` (boolean): Use social media history

#### 5. Calendar View
- **Tool Name**: `calendar_view`
- **Description**: Get calendar view and scheduled content
- **Parameters**:
  - `start_date` (string): Start date (ISO format)
  - `end_date` (string): End date (ISO format)
  - `organization_id` (string): Organization ID
  - `user_id` (string): User ID
  - `auth_token` (string): Authentication token
  - `platforms` (array): Filter by platforms
  - `content_types` (array): Filter by content types

## Available Resources

### Research Resources

#### 1. Research Reports
- **URI Template**: `research://reports/{organization_id}/{report_type?}`
- **Description**: Access to research reports and analysis results

#### 2. Competitor Data
- **URI Template**: `research://competitors/{organization_id}/{competitor_name?}`
- **Description**: Access to competitor analysis data and insights

#### 3. Trend Data
- **URI Template**: `research://trends/{organization_id}/{time_period?}`
- **Description**: Access to trend analysis data and market insights

#### 4. Market Data
- **URI Template**: `research://market/{organization_id}/{market_segment?}`
- **Description**: Access to market research data and analysis

#### 5. Research Templates
- **URI Template**: `research://templates/{template_type?}`
- **Description**: Access to research templates and methodologies

### Calendar Resources

#### 1. Content Calendar
- **URI Template**: `calendar://content/{organization_id}/{date_range?}`
- **Description**: Access to content calendar data and schedules

#### 2. Scheduled Content
- **URI Template**: `calendar://scheduled/{organization_id}/{platform?}`
- **Description**: Access to scheduled content details and status

#### 3. Optimal Timing
- **URI Template**: `calendar://timing/{organization_id}/{platform?}`
- **Description**: Access to optimal timing data and recommendations

#### 4. Content Templates
- **URI Template**: `calendar://templates/{template_type?}`
- **Description**: Access to content templates and formats

#### 5. Social Insights
- **URI Template**: `calendar://insights/{organization_id}/{platform?}`
- **Description**: Access to social media insights and analytics

## Usage Examples

### Python Client

```python
from app.mcp.client import MCPClient

async def example_usage():
    async with MCPClient() as client:
        # Research example
        result = await client.competitor_analysis(
            competitor_name="Apple Inc.",
            organization_id="org-123",
            user_id="user-456",
            keywords=["innovation", "technology"],
            time_period="1m"
        )
        
        # Calendar example
        result = await client.content_planning(
            topic="Product Launch",
            organization_id="org-123",
            user_id="user-456",
            auth_token="token-789",
            platforms=["twitter", "linkedin"],
            time_period="2w"
        )
        
        # Resource access
        reports = await client.get_research_reports("org-123")
        calendar = await client.get_content_calendar("org-123")
```

### REST API

```bash
# List available tools
curl http://localhost:8007/api/v1/mcp/tools

# Call a research tool
curl -X POST http://localhost:8007/api/v1/mcp/research/competitor-analysis \
  -H "Content-Type: application/json" \
  -d '{
    "competitor_name": "Apple Inc.",
    "organization_id": "org-123",
    "user_id": "user-456",
    "keywords": ["innovation", "technology"],
    "time_period": "1m"
  }'

# Call a calendar tool
curl -X POST http://localhost:8007/api/v1/mcp/calendar/content-planning \
  -H "Content-Type: application/json" \
  -d '{
    "topic": "Product Launch",
    "organization_id": "org-123",
    "user_id": "user-456",
    "auth_token": "token-789",
    "platforms": ["twitter", "linkedin"],
    "time_period": "2w"
  }'

# Access resources
curl http://localhost:8007/api/v1/mcp/research/reports/org-123
curl http://localhost:8007/api/v1/mcp/calendar/content/org-123
```

## Testing

Run the test script to verify the MCP implementation:

```bash
cd microservices/deep_ellum
python test_mcp.py
```

The test script will verify:
- Server health and connectivity
- Tool availability and functionality
- Resource access
- Integration with existing agents

## API Endpoints

### MCP Management
- `GET /api/v1/mcp/status` - Get MCP server status
- `GET /api/v1/mcp/tools` - List available tools
- `GET /api/v1/mcp/resources` - List available resources
- `POST /api/v1/mcp/tools/call` - Call an MCP tool
- `POST /api/v1/mcp/resources/get` - Get an MCP resource

### Research Endpoints
- `POST /api/v1/mcp/research/competitor-analysis` - Competitor analysis
- `POST /api/v1/mcp/research/trend-analysis` - Trend analysis
- `GET /api/v1/mcp/research/reports/{org_id}` - Get research reports

### Calendar Endpoints
- `POST /api/v1/mcp/calendar/content-planning` - Content planning
- `POST /api/v1/mcp/calendar/content-scheduling` - Content scheduling
- `GET /api/v1/mcp/calendar/content/{org_id}` - Get content calendar

## Configuration Options

| Variable | Default | Description |
|----------|---------|-------------|
| `MCP_ENABLED` | `true` | Enable/disable MCP functionality |
| `MCP_HOST` | `localhost` | MCP server host |
| `MCP_PORT` | `8008` | MCP server port |
| `MCP_AUTO_START` | `true` | Auto-start MCP server with service |

## Security Considerations

- Authentication tokens are required for calendar agent operations
- Organization ID isolation ensures data privacy
- All requests are validated and sanitized
- Error handling prevents information leakage

## Troubleshooting

### Common Issues

1. **MCP Server Not Starting**
   - Check if port 8008 is available
   - Verify FastMCP is installed
   - Check logs for startup errors

2. **Tool Calls Failing**
   - Verify agent instances are available
   - Check parameter validation
   - Ensure required services are running

3. **Resource Access Issues**
   - Verify organization ID format
   - Check resource URI syntax
   - Ensure proper permissions

### Logs

MCP-related logs are available in the Deep Ellum service logs:
```bash
# View logs
docker logs deep_ellum_service

# Filter MCP logs
docker logs deep_ellum_service 2>&1 | grep MCP
```

## Future Enhancements

- Custom MCP server integration for private data
- Advanced authentication and authorization
- Real-time notifications and webhooks
- Performance monitoring and metrics
- Multi-tenant resource isolation
