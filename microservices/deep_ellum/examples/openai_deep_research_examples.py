"""
Examples of using the OpenAI Deep Research Agent.
"""

import asyncio
import httpx
import json
from typing import Dict, Any

# Configuration
BASE_URL = "http://localhost:8003"
API_TOKEN = "your_api_token_here"  # Replace with actual token

class DeepResearchClient:
    """Client for interacting with the OpenAI Deep Research Agent."""
    
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url
        self.headers = {"Authorization": f"Bearer {token}"}
    
    async def conduct_research(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Conduct deep research using the API."""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/deep-research",
                json=request_data,
                headers=self.headers,
                timeout=3600  # 1 hour timeout
            )
            response.raise_for_status()
            return response.json()
    
    async def check_status(self, response_id: str) -> Dict[str, Any]:
        """Check the status of a research task."""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/api/v1/deep-research/status/{response_id}",
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()
    
    async def research_with_agent(self, query: str, model: str = "o3-deep-research") -> Dict[str, Any]:
        """Use the agent interface for research."""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/deep-research/agent",
                params={"query": query, "model": model},
                headers=self.headers,
                timeout=3600
            )
            response.raise_for_status()
            return response.json()
    
    async def get_models(self) -> Dict[str, Any]:
        """Get available research models."""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/api/v1/deep-research/models",
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()
    
    async def get_examples(self) -> Dict[str, Any]:
        """Get research examples and best practices."""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/api/v1/deep-research/examples",
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()


async def example_comprehensive_market_research():
    """Example: Comprehensive market research using o3-deep-research."""
    client = DeepResearchClient(BASE_URL, API_TOKEN)
    
    print("🔬 Example 1: Comprehensive Market Research")
    print("=" * 50)
    
    request = {
        "query": """Research the economic impact of semaglutide on global healthcare systems.
        
        Requirements:
        - Include specific figures, trends, statistics, and measurable outcomes
        - Prioritize reliable, up-to-date sources: peer-reviewed research, health organizations (WHO, CDC), regulatory agencies, pharmaceutical earnings reports
        - Include inline citations and return all source metadata
        - Focus on cost-effectiveness, market adoption, and healthcare system impacts
        
        Be analytical, avoid generalities, and ensure each section supports data-backed reasoning that could inform healthcare policy or financial modeling.""",
        "model": "o3-deep-research",
        "background": True,
        "max_tool_calls": 75,
        "include_code_interpreter": True,
        "include_web_search": True,
        "instructions": "Focus on quantitative data and peer-reviewed sources. Include economic modeling if relevant data is available."
    }
    
    try:
        result = await client.conduct_research(request)
        print(f"✅ Research initiated successfully!")
        print(f"📋 Response ID: {result.get('response_id')}")
        print(f"🤖 Model: {result.get('model')}")
        
        if result.get('background'):
            print("⏳ Research running in background mode...")
            # In a real application, you would poll for status or use webhooks
        else:
            print("📊 Research Results:")
            print(result.get('output_text', '')[:500] + "...")
            
    except Exception as e:
        print(f"❌ Error: {e}")


async def example_quick_competitive_analysis():
    """Example: Quick competitive analysis using o4-mini-deep-research."""
    client = DeepResearchClient(BASE_URL, API_TOKEN)
    
    print("\n🚀 Example 2: Quick Competitive Analysis")
    print("=" * 50)
    
    try:
        result = await client.research_with_agent(
            query="Quick analysis of Tesla's competitive positioning in the electric vehicle market. Focus on 2024 market share, production capacity, and key strategic moves.",
            model="o4-mini-deep-research"
        )
        
        print("✅ Quick research completed!")
        print("📊 Results:")
        print(result.get('result', '')[:800] + "...")
        
    except Exception as e:
        print(f"❌ Error: {e}")


async def example_legal_research():
    """Example: Legal research with specific requirements."""
    client = DeepResearchClient(BASE_URL, API_TOKEN)
    
    print("\n⚖️ Example 3: Legal Research")
    print("=" * 50)
    
    request = {
        "query": """Conduct comprehensive legal research on recent AI regulation developments affecting healthcare applications.
        
        Scope:
        - EU AI Act implications for medical AI
        - FDA guidance on AI/ML-based medical devices
        - GDPR compliance for healthcare AI systems
        - Recent court cases or regulatory actions
        
        Focus on:
        - Compliance requirements for healthcare organizations
        - Risk assessment frameworks
        - Data protection obligations
        - Certification and approval processes
        
        Prioritize official regulatory sources, legal databases, and recent policy documents.""",
        "model": "o3-deep-research",
        "background": False,  # Synchronous for demonstration
        "max_tool_calls": 50,
        "include_code_interpreter": False,  # Legal research doesn't need code
        "include_web_search": True,
        "instructions": "Focus on official regulatory sources and recent developments. Include specific compliance requirements and deadlines."
    }
    
    try:
        result = await client.conduct_research(request)
        print("✅ Legal research completed!")
        print(f"📄 Citations found: {len(result.get('citations', []))}")
        print("📊 Key findings:")
        print(result.get('output_text', '')[:600] + "...")
        
    except Exception as e:
        print(f"❌ Error: {e}")


async def example_technology_trends():
    """Example: Technology trend analysis."""
    client = DeepResearchClient(BASE_URL, API_TOKEN)
    
    print("\n🔮 Example 4: Technology Trends Analysis")
    print("=" * 50)
    
    try:
        result = await client.research_with_agent(
            query="""Analyze emerging trends in quantum computing applications for 2024-2025.
            
            Focus areas:
            - Commercial applications and use cases
            - Key players and investments
            - Technical breakthroughs and milestones
            - Market size and growth projections
            - Challenges and limitations
            
            Include both current developments and future outlook.""",
            model="o3-deep-research"
        )
        
        print("✅ Trend analysis completed!")
        print("🔍 Analysis:")
        print(result.get('result', '')[:700] + "...")
        
    except Exception as e:
        print(f"❌ Error: {e}")


async def example_status_checking():
    """Example: Checking research status for background tasks."""
    client = DeepResearchClient(BASE_URL, API_TOKEN)
    
    print("\n📊 Example 5: Status Checking")
    print("=" * 50)
    
    # Start a background research task
    request = {
        "query": "Research the impact of AI on software development productivity",
        "model": "o4-mini-deep-research",
        "background": True
    }
    
    try:
        result = await client.conduct_research(request)
        response_id = result.get('response_id')
        
        print(f"✅ Background research started: {response_id}")
        
        # Check status
        status = await client.check_status(response_id)
        print(f"📋 Current status: {status.get('status')}")
        print(f"⏰ Created at: {status.get('created_at')}")
        
        # In a real application, you would poll until completion
        print("💡 In production, poll this endpoint until status is 'completed'")
        
    except Exception as e:
        print(f"❌ Error: {e}")


async def example_model_information():
    """Example: Getting model information and capabilities."""
    client = DeepResearchClient(BASE_URL, API_TOKEN)
    
    print("\n🤖 Example 6: Model Information")
    print("=" * 50)
    
    try:
        models = await client.get_models()
        print("Available models:")
        
        for model in models.get('models', []):
            print(f"\n📋 {model['name']}")
            print(f"   Description: {model['description']}")
            print(f"   Best for: {model['recommended_for']}")
            print(f"   Use cases: {', '.join(model['use_cases'][:3])}...")
        
        print(f"\n🛠️ Available tools: {len(models.get('tools', []))}")
        
    except Exception as e:
        print(f"❌ Error: {e}")


async def example_best_practices():
    """Example: Getting research examples and best practices."""
    client = DeepResearchClient(BASE_URL, API_TOKEN)
    
    print("\n💡 Example 7: Best Practices and Examples")
    print("=" * 50)
    
    try:
        examples = await client.get_examples()
        
        print("Research examples by category:")
        for example in examples.get('examples', [])[:3]:
            print(f"\n📂 {example['category']}")
            print(f"   Query: {example['query'][:80]}...")
            print(f"   Model: {example['recommended_model']}")
        
        print(f"\n✨ Best practices:")
        for practice in examples.get('best_practices', [])[:3]:
            print(f"   • {practice}")
        
    except Exception as e:
        print(f"❌ Error: {e}")


async def main():
    """Run all examples."""
    print("🧪 OpenAI Deep Research Agent Examples")
    print("=" * 60)
    print("Note: Make sure to set your API token in the script!")
    print()
    
    # Check if token is set
    if API_TOKEN == "your_api_token_here":
        print("⚠️ Please set your API token in the script before running examples")
        return
    
    try:
        # Run examples
        await example_model_information()
        await example_best_practices()
        await example_quick_competitive_analysis()
        await example_status_checking()
        
        # Uncomment these for full examples (they take longer)
        # await example_comprehensive_market_research()
        # await example_legal_research()
        # await example_technology_trends()
        
        print("\n✅ All examples completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Example execution failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
