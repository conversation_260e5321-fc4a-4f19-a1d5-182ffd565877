#!/usr/bin/env python3
"""
MCP Usage Example for EllumAI Agents

This example demonstrates how to use the MCP implementation to interact
with research and calendar agents through the Model Context Protocol.
"""

import asyncio
import json
import sys
import os
from datetime import datetime, timedelta

# Add the parent directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.mcp_server.client import MCPClient


async def research_example():
    """Example of using research agent tools through MCP."""
    print("🔬 Research Agent Example")
    print("-" * 40)
    
    # Example organization and user IDs
    org_id = "example-org-123"
    user_id = "example-user-456"
    
    async with MCPClient() as client:
        # 1. Competitor Analysis
        print("1. Performing competitor analysis...")
        result = await client.competitor_analysis(
            competitor_name="Tesla",
            organization_id=org_id,
            user_id=user_id,
            keywords=["electric vehicles", "autonomous driving", "battery technology"],
            time_period="3m",
            region="North America",
            focus_areas=["market share", "innovation", "partnerships"]
        )
        
        if "error" not in result:
            print("   ✅ Competitor analysis completed")
            print(f"   📊 Analysis type: {result.get('tool', 'Unknown')}")
        else:
            print(f"   ❌ Error: {result['error']}")
        
        # 2. Trend Analysis
        print("\n2. Analyzing market trends...")
        result = await client.trend_analysis(
            topic="Artificial Intelligence in Healthcare",
            organization_id=org_id,
            user_id=user_id,
            keywords=["AI", "healthcare", "medical devices", "diagnostics"],
            time_period="6m",
            region="Global",
            sources=["industry reports", "academic papers", "news articles"]
        )
        
        if "error" not in result:
            print("   ✅ Trend analysis completed")
            print(f"   📈 Analysis type: {result.get('tool', 'Unknown')}")
        else:
            print(f"   ❌ Error: {result['error']}")
        
        # 3. Market Research
        print("\n3. Conducting market research...")
        result = await client.market_research(
            market_segment="Cloud Computing Services",
            organization_id=org_id,
            user_id=user_id,
            keywords=["cloud", "SaaS", "infrastructure", "enterprise"],
            region="United States",
            focus_areas=["market size", "growth rate", "key players"]
        )
        
        if "error" not in result:
            print("   ✅ Market research completed")
            print(f"   💼 Analysis type: {result.get('tool', 'Unknown')}")
        else:
            print(f"   ❌ Error: {result['error']}")
        
        # 4. Access Research Resources
        print("\n4. Accessing research resources...")
        
        # Get research reports
        reports = await client.get_research_reports(org_id, "competitor_analysis")
        if "error" not in reports:
            print("   ✅ Research reports accessed")
            data = json.loads(reports.get('data', '{}'))
            print(f"   📄 Found {data.get('total_count', 0)} reports")
        else:
            print(f"   ❌ Error accessing reports: {reports['error']}")
        
        # Get competitor data
        competitor_data = await client.get_competitor_data(org_id)
        if "error" not in competitor_data:
            print("   ✅ Competitor data accessed")
            data = json.loads(competitor_data.get('data', '{}'))
            print(f"   🏢 Found {len(data.get('competitors', []))} competitors")
        else:
            print(f"   ❌ Error accessing competitor data: {competitor_data['error']}")


async def calendar_example():
    """Example of using calendar agent tools through MCP."""
    print("\n📅 Calendar Agent Example")
    print("-" * 40)
    
    # Example organization, user, and auth token
    org_id = "example-org-123"
    user_id = "example-user-456"
    auth_token = "example-auth-token-789"
    
    async with MCPClient() as client:
        # 1. Content Planning
        print("1. Planning content...")
        result = await client.content_planning(
            topic="New Product Launch Campaign",
            organization_id=org_id,
            user_id=user_id,
            auth_token=auth_token,
            platforms=["twitter", "linkedin", "instagram", "facebook"],
            content_types=["post", "article", "video", "image"],
            time_period="4w",
            target_audience="Tech professionals and early adopters"
        )
        
        if "error" not in result:
            print("   ✅ Content planning completed")
            print(f"   📝 Planning type: {result.get('tool', 'Unknown')}")
        else:
            print(f"   ❌ Error: {result['error']}")
        
        # 2. Content Scheduling
        print("\n2. Scheduling content...")
        
        # Schedule a Twitter post
        scheduled_time = (datetime.now() + timedelta(hours=2)).isoformat() + "Z"
        result = await client.content_scheduling(
            content="🚀 Excited to announce our groundbreaking new product! Revolutionary technology meets user-friendly design. Stay tuned for more details! #Innovation #TechLaunch",
            platform="twitter",
            organization_id=org_id,
            user_id=user_id,
            auth_token=auth_token,
            scheduled_time=scheduled_time,
            content_type="post",
            auto_optimize_timing=False
        )
        
        if "error" not in result:
            print("   ✅ Content scheduled for Twitter")
            print(f"   ⏰ Scheduled for: {scheduled_time}")
        else:
            print(f"   ❌ Error: {result['error']}")
        
        # Schedule a LinkedIn article with optimal timing
        result = await client.content_scheduling(
            content="In-depth analysis of our new product's impact on the industry. This comprehensive article explores the technology, market implications, and future possibilities.",
            platform="linkedin",
            organization_id=org_id,
            user_id=user_id,
            auth_token=auth_token,
            content_type="article",
            auto_optimize_timing=True
        )
        
        if "error" not in result:
            print("   ✅ Content scheduled for LinkedIn with optimal timing")
        else:
            print(f"   ❌ Error: {result['error']}")
        
        # 3. Access Calendar Resources
        print("\n3. Accessing calendar resources...")
        
        # Get content calendar
        start_date = datetime.now().strftime("%Y-%m-%d")
        end_date = (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")
        date_range = f"{start_date}:{end_date}"
        
        calendar_data = await client.get_content_calendar(org_id, date_range)
        if "error" not in calendar_data:
            print("   ✅ Content calendar accessed")
            data = json.loads(calendar_data.get('data', '{}'))
            print(f"   📅 Found {len(data.get('scheduled_content', []))} scheduled items")
        else:
            print(f"   ❌ Error accessing calendar: {calendar_data['error']}")
        
        # Get optimal timing insights
        timing_data = await client.get_optimal_timing(org_id, "twitter")
        if "error" not in timing_data:
            print("   ✅ Optimal timing data accessed")
            data = json.loads(timing_data.get('data', '{}'))
            twitter_timing = data.get('optimal_times', {}).get('twitter', {})
            print(f"   ⏰ Best hours for Twitter: {twitter_timing.get('best_hours', [])}")
        else:
            print(f"   ❌ Error accessing timing data: {timing_data['error']}")


async def integration_example():
    """Example of checking MCP server status and capabilities."""
    print("\n🔗 Integration Example")
    print("-" * 40)
    
    async with MCPClient() as client:
        # 1. Health Check
        print("1. Checking server health...")
        healthy = await client.health_check()
        if healthy:
            print("   ✅ MCP server is healthy and responsive")
        else:
            print("   ❌ MCP server is not responding")
            return
        
        # 2. Server Information
        print("\n2. Getting server information...")
        info = await client.get_server_info()
        if "error" not in info:
            print("   ✅ Server info retrieved")
            print(f"   📋 Server: {info.get('name', 'Unknown')}")
            print(f"   🔢 Version: {info.get('version', 'Unknown')}")
        else:
            print(f"   ❌ Error: {info['error']}")
        
        # 3. Available Tools
        print("\n3. Listing available tools...")
        tools = await client.list_tools()
        if tools:
            print(f"   ✅ Found {len(tools)} available tools:")
            for tool in tools[:5]:  # Show first 5 tools
                print(f"      - {tool.get('name', 'Unknown')}")
            if len(tools) > 5:
                print(f"      ... and {len(tools) - 5} more")
        else:
            print("   ❌ No tools found")
        
        # 4. Available Resources
        print("\n4. Listing available resources...")
        resources = await client.list_resources()
        if resources:
            print(f"   ✅ Found {len(resources)} available resources:")
            for resource in resources[:5]:  # Show first 5 resources
                print(f"      - {resource.get('name', 'Unknown')}")
            if len(resources) > 5:
                print(f"      ... and {len(resources) - 5} more")
        else:
            print("   ❌ No resources found")


async def main():
    """Main example function."""
    print("🚀 EllumAI MCP Usage Examples")
    print("=" * 50)
    
    try:
        # Run all examples
        await research_example()
        await calendar_example()
        await integration_example()
        
        print("\n" + "=" * 50)
        print("✅ All examples completed successfully!")
        print("\n💡 Tips:")
        print("   - Replace example IDs with real organization/user IDs")
        print("   - Use valid authentication tokens for calendar operations")
        print("   - Check the MCP_IMPLEMENTATION.md for detailed documentation")
        print("   - Run test_mcp.py to verify your MCP setup")
        
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
        print("\n🔧 Troubleshooting:")
        print("   - Ensure the Deep Ellum service is running")
        print("   - Check that MCP is enabled in configuration")
        print("   - Verify the MCP server is accessible on the configured port")


if __name__ == "__main__":
    asyncio.run(main())
