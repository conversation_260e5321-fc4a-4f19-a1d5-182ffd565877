"""
Calendar Agent MCP Tools.

This module provides MCP tool implementations for the calendar agent capabilities.
"""

import json
from typing import Dict, Any, List, Optional, Callable
from pydantic import BaseModel, <PERSON>
from datetime import datetime

from fastmcp import FastMCP

from app.utils.logger import get_logger
from app.database.database import get_db
from app.agents.agent_factory import AgentFactory

logger = get_logger(__name__)


class ContentPlanningRequest(BaseModel):
    """Request model for content planning."""
    topic: str = Field(description="Topic or theme for content planning")
    platforms: List[str] = Field(default=[], description="Social media platforms to plan for")
    content_types: List[str] = Field(default=[], description="Types of content (post, video, image, etc.)")
    time_period: str = Field(default="1w", description="Time period for content planning")
    target_audience: str = Field(default="", description="Target audience description")
    organization_id: str = Field(description="Organization ID for context")
    user_id: str = Field(description="User ID for context")
    auth_token: str = Field(description="Authentication token for API access")


class ContentSchedulingRequest(BaseModel):
    """Request model for content scheduling."""
    content: str = Field(description="Content to schedule")
    platform: str = Field(description="Social media platform")
    scheduled_time: Optional[str] = Field(default=None, description="Scheduled time (ISO format)")
    content_type: str = Field(default="post", description="Type of content")
    auto_optimize_timing: bool = Field(default=True, description="Whether to optimize timing automatically")
    organization_id: str = Field(description="Organization ID for context")
    user_id: str = Field(description="User ID for context")
    auth_token: str = Field(description="Authentication token for API access")


class OptimalTimingRequest(BaseModel):
    """Request model for optimal timing analysis."""
    platform: str = Field(description="Social media platform")
    content_type: str = Field(default="post", description="Type of content")
    target_audience: str = Field(default="", description="Target audience description")
    timezone: str = Field(default="UTC", description="Target timezone")
    organization_id: str = Field(description="Organization ID for context")
    user_id: str = Field(description="User ID for context")
    auth_token: str = Field(description="Authentication token for API access")


class ContentSuggestionsRequest(BaseModel):
    """Request model for content suggestions."""
    topic: str = Field(description="Topic or theme for content suggestions")
    platforms: List[str] = Field(default=[], description="Social media platforms")
    content_types: List[str] = Field(default=[], description="Types of content to suggest")
    use_knowledgebase: bool = Field(default=True, description="Whether to use organization knowledgebase")
    use_social_history: bool = Field(default=True, description="Whether to use social media history")
    organization_id: str = Field(description="Organization ID for context")
    user_id: str = Field(description="User ID for context")
    auth_token: str = Field(description="Authentication token for API access")


class CalendarViewRequest(BaseModel):
    """Request model for calendar view."""
    start_date: str = Field(description="Start date for calendar view (ISO format)")
    end_date: str = Field(description="End date for calendar view (ISO format)")
    platforms: List[str] = Field(default=[], description="Filter by platforms")
    content_types: List[str] = Field(default=[], description="Filter by content types")
    organization_id: str = Field(description="Organization ID for context")
    user_id: str = Field(description="User ID for context")
    auth_token: str = Field(description="Authentication token for API access")


class CalendarTools:
    """
    MCP tools for calendar agent capabilities.
    
    Provides tools for content planning, scheduling, optimal timing analysis,
    content suggestions, and calendar management.
    """
    
    def __init__(self, agent_factory: AgentFactory):
        """
        Initialize calendar tools.
        
        Args:
            agent_factory: Factory for creating agent instances
        """
        self.agent_factory = agent_factory
    
    def get_tools(self) -> Dict[str, Callable]:
        """Get all calendar tools."""
        return {
            "content_planning": self.content_planning,
            "content_scheduling": self.content_scheduling,
            "optimal_timing_analysis": self.optimal_timing_analysis,
            "content_suggestions": self.content_suggestions,
            "calendar_view": self.calendar_view
        }
    
    async def content_planning(self, request: ContentPlanningRequest) -> str:
        """
        Plan content using the calendar agent.
        
        Args:
            request: Content planning request parameters
            
        Returns:
            Content planning report
        """
        try:
            # Get calendar agent instance
            calendar_agent = self.agent_factory.create_sample_agent("content_calendar_agent")
            if not calendar_agent:
                return "Error: Could not create calendar agent instance"
            
            # Prepare context
            context = {
                "organization_id": request.organization_id,
                "user_id": request.user_id,
                "auth_token": request.auth_token,
                "db": None  # Will be set by the agent if needed
            }
            
            # Create planning message
            message = f"Plan content for topic: {request.topic}"
            if request.platforms:
                message += f" for platforms: {', '.join(request.platforms)}"
            if request.content_types:
                message += f" with content types: {', '.join(request.content_types)}"
            if request.time_period:
                message += f" over {request.time_period}"
            if request.target_audience:
                message += f" targeting: {request.target_audience}"
            
            # Process the planning request
            result = await calendar_agent.process_message(message, context)
            return result
            
        except Exception as e:
            logger.error(f"Error in content planning: {e}")
            return f"Error performing content planning: {str(e)}"
    
    async def content_scheduling(self, request: ContentSchedulingRequest) -> str:
        """
        Schedule content using the calendar agent.
        
        Args:
            request: Content scheduling request parameters
            
        Returns:
            Scheduling confirmation
        """
        try:
            # Get calendar agent instance
            calendar_agent = self.agent_factory.create_sample_agent("content_calendar_agent")
            if not calendar_agent:
                return "Error: Could not create calendar agent instance"
            
            # Prepare context
            context = {
                "organization_id": request.organization_id,
                "user_id": request.user_id,
                "auth_token": request.auth_token,
                "db": None
            }
            
            # Create scheduling message
            message = f"Schedule content: '{request.content}' for {request.platform}"
            if request.scheduled_time:
                message += f" at {request.scheduled_time}"
            elif request.auto_optimize_timing:
                message += " with optimal timing"
            message += f" as {request.content_type}"
            
            # Process the scheduling request
            result = await calendar_agent.process_message(message, context)
            return result
            
        except Exception as e:
            logger.error(f"Error in content scheduling: {e}")
            return f"Error scheduling content: {str(e)}"

    async def optimal_timing_analysis(self, request: OptimalTimingRequest) -> str:
        """
        Analyze optimal timing using the calendar agent.

        Args:
            request: Optimal timing request parameters

        Returns:
            Optimal timing analysis report
        """
        try:
            # Get calendar agent instance
            calendar_agent = self.agent_factory.create_sample_agent("content_calendar_agent")
            if not calendar_agent:
                return "Error: Could not create calendar agent instance"

            # Prepare context
            context = {
                "organization_id": request.organization_id,
                "user_id": request.user_id,
                "auth_token": request.auth_token,
                "db": None
            }

            # Create timing analysis message
            message = f"Analyze optimal timing for {request.platform}"
            message += f" for {request.content_type} content"
            if request.target_audience:
                message += f" targeting {request.target_audience}"
            message += f" in {request.timezone} timezone"

            # Process the timing analysis request
            result = await calendar_agent.process_message(message, context)
            return result

        except Exception as e:
            logger.error(f"Error in optimal timing analysis: {e}")
            return f"Error analyzing optimal timing: {str(e)}"

    async def content_suggestions(self, request: ContentSuggestionsRequest) -> str:
        """
        Get content suggestions using the calendar agent.

        Args:
            request: Content suggestions request parameters

        Returns:
            Content suggestions report
        """
        try:
            # Get calendar agent instance
            calendar_agent = self.agent_factory.create_sample_agent("content_calendar_agent")
            if not calendar_agent:
                return "Error: Could not create calendar agent instance"

            # Prepare context
            context = {
                "organization_id": request.organization_id,
                "user_id": request.user_id,
                "auth_token": request.auth_token,
                "db": None
            }

            # Create suggestions message
            message = f"Suggest content ideas for topic: {request.topic}"
            if request.platforms:
                message += f" for platforms: {', '.join(request.platforms)}"
            if request.content_types:
                message += f" with content types: {', '.join(request.content_types)}"
            if request.use_knowledgebase:
                message += " using organization knowledgebase"
            if request.use_social_history:
                message += " and social media history"

            # Process the suggestions request
            result = await calendar_agent.process_message(message, context)
            return result

        except Exception as e:
            logger.error(f"Error in content suggestions: {e}")
            return f"Error getting content suggestions: {str(e)}"

    async def calendar_view(self, request: CalendarViewRequest) -> str:
        """
        Get calendar view using the calendar agent.

        Args:
            request: Calendar view request parameters

        Returns:
            Calendar view data
        """
        try:
            # Get calendar agent instance
            calendar_agent = self.agent_factory.create_sample_agent("content_calendar_agent")
            if not calendar_agent:
                return "Error: Could not create calendar agent instance"

            # Prepare context
            context = {
                "organization_id": request.organization_id,
                "user_id": request.user_id,
                "auth_token": request.auth_token,
                "db": None
            }

            # Create calendar view message
            message = f"Show calendar view from {request.start_date} to {request.end_date}"
            if request.platforms:
                message += f" for platforms: {', '.join(request.platforms)}"
            if request.content_types:
                message += f" with content types: {', '.join(request.content_types)}"

            # Process the calendar view request
            result = await calendar_agent.process_message(message, context)
            return result

        except Exception as e:
            logger.error(f"Error in calendar view: {e}")
            return f"Error getting calendar view: {str(e)}"
