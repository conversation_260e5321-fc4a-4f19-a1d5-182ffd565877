"""
Research Agent MCP Tools.

This module provides MCP tool implementations for the research agent capabilities.
"""

import json
from typing import Dict, Any, List, Optional, Callable
from pydantic import BaseModel, Field

from fastmcp import FastMCP

from app.utils.logger import get_logger
from app.database.database import get_db
from app.agents.agent_factory import AgentFactory

logger = get_logger(__name__)


class CompetitorAnalysisRequest(BaseModel):
    """Request model for competitor analysis."""
    competitor_name: str = Field(description="Name of the competitor to analyze")
    keywords: List[str] = Field(default=[], description="Keywords related to the analysis")
    time_period: str = Field(default="1w", description="Time period for analysis (e.g., '1w', '1m', '3m')")
    region: str = Field(default="", description="Geographic region for analysis")
    focus_areas: List[str] = Field(default=[], description="Specific areas to focus on")
    organization_id: str = Field(description="Organization ID for context")
    user_id: str = Field(description="User ID for context")


class TrendAnalysisRequest(BaseModel):
    """Request model for trend analysis."""
    topic: str = Field(description="Topic or industry to analyze trends for")
    keywords: List[str] = Field(default=[], description="Keywords related to the trend analysis")
    time_period: str = Field(default="1m", description="Time period for trend analysis")
    region: str = Field(default="", description="Geographic region for analysis")
    sources: List[str] = Field(default=[], description="Specific sources to analyze")
    organization_id: str = Field(description="Organization ID for context")
    user_id: str = Field(description="User ID for context")


class MarketResearchRequest(BaseModel):
    """Request model for market research."""
    market_segment: str = Field(description="Market segment to research")
    keywords: List[str] = Field(default=[], description="Keywords related to the market research")
    region: str = Field(default="", description="Geographic region for analysis")
    focus_areas: List[str] = Field(default=[], description="Specific areas to focus on")
    organization_id: str = Field(description="Organization ID for context")
    user_id: str = Field(description="User ID for context")


class DataAggregationRequest(BaseModel):
    """Request model for data aggregation."""
    data_sources: List[str] = Field(description="Data sources to aggregate from")
    query: str = Field(description="Query or topic for data aggregation")
    keywords: List[str] = Field(default=[], description="Keywords for filtering data")
    time_period: str = Field(default="1w", description="Time period for data collection")
    organization_id: str = Field(description="Organization ID for context")
    user_id: str = Field(description="User ID for context")


class GeneralResearchRequest(BaseModel):
    """Request model for general research."""
    query: str = Field(description="Research query or question")
    keywords: List[str] = Field(default=[], description="Keywords related to the research")
    sources: List[str] = Field(default=[], description="Specific sources to use")
    organization_id: str = Field(description="Organization ID for context")
    user_id: str = Field(description="User ID for context")


class ResearchTools:
    """
    MCP tools for research agent capabilities.
    
    Provides tools for competitor analysis, trend analysis, market research,
    data aggregation, and general research.
    """
    
    def __init__(self, agent_factory: AgentFactory):
        """
        Initialize research tools.
        
        Args:
            agent_factory: Factory for creating agent instances
        """
        self.agent_factory = agent_factory
    
    def get_tools(self) -> Dict[str, Callable]:
        """Get all research tools."""
        return {
            "competitor_analysis": self.competitor_analysis,
            "trend_analysis": self.trend_analysis,
            "market_research": self.market_research,
            "data_aggregation": self.data_aggregation,
            "general_research": self.general_research
        }
    
    async def competitor_analysis(self, request: CompetitorAnalysisRequest) -> str:
        """
        Perform competitor analysis using the research agent.
        
        Args:
            request: Competitor analysis request parameters
            
        Returns:
            Competitor analysis report
        """
        try:
            # Get research agent instance
            research_agent = self.agent_factory.create_sample_agent("deep_research_agent")
            if not research_agent:
                return "Error: Could not create research agent instance"
            
            # Prepare context
            context = {
                "organization_id": request.organization_id,
                "user_id": request.user_id,
                "db": None  # Will be set by the agent if needed
            }
            
            # Create research message
            message = f"Analyze competitor: {request.competitor_name}"
            if request.keywords:
                message += f" with focus on: {', '.join(request.keywords)}"
            if request.time_period:
                message += f" for the past {request.time_period}"
            if request.region:
                message += f" in {request.region}"
            if request.focus_areas:
                message += f" focusing on: {', '.join(request.focus_areas)}"
            
            # Process the research request
            result = await research_agent.process_message(message, context)
            return result
            
        except Exception as e:
            logger.error(f"Error in competitor analysis: {e}")
            return f"Error performing competitor analysis: {str(e)}"
    

    async def trend_analysis(self, request: TrendAnalysisRequest) -> str:
        """
        Perform trend analysis using the research agent.
        
        Args:
            request: Trend analysis request parameters
            
        Returns:
            Trend analysis report
        """
        try:
            # Get research agent instance
            research_agent = self.agent_factory.create_sample_agent("deep_research_agent")
            if not research_agent:
                return "Error: Could not create research agent instance"
            
            # Prepare context
            context = {
                "organization_id": request.organization_id,
                "user_id": request.user_id,
                "db": None
            }
            
            # Create research message
            message = f"Analyze trends for: {request.topic}"
            if request.keywords:
                message += f" with keywords: {', '.join(request.keywords)}"
            if request.time_period:
                message += f" over the past {request.time_period}"
            if request.region:
                message += f" in {request.region}"
            if request.sources:
                message += f" using sources: {', '.join(request.sources)}"
            
            # Process the research request
            result = await research_agent.process_message(message, context)
            return result
            
        except Exception as e:
            logger.error(f"Error in trend analysis: {e}")
            return f"Error performing trend analysis: {str(e)}"


    async def market_research(self, request: MarketResearchRequest) -> str:
        """
        Perform market research using the research agent.

        Args:
            request: Market research request parameters

        Returns:
            Market research report
        """
        try:
            # Get research agent instance
            research_agent = self.agent_factory.create_sample_agent("deep_research_agent")
            if not research_agent:
                return "Error: Could not create research agent instance"

            # Prepare context
            context = {
                "organization_id": request.organization_id,
                "user_id": request.user_id,
                "db": None
            }

            # Create research message
            message = f"Research market segment: {request.market_segment}"
            if request.keywords:
                message += f" with keywords: {', '.join(request.keywords)}"
            if request.region:
                message += f" in {request.region}"
            if request.focus_areas:
                message += f" focusing on: {', '.join(request.focus_areas)}"

            # Process the research request
            result = await research_agent.process_message(message, context)
            return result

        except Exception as e:
            logger.error(f"Error in market research: {e}")
            return f"Error performing market research: {str(e)}"


    async def data_aggregation(self, request: DataAggregationRequest) -> str:
        """
        Perform data aggregation using the research agent.

        Args:
            request: Data aggregation request parameters

        Returns:
            Data aggregation report
        """
        try:
            # Get research agent instance
            research_agent = self.agent_factory.create_sample_agent("deep_research_agent")
            if not research_agent:
                return "Error: Could not create research agent instance"

            # Prepare context
            context = {
                "organization_id": request.organization_id,
                "user_id": request.user_id,
                "db": None
            }

            # Create research message
            message = f"Aggregate data for: {request.query}"
            if request.data_sources:
                message += f" from sources: {', '.join(request.data_sources)}"
            if request.keywords:
                message += f" with keywords: {', '.join(request.keywords)}"
            if request.time_period:
                message += f" over the past {request.time_period}"

            # Process the research request
            result = await research_agent.process_message(message, context)
            return result

        except Exception as e:
            logger.error(f"Error in data aggregation: {e}")
            return f"Error performing data aggregation: {str(e)}"


    async def general_research(self, request: GeneralResearchRequest) -> str:
        """
        Perform general research using the research agent.

        Args:
            request: General research request parameters

        Returns:
            Research report
        """
        try:
            # Get research agent instance
            research_agent = self.agent_factory.create_sample_agent("deep_research_agent")
            if not research_agent:
                return "Error: Could not create research agent instance"

            # Prepare context
            context = {
                "organization_id": request.organization_id,
                "user_id": request.user_id,
                "db": None
            }

            # Create research message
            message = request.query
            if request.keywords:
                message += f" Keywords: {', '.join(request.keywords)}"
            if request.sources:
                message += f" Sources: {', '.join(request.sources)}"

            # Process the research request
            result = await research_agent.process_message(message, context)
            return result

        except Exception as e:
            logger.error(f"Error in general research: {e}")
            return f"Error performing general research: {str(e)}"
