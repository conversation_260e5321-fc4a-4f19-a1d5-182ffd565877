"""
MCP Client for connecting to EllumAI agents MCP server.

This module provides client-side functionality to connect to and interact
with the MCP server exposing research and calendar agent capabilities.
"""

import json
import asyncio
from typing import Dict, Any, List, Optional, Union
import httpx

from app.utils.logger import get_logger
from app.core.config import settings

logger = get_logger(__name__)


class MCPClient:
    """
    Client for connecting to EllumAI agents MCP server.
    
    Provides methods to call MCP tools and access resources from external clients.
    """
    
    def __init__(self, host: str = None, port: int = None):
        """
        Initialize MCP client.
        
        Args:
            host: MCP server host (defaults to settings)
            port: MCP server port (defaults to settings)
        """
        self.host = host or settings.MCP_HOST
        self.port = port or settings.MCP_PORT
        self.base_url = f"http://{self.host}:{self.port}"
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
    
    async def health_check(self) -> bool:
        """
        Check if the MCP server is healthy.

        Returns:
            True if server is healthy, False otherwise
        """
        try:
            # For now, use the main service health endpoint
            response = await self.client.get(f"http://{self.host}:8007/health")
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False
    
    async def get_server_info(self) -> Dict[str, Any]:
        """
        Get MCP server information.

        Returns:
            Server information
        """
        try:
            # Use the MCP status endpoint
            response = await self.client.get(f"http://{self.host}:8007/api/v1/mcp/status")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error getting server info: {e}")
            return {"error": str(e)}
    
    async def list_tools(self) -> List[Dict[str, Any]]:
        """
        List available MCP tools.

        Returns:
            List of available tools
        """
        try:
            response = await self.client.get(f"http://{self.host}:8007/api/v1/mcp/tools")
            response.raise_for_status()
            result = response.json()
            return result.get("data", [])
        except Exception as e:
            logger.error(f"Error listing tools: {e}")
            return []
    
    async def list_resources(self) -> List[Dict[str, Any]]:
        """
        List available MCP resources.

        Returns:
            List of available resources
        """
        try:
            response = await self.client.get(f"http://{self.host}:8007/api/v1/mcp/resources")
            response.raise_for_status()
            result = response.json()
            return result.get("data", [])
        except Exception as e:
            logger.error(f"Error listing resources: {e}")
            return []
    
    async def call_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Call an MCP tool.

        Args:
            tool_name: Name of the tool to call
            parameters: Tool parameters

        Returns:
            Tool execution result
        """
        try:
            payload = {
                "tool_name": tool_name,
                "parameters": parameters
            }

            response = await self.client.post(
                f"http://{self.host}:8007/api/v1/mcp/tools/call",
                json=payload
            )
            response.raise_for_status()
            result = response.json()
            return result.get("data", result)

        except Exception as e:
            logger.error(f"Error calling tool {tool_name}: {e}")
            return {"error": str(e)}
    
    async def get_resource(self, resource_uri: str) -> Dict[str, Any]:
        """
        Get an MCP resource.

        Args:
            resource_uri: URI of the resource to get

        Returns:
            Resource data
        """
        try:
            payload = {"resource_uri": resource_uri}

            response = await self.client.post(
                f"http://{self.host}:8007/api/v1/mcp/resources/get",
                json=payload
            )
            response.raise_for_status()
            result = response.json()
            return result.get("data", result)

        except Exception as e:
            logger.error(f"Error getting resource {resource_uri}: {e}")
            return {"error": str(e)}
    
    # Research Agent Tools
    async def competitor_analysis(
        self,
        competitor_name: str,
        organization_id: str,
        user_id: str,
        keywords: List[str] = None,
        time_period: str = "1w",
        region: str = "",
        focus_areas: List[str] = None
    ) -> Dict[str, Any]:
        """
        Perform competitor analysis using the research agent.
        
        Args:
            competitor_name: Name of the competitor to analyze
            organization_id: Organization ID
            user_id: User ID
            keywords: Keywords for analysis
            time_period: Time period for analysis
            region: Geographic region
            focus_areas: Specific focus areas
            
        Returns:
            Competitor analysis result
        """
        parameters = {
            "competitor_name": competitor_name,
            "organization_id": organization_id,
            "user_id": user_id,
            "keywords": keywords or [],
            "time_period": time_period,
            "region": region,
            "focus_areas": focus_areas or []
        }
        
        return await self.call_tool("competitor_analysis", parameters)
    
    async def trend_analysis(
        self,
        topic: str,
        organization_id: str,
        user_id: str,
        keywords: List[str] = None,
        time_period: str = "1m",
        region: str = "",
        sources: List[str] = None
    ) -> Dict[str, Any]:
        """
        Perform trend analysis using the research agent.
        
        Args:
            topic: Topic to analyze trends for
            organization_id: Organization ID
            user_id: User ID
            keywords: Keywords for analysis
            time_period: Time period for analysis
            region: Geographic region
            sources: Specific sources to analyze
            
        Returns:
            Trend analysis result
        """
        parameters = {
            "topic": topic,
            "organization_id": organization_id,
            "user_id": user_id,
            "keywords": keywords or [],
            "time_period": time_period,
            "region": region,
            "sources": sources or []
        }
        
        return await self.call_tool("trend_analysis", parameters)
    
    async def market_research(
        self,
        market_segment: str,
        organization_id: str,
        user_id: str,
        keywords: List[str] = None,
        region: str = "",
        focus_areas: List[str] = None
    ) -> Dict[str, Any]:
        """
        Perform market research using the research agent.
        
        Args:
            market_segment: Market segment to research
            organization_id: Organization ID
            user_id: User ID
            keywords: Keywords for research
            region: Geographic region
            focus_areas: Specific focus areas
            
        Returns:
            Market research result
        """
        parameters = {
            "market_segment": market_segment,
            "organization_id": organization_id,
            "user_id": user_id,
            "keywords": keywords or [],
            "region": region,
            "focus_areas": focus_areas or []
        }
        
        return await self.call_tool("market_research", parameters)
    
    # Calendar Agent Tools
    async def content_planning(
        self,
        topic: str,
        organization_id: str,
        user_id: str,
        auth_token: str,
        platforms: List[str] = None,
        content_types: List[str] = None,
        time_period: str = "1w",
        target_audience: str = ""
    ) -> Dict[str, Any]:
        """
        Plan content using the calendar agent.
        
        Args:
            topic: Topic for content planning
            organization_id: Organization ID
            user_id: User ID
            auth_token: Authentication token
            platforms: Social media platforms
            content_types: Types of content
            time_period: Time period for planning
            target_audience: Target audience
            
        Returns:
            Content planning result
        """
        parameters = {
            "topic": topic,
            "organization_id": organization_id,
            "user_id": user_id,
            "auth_token": auth_token,
            "platforms": platforms or [],
            "content_types": content_types or [],
            "time_period": time_period,
            "target_audience": target_audience
        }
        
        return await self.call_tool("content_planning", parameters)
    
    async def content_scheduling(
        self,
        content: str,
        platform: str,
        organization_id: str,
        user_id: str,
        auth_token: str,
        scheduled_time: str = None,
        content_type: str = "post",
        auto_optimize_timing: bool = True
    ) -> Dict[str, Any]:
        """
        Schedule content using the calendar agent.
        
        Args:
            content: Content to schedule
            platform: Social media platform
            organization_id: Organization ID
            user_id: User ID
            auth_token: Authentication token
            scheduled_time: Scheduled time (ISO format)
            content_type: Type of content
            auto_optimize_timing: Whether to optimize timing
            
        Returns:
            Content scheduling result
        """
        parameters = {
            "content": content,
            "platform": platform,
            "organization_id": organization_id,
            "user_id": user_id,
            "auth_token": auth_token,
            "scheduled_time": scheduled_time,
            "content_type": content_type,
            "auto_optimize_timing": auto_optimize_timing
        }
        
        return await self.call_tool("content_scheduling", parameters)

    # Resource Access Methods
    async def get_research_reports(self, organization_id: str, report_type: str = None) -> Dict[str, Any]:
        """Get research reports resource."""
        uri = f"research://reports/{organization_id}"
        if report_type:
            uri += f"/{report_type}"
        return await self.get_resource(uri)

    async def get_competitor_data(self, organization_id: str, competitor_name: str = None) -> Dict[str, Any]:
        """Get competitor data resource."""
        uri = f"research://competitors/{organization_id}"
        if competitor_name:
            uri += f"/{competitor_name}"
        return await self.get_resource(uri)

    async def get_content_calendar(self, organization_id: str, date_range: str = None) -> Dict[str, Any]:
        """Get content calendar resource."""
        uri = f"calendar://content/{organization_id}"
        if date_range:
            uri += f"/{date_range}"
        return await self.get_resource(uri)

    async def get_optimal_timing(self, organization_id: str, platform: str = None) -> Dict[str, Any]:
        """Get optimal timing resource."""
        uri = f"calendar://timing/{organization_id}"
        if platform:
            uri += f"/{platform}"
        return await self.get_resource(uri)


# Convenience functions for creating client instances
async def create_mcp_client(host: str = None, port: int = None) -> MCPClient:
    """
    Create an MCP client instance.

    Args:
        host: MCP server host
        port: MCP server port

    Returns:
        MCP client instance
    """
    return MCPClient(host, port)


async def with_mcp_client(func, *args, **kwargs):
    """
    Execute a function with an MCP client context.

    Args:
        func: Function to execute
        *args: Function arguments
        **kwargs: Function keyword arguments

    Returns:
        Function result
    """
    async with MCPClient() as client:
        return await func(client, *args, **kwargs)
