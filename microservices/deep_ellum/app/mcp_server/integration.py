"""
MCP Integration with existing agent framework.

This module provides integration between the MCP server and the existing
agent framework, ensuring backward compatibility while adding MCP capabilities.
"""

import asyncio
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager

from app.utils.logger import get_logger
from app.mcp_server.server import MC<PERSON>erver, get_mcp_server
from app.agents.agent_factory import AgentFactory
from app.database.database import get_db

logger = get_logger(__name__)


class MCPAgentIntegration:
    """
    Integration layer between MCP server and existing agents.
    
    Provides seamless integration while maintaining backward compatibility
    with the existing agent framework.
    """
    
    def __init__(self):
        """Initialize MCP agent integration."""
        self.mcp_server: Optional[MCPServer] = None
        self.agent_factory = AgentFactory()
        self._server_task: Optional[asyncio.Task] = None
    
    async def initialize(self, host: str = "localhost", port: int = 8008):
        """
        Initialize the MCP integration.
        
        Args:
            host: MCP server host
            port: MCP server port
        """
        try:
            logger.info("Initializing MCP agent integration")
            
            # Get MCP server instance
            self.mcp_server = await get_mcp_server()
            self.mcp_server.host = host
            self.mcp_server.port = port
            
            logger.info(f"MCP integration initialized for {host}:{port}")
            
        except Exception as e:
            logger.error(f"Error initializing MCP integration: {e}")
            raise
    
    async def start_server(self):
        """Start the MCP server in the background."""
        if not self.mcp_server:
            raise RuntimeError("MCP integration not initialized")
        
        try:
            logger.info("Starting MCP server")
            
            # Start server in background task
            self._server_task = asyncio.create_task(self.mcp_server.start())
            
            # Give server a moment to start
            await asyncio.sleep(1)
            
            logger.info("MCP server started successfully")
            
        except Exception as e:
            logger.error(f"Error starting MCP server: {e}")
            raise
    
    async def stop_server(self):
        """Stop the MCP server."""
        try:
            logger.info("Stopping MCP server")
            
            if self._server_task and not self._server_task.done():
                self._server_task.cancel()
                try:
                    await self._server_task
                except asyncio.CancelledError:
                    pass
            
            if self.mcp_server:
                await self.mcp_server.stop()
            
            logger.info("MCP server stopped")
            
        except Exception as e:
            logger.error(f"Error stopping MCP server: {e}")
    
    def is_server_running(self) -> bool:
        """Check if the MCP server is running."""
        return (
            self._server_task is not None and
            not self._server_task.done() and
            not self._server_task.cancelled()
        )

    async def get_mcp_server(self):
        """Get the MCP server instance."""
        if not self.mcp_server:
            await self.initialize()
        return self.mcp_server
    
    async def get_agent_capabilities(self, agent_type: str) -> Dict[str, Any]:
        """
        Get agent capabilities including MCP tools.
        
        Args:
            agent_type: Type of agent
            
        Returns:
            Agent capabilities including MCP tools
        """
        try:
            # Get standard agent capabilities
            agent = self.agent_factory.create_sample_agent(agent_type)
            if not agent:
                return {"error": f"Unknown agent type: {agent_type}"}
            
            capabilities = {
                "name": agent.name,
                "description": agent.description,
                "capabilities": [cap.value for cap in agent.capabilities],
                "mcp_enabled": True,
                "mcp_tools": [],
                "mcp_resources": []
            }
            
            # Add MCP-specific capabilities
            if agent_type in ["deep_research_agent", "openai_deep_research_agent"]:
                capabilities["mcp_tools"] = [
                    "competitor_analysis",
                    "trend_analysis", 
                    "market_research",
                    "data_aggregation",
                    "general_research"
                ]
                capabilities["mcp_resources"] = [
                    "research_reports",
                    "competitor_data",
                    "trend_data",
                    "market_data",
                    "research_templates"
                ]
            
            elif agent_type == "content_calendar_agent":
                capabilities["mcp_tools"] = [
                    "content_planning",
                    "content_scheduling",
                    "optimal_timing_analysis",
                    "content_suggestions",
                    "calendar_view"
                ]
                capabilities["mcp_resources"] = [
                    "content_calendar",
                    "scheduled_content",
                    "optimal_timing",
                    "content_templates",
                    "social_insights"
                ]
            
            return capabilities
            
        except Exception as e:
            logger.error(f"Error getting agent capabilities: {e}")
            return {"error": f"Error getting capabilities: {str(e)}"}
    
    async def process_mcp_request(
        self,
        agent_type: str,
        tool_name: str,
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process an MCP tool request through the agent framework.

        Args:
            agent_type: Type of agent to use ("auto" for auto-detection)
            tool_name: Name of the MCP tool
            parameters: Tool parameters

        Returns:
            Tool execution result
        """
        try:
            # Auto-detect agent type based on tool name
            if agent_type == "auto":
                research_tools = ["competitor_analysis", "trend_analysis", "market_research", "data_aggregation", "general_research"]
                calendar_tools = ["content_planning", "content_scheduling", "optimal_timing_analysis", "content_suggestions", "calendar_view"]

                if tool_name in research_tools:
                    agent_type = "deep_research_agent"
                elif tool_name in calendar_tools:
                    agent_type = "content_calendar_agent"
                else:
                    return {"error": f"Unknown tool: {tool_name}"}

            # Validate agent type
            if agent_type not in ["deep_research_agent", "openai_deep_research_agent", "content_calendar_agent"]:
                return {"error": f"Unsupported agent type for MCP: {agent_type}"}

            # Get agent capabilities to validate tool
            capabilities = await self.get_agent_capabilities(agent_type)
            if tool_name not in capabilities.get("mcp_tools", []):
                return {"error": f"Tool {tool_name} not available for agent {agent_type}"}

            # Get server instance and call tool
            if not self.mcp_server:
                return {"error": "MCP server not initialized"}

            result = await self.mcp_server.call_tool(tool_name, parameters)

            return result

        except Exception as e:
            logger.error(f"Error processing MCP request: {e}")
            return {"error": f"Error processing request: {str(e)}"}
    
    @asynccontextmanager
    async def lifespan(self, host: str = "localhost", port: int = 8008):
        """
        Context manager for MCP integration lifespan.
        
        Args:
            host: MCP server host
            port: MCP server port
        """
        try:
            await self.initialize(host, port)
            await self.start_server()
            yield self
        finally:
            await self.stop_server()


# Global integration instance
mcp_integration = None


async def get_mcp_integration() -> MCPAgentIntegration:
    """Get the global MCP integration instance."""
    global mcp_integration
    if mcp_integration is None:
        mcp_integration = MCPAgentIntegration()
    return mcp_integration


async def initialize_mcp_integration(host: str = "localhost", port: int = 8008):
    """Initialize the MCP integration."""
    integration = await get_mcp_integration()
    await integration.initialize(host, port)
    return integration


async def start_mcp_integration():
    """Start the MCP integration server."""
    integration = await get_mcp_integration()
    await integration.start_server()


async def stop_mcp_integration():
    """Stop the MCP integration server."""
    global mcp_integration
    if mcp_integration:
        await mcp_integration.stop_server()
        mcp_integration = None
