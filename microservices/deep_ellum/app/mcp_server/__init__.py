"""
MCP (Model Context Protocol) integration for EllumAI agents.

This module provides MCP server functionality for research and calendar agents,
allowing them to be used as MCP tools by external clients.
"""

from .server import MCPServer
from .tools import ResearchTools, CalendarTools
from .resources import ResearchResources, CalendarResources

__all__ = [
    "MCPServer",
    "ResearchTools", 
    "CalendarTools",
    "ResearchResources",
    "CalendarResources"
]
