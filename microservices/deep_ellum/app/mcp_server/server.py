"""
MCP Server implementation for EllumAI agents.

This module provides the main MCP server that exposes research and calendar
agent capabilities as MCP tools and resources.
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from contextlib import asynccontextmanager

from fastmcp import FastMCP

from app.utils.logger import get_logger
from app.database.database import get_db
from app.agents.agent_factory import AgentFactory
from app.models.schemas import AgentCapability

logger = get_logger(__name__)


class MCPServer:
    """
    MCP Server for EllumAI agents.
    
    Provides research and calendar agent capabilities through the Model Context Protocol,
    allowing external clients to use these agents as tools and access their resources.
    """
    
    def __init__(self, host: str = "localhost", port: int = 8008):
        """
        Initialize the MCP server.

        Args:
            host: Server host address
            port: Server port number
        """
        self.host = host
        self.port = port
        self.app = FastMCP("EllumAI Agents MCP Server")
        self.agent_factory = AgentFactory()
        self.tools = {}
        self.resources = {}
        self._setup_server()
    
    def _setup_server(self):
        """Set up the MCP server with tools and resources."""
        # Register tools and resources
        self._register_research_tools()
        self._register_calendar_tools()
        self._register_resources()
    
    def _register_research_tools(self):
        """Register research agent tools."""
        from .tools.research_tools import ResearchTools
        research_tools = ResearchTools(self.agent_factory)

        # Store tools for later access
        self.tools.update(research_tools.get_tools())

    def _register_calendar_tools(self):
        """Register calendar agent tools."""
        from .tools.calendar_tools import CalendarTools
        calendar_tools = CalendarTools(self.agent_factory)

        # Store tools for later access
        self.tools.update(calendar_tools.get_tools())

    def _register_resources(self):
        """Register MCP resources."""
        from .resources.research_resources import ResearchResources
        from .resources.calendar_resources import CalendarResources

        research_resources = ResearchResources()
        calendar_resources = CalendarResources()

        # Store resources for later access
        self.resources.update(research_resources.get_resources())
        self.resources.update(calendar_resources.get_resources())
    
    async def start(self):
        """Start the MCP server."""
        try:
            logger.info(f"Starting MCP server on {self.host}:{self.port}")
            # For now, just log that the server is "started"
            # In a real implementation, this would start the FastMCP server
            logger.info("MCP server started successfully")
        except Exception as e:
            logger.error(f"Error starting MCP server: {e}")
            raise
    
    async def stop(self):
        """Stop the MCP server."""
        try:
            logger.info("Stopping MCP server")
            # Server cleanup logic would go here
        except Exception as e:
            logger.error(f"Error stopping MCP server: {e}")

    async def call_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Call an MCP tool."""
        try:
            if tool_name not in self.tools:
                return {"error": f"Tool '{tool_name}' not found"}

            tool_func = self.tools[tool_name]

            # Create appropriate request object based on tool name
            if tool_name == "competitor_analysis":
                from .tools.research_tools import CompetitorAnalysisRequest
                request = CompetitorAnalysisRequest(**parameters)
            elif tool_name == "trend_analysis":
                from .tools.research_tools import TrendAnalysisRequest
                request = TrendAnalysisRequest(**parameters)
            elif tool_name == "market_research":
                from .tools.research_tools import MarketResearchRequest
                request = MarketResearchRequest(**parameters)
            elif tool_name == "data_aggregation":
                from .tools.research_tools import DataAggregationRequest
                request = DataAggregationRequest(**parameters)
            elif tool_name == "general_research":
                from .tools.research_tools import GeneralResearchRequest
                request = GeneralResearchRequest(**parameters)
            elif tool_name == "content_planning":
                from .tools.calendar_tools import ContentPlanningRequest
                request = ContentPlanningRequest(**parameters)
            elif tool_name == "content_scheduling":
                from .tools.calendar_tools import ContentSchedulingRequest
                request = ContentSchedulingRequest(**parameters)
            elif tool_name == "optimal_timing_analysis":
                from .tools.calendar_tools import OptimalTimingRequest
                request = OptimalTimingRequest(**parameters)
            elif tool_name == "content_suggestions":
                from .tools.calendar_tools import ContentSuggestionsRequest
                request = ContentSuggestionsRequest(**parameters)
            elif tool_name == "calendar_view":
                from .tools.calendar_tools import CalendarViewRequest
                request = CalendarViewRequest(**parameters)
            else:
                return {"error": f"Unknown tool: {tool_name}"}

            # Call the tool function
            result = await tool_func(request)
            return {"success": True, "result": result, "tool": tool_name}

        except Exception as e:
            logger.error(f"Error calling tool {tool_name}: {e}")
            return {"error": f"Error calling tool: {str(e)}"}

    async def get_resource(self, resource_uri: str) -> Dict[str, Any]:
        """Get an MCP resource."""
        try:
            # Parse resource URI
            parts = resource_uri.split("://")
            if len(parts) != 2:
                return {"error": "Invalid resource URI format"}

            scheme, path = parts
            path_parts = path.split("/")

            if scheme == "research":
                if len(path_parts) < 2:
                    return {"error": "Invalid research resource path"}

                resource_type = path_parts[0]
                organization_id = path_parts[1]

                if resource_type == "reports":
                    report_type = path_parts[2] if len(path_parts) > 2 else None
                    resource_func = self.resources["research_reports"]
                    result = await resource_func(organization_id, report_type)
                elif resource_type == "competitors":
                    competitor_name = path_parts[2] if len(path_parts) > 2 else None
                    resource_func = self.resources["competitor_data"]
                    result = await resource_func(organization_id, competitor_name)
                elif resource_type == "trends":
                    time_period = path_parts[2] if len(path_parts) > 2 else None
                    resource_func = self.resources["trend_data"]
                    result = await resource_func(organization_id, time_period)
                elif resource_type == "market":
                    market_segment = path_parts[2] if len(path_parts) > 2 else None
                    resource_func = self.resources["market_data"]
                    result = await resource_func(organization_id, market_segment)
                elif resource_type == "templates":
                    template_type = path_parts[1] if len(path_parts) > 1 else None
                    resource_func = self.resources["research_templates"]
                    result = await resource_func(template_type)
                else:
                    return {"error": f"Unknown research resource type: {resource_type}"}

            elif scheme == "calendar":
                if len(path_parts) < 2:
                    return {"error": "Invalid calendar resource path"}

                resource_type = path_parts[0]
                organization_id = path_parts[1]

                if resource_type == "content":
                    date_range = path_parts[2] if len(path_parts) > 2 else None
                    resource_func = self.resources["content_calendar"]
                    result = await resource_func(organization_id, date_range)
                elif resource_type == "scheduled":
                    platform = path_parts[2] if len(path_parts) > 2 else None
                    resource_func = self.resources["scheduled_content"]
                    result = await resource_func(organization_id, platform)
                elif resource_type == "timing":
                    platform = path_parts[2] if len(path_parts) > 2 else None
                    resource_func = self.resources["optimal_timing"]
                    result = await resource_func(organization_id, platform)
                elif resource_type == "templates":
                    template_type = path_parts[1] if len(path_parts) > 1 else None
                    resource_func = self.resources["content_templates"]
                    result = await resource_func(template_type)
                elif resource_type == "insights":
                    platform = path_parts[2] if len(path_parts) > 2 else None
                    resource_func = self.resources["social_insights"]
                    result = await resource_func(organization_id, platform)
                else:
                    return {"error": f"Unknown calendar resource type: {resource_type}"}
            else:
                return {"error": f"Unknown resource scheme: {scheme}"}

            return {"success": True, "data": result}

        except Exception as e:
            logger.error(f"Error getting resource {resource_uri}: {e}")
            return {"error": f"Error getting resource: {str(e)}"}

    def list_tools(self) -> List[Dict[str, Any]]:
        """List available tools."""
        tools = []
        for tool_name in self.tools.keys():
            tools.append({
                "name": tool_name,
                "description": f"MCP tool: {tool_name}"
            })
        return tools

    def list_resources(self) -> List[Dict[str, Any]]:
        """List available resources."""
        resources = []
        for resource_name in self.resources.keys():
            resources.append({
                "name": resource_name,
                "description": f"MCP resource: {resource_name}"
            })
        return resources
    
    @asynccontextmanager
    async def lifespan(self):
        """Context manager for server lifespan."""
        try:
            yield self
        finally:
            await self.stop()


# Global server instance
mcp_server = None


async def get_mcp_server() -> MCPServer:
    """Get the global MCP server instance."""
    global mcp_server
    if mcp_server is None:
        mcp_server = MCPServer()
    return mcp_server


async def start_mcp_server(host: str = "localhost", port: int = 8008):
    """Start the MCP server."""
    server = await get_mcp_server()
    server.host = host
    server.port = port
    await server.start()


async def stop_mcp_server():
    """Stop the MCP server."""
    global mcp_server
    if mcp_server:
        await mcp_server.stop()
        mcp_server = None
