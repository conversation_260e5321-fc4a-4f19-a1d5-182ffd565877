from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from typing import List, Optional, Annotated
from uuid import UUID

from app.database.database import get_db
from app.models.models import CustomAgent, AgentTemplate
from app.models.schemas import (
    CustomAgentCreate, CustomAgentUpdate, CustomAgentResponse,
    AgentListResponse, AgentTemplateResponse, AgentCapability
)
from pydantic import BaseModel
from app.utils.auth import get_user_id, verify_organization, check_permissions
from app.utils.logger import get_logger
from app.agents.agent_factory import agent_factory
from app.services.gemini_service import gemini_service

logger = get_logger(__name__)
router = APIRouter()


@router.post("/", response_model=CustomAgentResponse)
async def create_agent(
    agent_data: CustomAgentCreate,
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """Create a new custom agent."""
    try:
        # Check permissions for creating agents
        # await check_permissions(user_id, organization_id, "create_agent")

        # Generate enhanced instructions if needed
        if len(agent_data.instructions.strip()) < 50:
            logger.info("Generating enhanced instructions for agent")
            enhanced_instructions = await gemini_service.generate_agent_instructions(
                agent_data.description,
                agent_data.personality
            )
            agent_data.instructions = enhanced_instructions

        # Create agent record
        agent = CustomAgent(
            name=agent_data.name,
            description=agent_data.description,
            personality=agent_data.personality,
            instructions=agent_data.instructions,
            capabilities=[cap.value for cap in agent_data.capabilities] if agent_data.capabilities else [],
            is_active=agent_data.is_active,
            created_by=user_id,
            organization_id=organization_id
        )

        db.add(agent)
        await db.commit()
        await db.refresh(agent)

        # Invalidate agent factory cache to ensure new agent is immediately available
        from app.agents.agent_factory import AgentFactory
        agent_factory = AgentFactory()
        agent_factory.invalidate_registry_cache()

        logger.info(f"Created agent: {agent.name} (ID: {agent.id}) for organization: {organization_id}")
        return agent

    except Exception as e:
        logger.error(f"Error creating agent: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create agent"
        )


@router.get("/", response_model=AgentListResponse)
async def list_agents(
    organization_id: Annotated[str, Depends(verify_organization)],
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    include_samples: bool = Query(True),
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """List organization's custom agents and optionally sample agents."""
    try:
        # Check permissions for viewing agents
        # await check_permissions(user_id, organization_id, "view_agents")

        # Build query conditions
        conditions = [CustomAgent.is_active == True]

        if include_samples:
            # Include organization's agents and sample agents
            conditions.append(
                or_(
                    and_(
                        CustomAgent.organization_id == organization_id,
                        CustomAgent.is_sample == False
                    ),
                    CustomAgent.is_sample == True
                )
            )
        else:
            # Only organization's agents
            conditions.extend([
                CustomAgent.organization_id == organization_id,
                CustomAgent.is_sample == False
            ])

        # Get total count
        count_query = select(func.count(CustomAgent.id)).where(and_(*conditions))
        total_result = await db.execute(count_query)
        total = total_result.scalar()

        # Get agents with pagination
        offset = (page - 1) * size
        query = (
            select(CustomAgent)
            .where(and_(*conditions))
            .order_by(CustomAgent.created_at.desc())
            .offset(offset)
            .limit(size)
        )

        result = await db.execute(query)
        agents = result.scalars().all()

        return AgentListResponse(
            agents=agents,
            total=total,
            page=page,
            size=size
        )

    except Exception as e:
        logger.error(f"Error listing agents: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list agents"
        )


@router.get("/{agent_id}", response_model=CustomAgentResponse)
async def get_agent(
    agent_id: UUID,
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """Get a specific agent by ID."""
    try:
        # Check permissions for viewing agents
        # await check_permissions(user_id, organization_id, "view_agents")

        query = select(CustomAgent).where(
            and_(
                CustomAgent.id == agent_id,
                or_(
                    CustomAgent.organization_id == organization_id,
                    CustomAgent.is_sample == True
                ),
                CustomAgent.is_active == True
            )
        )

        result = await db.execute(query)
        agent = result.scalar_one_or_none()

        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )

        return agent

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting agent: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get agent"
        )


@router.put("/{agent_id}", response_model=CustomAgentResponse)
async def update_agent(
    agent_id: UUID,
    agent_data: CustomAgentUpdate,
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """Update an existing agent."""
    try:
        # Check permissions for updating agents
        # await check_permissions(user_id, organization_id, "update_agent")

        # Get agent
        query = select(CustomAgent).where(
            and_(
                CustomAgent.id == agent_id,
                CustomAgent.organization_id == organization_id,
                CustomAgent.is_sample == False,  # Can't update sample agents
                CustomAgent.is_active == True
            )
        )

        result = await db.execute(query)
        agent = result.scalar_one_or_none()

        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found or cannot be updated"
            )

        # Update fields
        update_data = agent_data.model_dump(exclude_unset=True)

        # Convert capabilities to list of strings
        if "capabilities" in update_data and update_data["capabilities"] is not None:
            update_data["capabilities"] = [cap.value for cap in update_data["capabilities"]]

        for field, value in update_data.items():
            setattr(agent, field, value)

        await db.commit()
        await db.refresh(agent)

        # Clear agent cache
        agent_factory.clear_cache(agent_id)

        logger.info(f"Updated agent: {agent.name} (ID: {agent.id}) for organization: {organization_id}")
        return agent

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating agent: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update agent"
        )


@router.delete("/{agent_id}")
async def delete_agent(
    agent_id: UUID,
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """Delete an agent (soft delete)."""
    try:
        # Check permissions for deleting agents
        # await check_permissions(user_id, organization_id, "delete_agent")

        # Get agent
        query = select(CustomAgent).where(
            and_(
                CustomAgent.id == agent_id,
                CustomAgent.organization_id == organization_id,
                CustomAgent.is_sample == False,  # Can't delete sample agents
                CustomAgent.is_active == True
            )
        )

        result = await db.execute(query)
        agent = result.scalar_one_or_none()

        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found or cannot be deleted"
            )

        # Soft delete
        agent.is_active = False
        await db.commit()

        # Clear agent cache
        agent_factory.clear_cache(agent_id)

        logger.info(f"Deleted agent: {agent.name} (ID: {agent.id}) for organization: {organization_id}")
        return {"message": "Agent deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting agent: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete agent"
        )


@router.get("/templates/", response_model=List[AgentTemplateResponse])
async def list_agent_templates(
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """List available agent templates."""
    try:
        # Check permissions for viewing templates
        # await check_permissions(user_id, organization_id, "view_templates")

        query = select(AgentTemplate).where(AgentTemplate.is_active == True)
        result = await db.execute(query)
        templates = result.scalars().all()

        return templates

    except Exception as e:
        logger.error(f"Error listing templates: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list templates"
        )


@router.post("/refresh-registry")
async def refresh_agent_registry(
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """Refresh the agent registry to make new agents immediately available."""
    try:
        # Check permissions for refreshing registry
        # await check_permissions(user_id, organization_id, "refresh_registry")

        from app.services.orchestrator_service import orchestrator_service

        # Force refresh the agent registry
        registry = await orchestrator_service.refresh_agent_registry(db, organization_id)

        agent_count = len(registry)
        logger.info(f"Refreshed agent registry for organization {organization_id}: {agent_count} agents")

        return {
            "message": "Agent registry refreshed successfully",
            "agent_count": agent_count,
            "agents": [
                {
                    "id": key,
                    "name": info.get("name"),
                    "type": info.get("type")
                }
                for key, info in registry.items()
            ]
        }

    except Exception as e:
        logger.error(f"Error refreshing agent registry: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to refresh agent registry"
        )


@router.get("/samples/info")
async def get_sample_agents_info(
    organization_id: Annotated[str, Depends(verify_organization)],
    user_id: str = Depends(get_user_id)
):
    """Get information about available sample agents."""
    try:
        # Check permissions for viewing agents (sample agents are included)
        # await check_permissions(user_id, organization_id, "view_agents")

        return agent_factory.get_sample_agents_info()
    except Exception as e:
        logger.error(f"Error getting sample agents info: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get sample agents information"
        )


class AgentTestRequest(BaseModel):
    instructions: str
    test_message: str = "Hello, can you help me?"
    personality: Optional[str] = None
    capabilities: Optional[List[AgentCapability]] = None


@router.post("/test")
async def test_agent_configuration(
    request: AgentTestRequest,
    organization_id: Annotated[str, Depends(verify_organization)],
    user_id: str = Depends(get_user_id)
):
    """Test an agent configuration with a sample message."""
    try:
        # Check permissions for testing agents
        # await check_permissions(user_id, organization_id, "test_agent")

        response = await agent_factory.test_agent(
            instructions=request.instructions,
            test_message=request.test_message,
            personality=request.personality,
            capabilities=request.capabilities or []
        )

        return {
            "test_message": request.test_message,
            "agent_response": response
        }

    except Exception as e:
        logger.error(f"Error testing agent: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to test agent configuration"
        )
