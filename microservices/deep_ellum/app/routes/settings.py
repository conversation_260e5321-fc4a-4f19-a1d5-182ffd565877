from typing import Annotated
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.database.database import get_db
from app.models.models import OrganizationSettings
from app.models.schemas import (
    KnowledgebaseToggleRequest,
    SocialsDatabaseToggleRequest,
    OrganizationSettingsResponse
)
from app.services.intelligent_knowledge_service import intelligent_knowledge_service
from app.services.intelligent_socials_service import intelligent_socials_service
from app.utils.auth import get_current_user, verify_organization
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()


@router.get("/", response_model=OrganizationSettingsResponse)
async def get_organization_settings(
    organization_id: str = Depends(verify_organization),
    token: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """Get organization knowledgebase settings."""
    try:
        # Get existing settings
        query = select(OrganizationSettings).where(
            OrganizationSettings.organization_id == organization_id
        )
        result = await db.execute(query)
        settings = result.scalar_one_or_none()

        if not settings:
            # Create default settings
            settings = OrganizationSettings(
                organization_id=organization_id,
                knowledgebase_enabled=False,
                socials_database_enabled=False
            )
            db.add(settings)
            await db.commit()
            await db.refresh(settings)

        return OrganizationSettingsResponse.model_validate(settings)

    except Exception as e:
        logger.error(f"Error retrieving organization settings: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving organization settings")


@router.post("/knowledgebase/toggle")
async def toggle_knowledgebase(
    toggle_data: KnowledgebaseToggleRequest,
    organization_id: str = Depends(verify_organization),
    token: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """Toggle knowledgebase access for the organization."""
    try:
        # Get or create organization settings
        query = select(OrganizationSettings).where(
            OrganizationSettings.organization_id == organization_id
        )
        result = await db.execute(query)
        settings = result.scalar_one_or_none()

        if settings:
            # Update existing settings
            settings.knowledgebase_enabled = toggle_data.enabled
        else:
            # Create new settings
            settings = OrganizationSettings(
                organization_id=organization_id,
                knowledgebase_enabled=toggle_data.enabled,
                socials_database_enabled=False
            )
            db.add(settings)

        await db.commit()
        await db.refresh(settings)

        status = "enabled" if toggle_data.enabled else "disabled"
        logger.info(f"Knowledgebase {status} for organization {organization_id}")

        return {
            "success": True,
            "message": f"Knowledgebase {status} for organization",
            "enabled": toggle_data.enabled,
            "organization_id": organization_id
        }

    except Exception as e:
        logger.error(f"Error toggling knowledgebase: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Error toggling knowledgebase access")


@router.get("/knowledgebase/status")
async def get_knowledgebase_status(
    organization_id: str = Depends(verify_organization),
    token: dict = Depends(get_current_user),
):
    """Get knowledgebase availability status."""
    try:
        auth_token = token["raw_token"]
        status = await intelligent_knowledge_service.get_knowledge_status(organization_id, auth_token)
        return status

    except Exception as e:
        logger.error(f"Error checking knowledgebase status: {e}")
        raise HTTPException(status_code=500, detail="Error checking knowledgebase status")


@router.post("/socials-database/toggle")
async def toggle_socials_database(
    toggle_data: SocialsDatabaseToggleRequest,
    organization_id: str = Depends(verify_organization),
    token: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """Toggle socials database access for the organization."""
    try:
        # Get or create organization settings
        query = select(OrganizationSettings).where(
            OrganizationSettings.organization_id == organization_id
        )
        result = await db.execute(query)
        settings = result.scalar_one_or_none()

        if settings:
            # Update existing settings
            settings.socials_database_enabled = toggle_data.enabled
        else:
            # Create new settings
            settings = OrganizationSettings(
                organization_id=organization_id,
                knowledgebase_enabled=False,
                socials_database_enabled=toggle_data.enabled
            )
            db.add(settings)

        await db.commit()
        await db.refresh(settings)

        status = "enabled" if toggle_data.enabled else "disabled"
        logger.info(f"Socials database {status} for organization {organization_id}")

        return {
            "success": True,
            "message": f"Socials database {status} for organization",
            "enabled": toggle_data.enabled,
            "organization_id": organization_id
        }

    except Exception as e:
        logger.error(f"Error toggling socials database: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="Error toggling socials database access")


@router.get("/socials-database/status")
async def get_socials_database_status(
    organization_id: str = Depends(verify_organization),
    token: dict = Depends(get_current_user),
):
    """Get socials database availability status."""
    try:
        auth_token = token["raw_token"]
        status = await intelligent_socials_service.get_socials_status(organization_id, auth_token)
        return status

    except Exception as e:
        logger.error(f"Error checking socials database status: {e}")
        raise HTTPException(status_code=500, detail="Error checking socials database status")
