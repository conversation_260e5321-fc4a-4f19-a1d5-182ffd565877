"""
API routes for research management, competitor monitoring, and trend analysis.
"""

from typing import List, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from app.database.database import get_db
from app.models.models import (
    ResearchProject, CompetitorProfile, ResearchFinding, 
    TrendAnalysis, ResearchReport, ResearchAlert
)
from app.models.schemas import (
    ResearchProjectCreate, ResearchProjectUpdate, ResearchProjectResponse,
    CompetitorProfileCreate, CompetitorProfileUpdate, CompetitorProfileResponse,
    ResearchFindingCreate, ResearchFindingUpdate, ResearchFindingResponse,
    TrendAnalysisCreate, TrendAnalysisResponse,
    ResearchReportCreate, ResearchReportUpdate, ResearchReportResponse,
    ResearchAlertResponse, ResearchAlertUpdate
)
from app.services.intelligent_router import intelligent_router
from app.agents.sample_agents import DeepResearchAgent
from app.utils.logger import get_logger
from app.utils.auth import get_user_id, verify_organization

logger = get_logger(__name__)
router = APIRouter(prefix="/research", tags=["research"])


# Research Projects
@router.post("/projects", response_model=ResearchProjectResponse)
async def create_research_project(
    project_data: ResearchProjectCreate,
    organization_id: str = Depends(verify_organization),
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """Create a new research project."""
    try:
        project = ResearchProject(
            name=project_data.name,
            description=project_data.description,
            organization_id=organization_id,
            created_by=user_id,
            project_type=project_data.project_type,
            configuration=project_data.configuration,
            is_active=project_data.is_active
        )
        
        db.add(project)
        await db.commit()
        await db.refresh(project)
        
        logger.info(f"Created research project: {project.name} (ID: {project.id})")
        return project
        
    except Exception as e:
        logger.error(f"Error creating research project: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create research project"
        )


@router.get("/projects", response_model=List[ResearchProjectResponse])
async def list_research_projects(
    organization_id: str = Depends(verify_organization),
    project_type: Optional[str] = Query(None),
    is_active: bool = Query(True),
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """List research projects for the organization."""
    try:
        conditions = [
            ResearchProject.organization_id == organization_id,
            ResearchProject.is_active == is_active
        ]
        
        if project_type:
            conditions.append(ResearchProject.project_type == project_type)
        
        result = await db.execute(
            select(ResearchProject).where(and_(*conditions))
        )
        projects = result.scalars().all()
        
        return projects
        
    except Exception as e:
        logger.error(f"Error listing research projects: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve research projects"
        )


# Competitor Profiles
@router.post("/competitors", response_model=CompetitorProfileResponse)
async def create_competitor_profile(
    competitor_data: CompetitorProfileCreate,
    organization_id: str = Depends(verify_organization),
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """Create a new competitor profile."""
    try:
        competitor = CompetitorProfile(
            project_id=competitor_data.project_id,
            organization_id=organization_id,
            competitor_name=competitor_data.competitor_name,
            industry=competitor_data.industry,
            website=competitor_data.website,
            description=competitor_data.description,
            keywords=competitor_data.keywords,
            monitoring_frequency=competitor_data.monitoring_frequency,
            profile_data=competitor_data.profile_data,
            is_active=competitor_data.is_active
        )
        
        db.add(competitor)
        await db.commit()
        await db.refresh(competitor)
        
        logger.info(f"Created competitor profile: {competitor.competitor_name}")
        return competitor
        
    except Exception as e:
        logger.error(f"Error creating competitor profile: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create competitor profile"
        )


@router.get("/competitors", response_model=List[CompetitorProfileResponse])
async def list_competitor_profiles(
    organization_id: str = Depends(verify_organization),
    project_id: Optional[UUID] = Query(None),
    is_active: bool = Query(True),
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """List competitor profiles for the organization."""
    try:
        conditions = [
            CompetitorProfile.organization_id == organization_id,
            CompetitorProfile.is_active == is_active
        ]
        
        if project_id:
            conditions.append(CompetitorProfile.project_id == project_id)
        
        result = await db.execute(
            select(CompetitorProfile).where(and_(*conditions))
        )
        competitors = result.scalars().all()
        
        return competitors
        
    except Exception as e:
        logger.error(f"Error listing competitor profiles: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve competitor profiles"
        )


# Research Execution
@router.post("/execute")
async def execute_research(
    request: dict,
    background_tasks: BackgroundTasks,
    organization_id: str = Depends(verify_organization),
    user_id: str = Depends(get_user_id)
):
    """Execute intelligent research using the Deep Research Agent."""
    try:
        research_query = request.get("query", "")
        if not research_query:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Research query is required"
            )
        
        # Use intelligent router to analyze the request
        routing_decision = await intelligent_router.route_research_request(research_query)
        
        # Create Deep Research Agent instance
        research_agent = DeepResearchAgent()
        
        # Execute research with context
        context = {
            "organization_id": organization_id,
            "user_id": user_id,
            "routing_decision": routing_decision
        }
        
        # Execute research
        result = await research_agent.process_message(research_query, context)
        
        return {
            "query": research_query,
            "research_type": routing_decision["research_type"],
            "confidence": routing_decision["confidence"],
            "result": result,
            "execution_plan": routing_decision["execution_plan"],
            "estimated_duration": routing_decision["estimated_duration"],
            "priority": routing_decision["priority"]
        }
        
    except Exception as e:
        logger.error(f"Error executing research: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to execute research"
        )


# Research Findings
@router.get("/findings", response_model=List[ResearchFindingResponse])
async def list_research_findings(
    organization_id: str = Depends(verify_organization),
    project_id: Optional[UUID] = Query(None),
    competitor_id: Optional[UUID] = Query(None),
    finding_type: Optional[str] = Query(None),
    is_significant: Optional[bool] = Query(None),
    limit: int = Query(50, le=100),
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """List research findings for the organization."""
    try:
        conditions = [ResearchFinding.organization_id == organization_id]
        
        if project_id:
            conditions.append(ResearchFinding.project_id == project_id)
        if competitor_id:
            conditions.append(ResearchFinding.competitor_id == competitor_id)
        if finding_type:
            conditions.append(ResearchFinding.finding_type == finding_type)
        if is_significant is not None:
            conditions.append(ResearchFinding.is_significant == is_significant)
        
        result = await db.execute(
            select(ResearchFinding)
            .where(and_(*conditions))
            .order_by(ResearchFinding.created_at.desc())
            .limit(limit)
        )
        findings = result.scalars().all()
        
        return findings
        
    except Exception as e:
        logger.error(f"Error listing research findings: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve research findings"
        )


# Trend Analysis
@router.post("/trends", response_model=TrendAnalysisResponse)
async def create_trend_analysis(
    trend_data: TrendAnalysisCreate,
    organization_id: str = Depends(verify_organization),
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """Create a new trend analysis."""
    try:
        trend_analysis = TrendAnalysis(
            organization_id=organization_id,
            topic=trend_data.topic,
            time_period=trend_data.time_period,
            region=trend_data.region,
            analysis_type=trend_data.analysis_type,
            trend_data=trend_data.trend_data,
            key_insights=trend_data.key_insights,
            growth_indicators=trend_data.growth_indicators,
            emerging_themes=trend_data.emerging_themes,
            confidence_score=trend_data.confidence_score,
            data_sources_count=trend_data.data_sources_count,
            created_by=user_id
        )
        
        db.add(trend_analysis)
        await db.commit()
        await db.refresh(trend_analysis)
        
        logger.info(f"Created trend analysis: {trend_analysis.topic}")
        return trend_analysis
        
    except Exception as e:
        logger.error(f"Error creating trend analysis: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create trend analysis"
        )


# Research Reports
@router.post("/reports", response_model=ResearchReportResponse)
async def create_research_report(
    report_data: ResearchReportCreate,
    organization_id: str = Depends(verify_organization),
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """Create a new research report."""
    try:
        report = ResearchReport(
            organization_id=organization_id,
            title=report_data.title,
            report_type=report_data.report_type,
            executive_summary=report_data.executive_summary,
            content=report_data.content,
            findings_summary=report_data.findings_summary,
            recommendations=report_data.recommendations,
            data_sources=report_data.data_sources,
            report_metadata=report_data.report_metadata,
            generated_by=user_id,
            is_published=report_data.is_published
        )
        
        db.add(report)
        await db.commit()
        await db.refresh(report)
        
        logger.info(f"Created research report: {report.title}")
        return report
        
    except Exception as e:
        logger.error(f"Error creating research report: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create research report"
        )


@router.get("/reports", response_model=List[ResearchReportResponse])
async def list_research_reports(
    organization_id: str = Depends(verify_organization),
    report_type: Optional[str] = Query(None),
    is_published: Optional[bool] = Query(None),
    limit: int = Query(20, le=50),
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """List research reports for the organization."""
    try:
        conditions = [ResearchReport.organization_id == organization_id]
        
        if report_type:
            conditions.append(ResearchReport.report_type == report_type)
        if is_published is not None:
            conditions.append(ResearchReport.is_published == is_published)
        
        result = await db.execute(
            select(ResearchReport)
            .where(and_(*conditions))
            .order_by(ResearchReport.created_at.desc())
            .limit(limit)
        )
        reports = result.scalars().all()
        
        return reports
        
    except Exception as e:
        logger.error(f"Error listing research reports: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve research reports"
        )
