from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from typing import List, Optional, Annotated
from uuid import UUID

from app.database.database import get_db
from app.models.models import CustomChatbot, Chatbot<PERSON>onversation, ChatbotMessage
from app.models.schemas import (
    ChatbotChatRequest, ChatbotChatResponse, ChatbotConversationCreate,
    ChatbotConversationResponse, ChatbotConversationListItem
)
from app.utils.auth import verify_organization, get_user_id
from app.chatbots.chatbot_factory import chatbot_factory
from app.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()


@router.post("/chat", response_model=ChatbotChatResponse)
async def chat_with_chatbot(
    chat_request: ChatbotChatRequest,
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """Chat with a specific chatbot."""
    try:
        # Verify chatbot exists and user has access
        chatbot_query = select(CustomChatbot).where(
            and_(
                CustomChatbot.id == chat_request.chatbot_id,
                CustomChatbot.is_active == True,
                (CustomChatbot.organization_id == organization_id) | (CustomChatbot.is_sample == True)
            )
        )
        
        chatbot_result = await db.execute(chatbot_query)
        chatbot_record = chatbot_result.scalar_one_or_none()
        
        if not chatbot_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Chatbot not found or access denied"
            )

        # Get or create conversation
        conversation = None
        if chat_request.conversation_id:
            # Get existing conversation
            conv_query = select(ChatbotConversation).where(
                and_(
                    ChatbotConversation.id == chat_request.conversation_id,
                    ChatbotConversation.chatbot_id == chat_request.chatbot_id,
                    ChatbotConversation.user_id == user_id,
                    ChatbotConversation.is_active == True
                )
            )
            conv_result = await db.execute(conv_query)
            conversation = conv_result.scalar_one_or_none()
            
            if not conversation:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Conversation not found"
                )
        else:
            # Create new conversation
            conversation = ChatbotConversation(
                chatbot_id=chat_request.chatbot_id,
                user_id=user_id,
                title=chat_request.message[:50] + "..." if len(chat_request.message) > 50 else chat_request.message
            )
            db.add(conversation)
            await db.commit()
            await db.refresh(conversation)

        # Get conversation history
        history_query = select(ChatbotMessage).where(
            ChatbotMessage.conversation_id == conversation.id
        ).order_by(ChatbotMessage.created_at.asc())
        
        history_result = await db.execute(history_query)
        history_messages = history_result.scalars().all()
        
        # Convert to conversation history format
        conversation_history = []
        for msg in history_messages:
            conversation_history.append({
                "role": msg.role,
                "content": msg.content
            })

        # Save user message
        user_message = ChatbotMessage(
            conversation_id=conversation.id,
            role="user",
            content=chat_request.message
        )
        db.add(user_message)

        # Get chatbot instance and process message
        chatbot_instance = await chatbot_factory.get_chatbot(db, chat_request.chatbot_id)
        if not chatbot_instance:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to load chatbot"
            )

        # Prepare context for chatbot
        context = {
            "conversation_history": conversation_history,
            "organization_id": organization_id,
            "user_id": user_id,
            "db": db,
            "auth_token": None  # Add auth token if needed
        }

        # Process message with chatbot
        response_content = await chatbot_instance.process_message(chat_request.message, context)

        # Save chatbot response
        chatbot_message = ChatbotMessage(
            conversation_id=conversation.id,
            role="assistant",
            content=response_content
        )
        db.add(chatbot_message)
        
        await db.commit()

        logger.info(f"Chat completed for chatbot {chat_request.chatbot_id} in conversation {conversation.id}")

        return ChatbotChatResponse(
            message=response_content,
            conversation_id=conversation.id,
            chatbot_name=chatbot_record.name,
            response_metadata={}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in chatbot chat: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process chat request"
        )


@router.get("/conversations", response_model=List[ChatbotConversationListItem])
async def list_chatbot_conversations(
    organization_id: Annotated[str, Depends(verify_organization)],
    chatbot_id: Optional[UUID] = None,
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """List user's chatbot conversations."""
    try:
        conditions = [
            ChatbotConversation.user_id == user_id,
            ChatbotConversation.is_active == True
        ]
        
        if chatbot_id:
            conditions.append(ChatbotConversation.chatbot_id == chatbot_id)

        query = (
            select(ChatbotConversation)
            .where(and_(*conditions))
            .order_by(ChatbotConversation.updated_at.desc())
        )
        
        result = await db.execute(query)
        conversations = result.scalars().all()

        return conversations

    except Exception as e:
        logger.error(f"Error listing chatbot conversations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list conversations"
        )


@router.get("/conversations/{conversation_id}", response_model=ChatbotConversationResponse)
async def get_chatbot_conversation(
    conversation_id: UUID,
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """Get a specific chatbot conversation with messages."""
    try:
        # Get conversation
        conv_query = select(ChatbotConversation).where(
            and_(
                ChatbotConversation.id == conversation_id,
                ChatbotConversation.user_id == user_id,
                ChatbotConversation.is_active == True
            )
        )
        
        conv_result = await db.execute(conv_query)
        conversation = conv_result.scalar_one_or_none()
        
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )

        # Get messages
        messages_query = select(ChatbotMessage).where(
            ChatbotMessage.conversation_id == conversation_id
        ).order_by(ChatbotMessage.created_at.asc())
        
        messages_result = await db.execute(messages_query)
        messages = messages_result.scalars().all()

        # Convert to response format
        conversation_dict = {
            "id": conversation.id,
            "chatbot_id": conversation.chatbot_id,
            "user_id": conversation.user_id,
            "title": conversation.title,
            "is_active": conversation.is_active,
            "created_at": conversation.created_at,
            "updated_at": conversation.updated_at,
            "messages": [
                {
                    "id": msg.id,
                    "conversation_id": msg.conversation_id,
                    "role": msg.role,
                    "content": msg.content,
                    "message_metadata": msg.message_metadata,
                    "created_at": msg.created_at
                }
                for msg in messages
            ]
        }

        return ChatbotConversationResponse(**conversation_dict)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting chatbot conversation {conversation_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get conversation"
        )


@router.delete("/conversations/{conversation_id}")
async def delete_chatbot_conversation(
    conversation_id: UUID,
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """Delete a chatbot conversation (soft delete)."""
    try:
        # Get conversation
        conv_query = select(ChatbotConversation).where(
            and_(
                ChatbotConversation.id == conversation_id,
                ChatbotConversation.user_id == user_id,
                ChatbotConversation.is_active == True
            )
        )
        
        conv_result = await db.execute(conv_query)
        conversation = conv_result.scalar_one_or_none()
        
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )

        # Soft delete
        conversation.is_active = False
        await db.commit()

        logger.info(f"Deleted chatbot conversation {conversation_id}")
        return {"message": "Conversation deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting chatbot conversation {conversation_id}: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete conversation"
        )
