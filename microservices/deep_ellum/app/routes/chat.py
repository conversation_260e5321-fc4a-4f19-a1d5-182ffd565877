from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, or_
from typing import List, Optional, Dict, Any, Annotated
from uuid import UUID

from app.database.database import get_db
from app.models.models import CustomAgent, AgentConversation, ConversationMessage
from app.models.schemas import (
    ChatRequest, ChatResponse, AgentConversationCreate, AgentConversationResponse,
    ConversationMessageResponse, ConversationListResponse, MessageRole, AgentConversationListItem
)
from app.utils.auth import get_user_id, verify_organization, check_permissions, get_current_user
from app.utils.logger import get_logger
from app.agents.agent_factory import agent_factory
from app.services.agent_context_service import agent_context_service

logger = get_logger(__name__)
router = APIRouter()


@router.post("/", response_model=ChatResponse)
async def chat_with_agent(
    chat_request: ChatRequest,
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id),
    token: dict = Depends(get_current_user)
):
    """Send a message to an agent and get a response."""
    try:
        # Check permissions for chatting with agents
        # await check_permissions(user_id, organization_id, "chat_with_agent")

        # Verify agent exists and user has access
        agent_query = select(CustomAgent).where(
            and_(
                CustomAgent.id == chat_request.agent_id,
                or_(
                    CustomAgent.organization_id == organization_id,
                    CustomAgent.is_sample == True
                ),
                CustomAgent.is_active == True
            )
        )

        agent_result = await db.execute(agent_query)
        agent_record = agent_result.scalar_one_or_none()

        if not agent_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found or access denied"
            )
        
        # Get or create conversation
        conversation = None
        if chat_request.conversation_id:
            conv_query = select(AgentConversation).where(
                and_(
                    AgentConversation.id == chat_request.conversation_id,
                    AgentConversation.user_id == user_id,
                    AgentConversation.agent_id == chat_request.agent_id,
                    AgentConversation.is_active == True
                )
            )
            conv_result = await db.execute(conv_query)
            conversation = conv_result.scalar_one_or_none()
        
        if not conversation:
            # Create new conversation
            conversation = AgentConversation(
                agent_id=chat_request.agent_id,
                user_id=user_id,
                title=chat_request.message[:100] + "..." if len(chat_request.message) > 100 else chat_request.message
            )
            db.add(conversation)
            await db.commit()
            await db.refresh(conversation)
        
        # Get conversation history
        history_query = (
            select(ConversationMessage)
            .where(ConversationMessage.conversation_id == conversation.id)
            .order_by(ConversationMessage.created_at)
            .limit(20)  # Last 20 messages for context
        )
        
        history_result = await db.execute(history_query)
        history_messages = history_result.scalars().all()
        
        # Convert to format expected by agent
        conversation_history = [
            {
                "role": msg.role,
                "content": msg.content
            }
            for msg in history_messages
        ]
        
        # Save user message
        user_message = ConversationMessage(
            conversation_id=conversation.id,
            role=MessageRole.USER,
            content=chat_request.message
        )
        db.add(user_message)
        
        # Get agent instance and process message
        agent = await agent_factory.get_agent(db, chat_request.agent_id)
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to load agent"
            )
        
        # Get auth token
        auth_token = token["raw_token"]

        # Process message with conversation context
        context = {
            "conversation_history": conversation_history,
            "conversation_id": str(conversation.id),
            "user_id": user_id,
            "organization_id": organization_id,
            "auth_token": auth_token,
            "db": db
        }

        # Enhance message with knowledgebase if enabled
        enhanced_message = await agent_context_service.enhance_message_with_knowledge(
            message=chat_request.message,
            organization_id=organization_id,
            auth_token=auth_token,
            db=db
        )

        response_content = await agent.process_message(enhanced_message, context)
        
        # Save assistant response
        assistant_message = ConversationMessage(
            conversation_id=conversation.id,
            role=MessageRole.ASSISTANT,
            content=response_content
        )
        db.add(assistant_message)
        
        await db.commit()
        
        logger.info(f"Chat completed for agent {agent_record.name} in conversation {conversation.id}")
        
        return ChatResponse(
            message=response_content,
            conversation_id=conversation.id,
            agent_name=agent_record.name,
            response_metadata={
                "agent_id": str(chat_request.agent_id),
                "message_count": len(conversation_history) + 2
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in chat: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process chat message"
        )



@router.get("/conversations", response_model=ConversationListResponse)
async def list_conversations(
    organization_id: Annotated[str, Depends(verify_organization)],
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    agent_id: Optional[UUID] = Query(None),
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """List user's conversations within their organization."""
    try:
        # Check permissions for viewing conversations
        # await check_permissions(user_id, organization_id, "view_conversations")

        # Build query conditions
        conditions = [
            AgentConversation.user_id == user_id,
            AgentConversation.is_active == True
        ]

        if agent_id:
            # Verify agent belongs to organization or is a sample
            agent_check = select(CustomAgent).where(
                and_(
                    CustomAgent.id == agent_id,
                    or_(
                        CustomAgent.organization_id == organization_id,
                        CustomAgent.is_sample == True
                    )
                )
            )
            agent_result = await db.execute(agent_check)
            if not agent_result.scalar_one_or_none():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Agent not found or access denied"
                )
            conditions.append(AgentConversation.agent_id == agent_id)

        # Get total count
        from sqlalchemy import func
        count_query = select(func.count(AgentConversation.id)).where(and_(*conditions))
        total_result = await db.execute(count_query)
        total = total_result.scalar()

        # Get conversations with pagination
        offset = (page - 1) * size
        query = (
            select(AgentConversation)
            .where(and_(*conditions))
            .order_by(desc(AgentConversation.updated_at))
            .offset(offset)
            .limit(size)
        )

        result = await db.execute(query)
        conversations = result.scalars().all()

        return ConversationListResponse(
            conversations=conversations,
            total=total,
            page=page,
            size=size
        )

    except Exception as e:
        logger.error(f"Error listing conversations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list conversations"
        )


@router.get("/conversations/{conversation_id}", response_model=AgentConversationResponse)
async def get_conversation(
    conversation_id: UUID,
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """Get a specific conversation with its messages."""
    try:
        # Check permissions for viewing conversations
        # await check_permissions(user_id, organization_id, "view_conversations")

        # Get conversation
        conv_query = select(AgentConversation).where(
            and_(
                AgentConversation.id == conversation_id,
                AgentConversation.user_id == user_id,
                AgentConversation.is_active == True
            )
        )
        
        conv_result = await db.execute(conv_query)
        conversation = conv_result.scalar_one_or_none()
        
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )
        
        # Get messages
        messages_query = (
            select(ConversationMessage)
            .where(ConversationMessage.conversation_id == conversation_id)
            .order_by(ConversationMessage.created_at)
        )
        
        messages_result = await db.execute(messages_query)
        messages = messages_result.scalars().all()
        
        # Create response with messages
        conversation_dict = {
            "id": conversation.id,
            "agent_id": conversation.agent_id,
            "user_id": conversation.user_id,
            "title": conversation.title,
            "is_active": conversation.is_active,
            "created_at": conversation.created_at,
            "updated_at": conversation.updated_at,
            "messages": messages
        }
        
        return AgentConversationResponse(**conversation_dict)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting conversation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get conversation"
        )


@router.delete("/conversations/{conversation_id}")
async def delete_conversation(
    conversation_id: UUID,
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """Delete a conversation (soft delete)."""
    try:
        # Check permissions for deleting conversations
        # await check_permissions(user_id, organization_id, "delete_conversation")

        # Get conversation
        conv_query = select(AgentConversation).where(
            and_(
                AgentConversation.id == conversation_id,
                AgentConversation.user_id == user_id,
                AgentConversation.is_active == True
            )
        )
        
        conv_result = await db.execute(conv_query)
        conversation = conv_result.scalar_one_or_none()
        
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )
        
        # Soft delete
        conversation.is_active = False
        await db.commit()
        
        logger.info(f"Deleted conversation: {conversation_id}")
        return {"message": "Conversation deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting conversation: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete conversation"
        )


@router.get("/agent/{agent_id}/knowledgebase-status")
async def get_agent_knowledgebase_status(
    agent_id: UUID,
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id),
    token: dict = Depends(get_current_user)
):
    """Get knowledgebase status for a specific agent."""
    try:
        # Verify agent exists and user has access
        agent_query = select(CustomAgent).where(
            and_(
                CustomAgent.id == agent_id,
                or_(
                    CustomAgent.organization_id == organization_id,
                    CustomAgent.is_sample == True
                ),
                CustomAgent.is_active == True
            )
        )

        agent_result = await db.execute(agent_query)
        agent_record = agent_result.scalar_one_or_none()

        if not agent_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found or access denied"
            )

        # Get knowledgebase status
        from app.services.intelligent_knowledge_service import intelligent_knowledge_service
        from app.services.intelligent_socials_service import intelligent_socials_service
        auth_token = token["raw_token"]
        kb_status = await intelligent_knowledge_service.get_knowledge_status(organization_id, auth_token)
        socials_status = await intelligent_socials_service.get_socials_status(organization_id, auth_token)

        # Check if knowledgebase and socials database are enabled for organization
        kb_enabled = await agent_context_service.is_knowledgebase_enabled(db, organization_id)
        socials_enabled = await agent_context_service.is_socials_database_enabled(db, organization_id)

        return {
            "agent_id": str(agent_id),
            "agent_name": agent_record.name,
            "knowledgebase_enabled": kb_enabled,
            "knowledgebase_status": kb_status,
            "socials_database_enabled": socials_enabled,
            "socials_database_status": socials_status
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting knowledgebase status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving knowledgebase status"
        )


