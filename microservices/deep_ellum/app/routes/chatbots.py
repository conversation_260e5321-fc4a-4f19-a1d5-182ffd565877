from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from typing import List, Optional, Annotated
from uuid import UUID

from app.database.database import get_db
from app.models.models import CustomChatbot, AgentTemplate
from app.models.schemas import (
    CustomChatbotCreate, CustomChatbotUpdate, CustomChatbotResponse,
    ChatbotListResponse, AgentTemplateResponse, AgentCapability
)
from app.utils.auth import verify_organization, get_user_id
from app.services.gemini_service import gemini_service
from app.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()


@router.post("/", response_model=CustomChatbotResponse)
async def create_chatbot(
    chatbot_data: CustomChatbotCreate,
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """Create a new custom chatbot."""
    try:
        # Check permissions for creating chatbots
        # await check_permissions(user_id, organization_id, "create_chatbot")

        # Generate enhanced instructions if needed
        if len(chatbot_data.instructions.strip()) < 50:
            logger.info("Generating enhanced instructions for chatbot")
            enhanced_instructions = await gemini_service.generate_agent_instructions(
                chatbot_data.description,
                chatbot_data.personality
            )
            chatbot_data.instructions = enhanced_instructions

        # Create chatbot record
        chatbot = CustomChatbot(
            name=chatbot_data.name,
            description=chatbot_data.description,
            personality=chatbot_data.personality,
            instructions=chatbot_data.instructions,
            capabilities=[cap.value for cap in chatbot_data.capabilities] if chatbot_data.capabilities else [],
            is_active=chatbot_data.is_active,
            created_by=user_id,
            organization_id=organization_id
        )

        db.add(chatbot)
        await db.commit()
        await db.refresh(chatbot)

        # Invalidate chatbot factory cache to ensure new chatbot is immediately available
        from app.chatbots.chatbot_factory import chatbot_factory
        chatbot_factory.invalidate_registry_cache()

        logger.info(f"Created chatbot: {chatbot.name} (ID: {chatbot.id}) for organization: {organization_id}")
        return chatbot

    except Exception as e:
        logger.error(f"Error creating chatbot: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create chatbot"
        )


@router.get("/", response_model=ChatbotListResponse)
async def list_chatbots(
    organization_id: Annotated[str, Depends(verify_organization)],
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    include_samples: bool = Query(True),
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """List organization's custom chatbots and optionally sample chatbots."""
    try:
        # Check permissions for viewing chatbots
        # await check_permissions(user_id, organization_id, "view_chatbots")

        # Build query conditions
        conditions = [CustomChatbot.is_active == True]
        
        if include_samples:
            # Include both organization chatbots and sample chatbots
            conditions.append(
                or_(
                    CustomChatbot.organization_id == organization_id,
                    CustomChatbot.is_sample == True
                )
            )
        else:
            # Only organization-specific chatbots
            conditions.append(CustomChatbot.organization_id == organization_id)

        # Get total count
        count_query = select(func.count(CustomChatbot.id)).where(and_(*conditions))
        total_result = await db.execute(count_query)
        total = total_result.scalar()

        # Get paginated results
        offset = (page - 1) * size
        query = (
            select(CustomChatbot)
            .where(and_(*conditions))
            .order_by(CustomChatbot.created_at.desc())
            .offset(offset)
            .limit(size)
        )
        
        result = await db.execute(query)
        chatbots = result.scalars().all()

        return ChatbotListResponse(
            chatbots=chatbots,
            total=total,
            page=page,
            size=size
        )

    except Exception as e:
        logger.error(f"Error listing chatbots: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list chatbots"
        )


@router.get("/{chatbot_id}", response_model=CustomChatbotResponse)
async def get_chatbot(
    chatbot_id: UUID,
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """Get a specific chatbot by ID."""
    try:
        # Check permissions for viewing chatbots
        # await check_permissions(user_id, organization_id, "view_chatbot")

        query = select(CustomChatbot).where(
            and_(
                CustomChatbot.id == chatbot_id,
                or_(
                    CustomChatbot.organization_id == organization_id,
                    CustomChatbot.is_sample == True
                ),
                CustomChatbot.is_active == True
            )
        )
        
        result = await db.execute(query)
        chatbot = result.scalar_one_or_none()
        
        if not chatbot:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Chatbot not found"
            )
        
        return chatbot

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting chatbot {chatbot_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get chatbot"
        )


@router.put("/{chatbot_id}", response_model=CustomChatbotResponse)
async def update_chatbot(
    chatbot_id: UUID,
    chatbot_data: CustomChatbotUpdate,
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """Update an existing chatbot."""
    try:
        # Check permissions for updating chatbots
        # await check_permissions(user_id, organization_id, "update_chatbot")

        # Get chatbot
        query = select(CustomChatbot).where(
            and_(
                CustomChatbot.id == chatbot_id,
                CustomChatbot.organization_id == organization_id,
                CustomChatbot.is_sample == False,  # Can't update sample chatbots
                CustomChatbot.is_active == True
            )
        )
        
        result = await db.execute(query)
        chatbot = result.scalar_one_or_none()
        
        if not chatbot:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Chatbot not found or cannot be updated"
            )

        # Update fields
        update_data = chatbot_data.model_dump(exclude_unset=True)
        
        # Convert capabilities to list of strings if provided
        if "capabilities" in update_data and update_data["capabilities"] is not None:
            update_data["capabilities"] = [cap.value for cap in update_data["capabilities"]]
        
        for field, value in update_data.items():
            setattr(chatbot, field, value)

        await db.commit()
        await db.refresh(chatbot)

        # Invalidate chatbot factory cache
        from app.chatbots.chatbot_factory import chatbot_factory
        chatbot_factory.invalidate_registry_cache()

        logger.info(f"Updated chatbot: {chatbot.name} (ID: {chatbot.id})")
        return chatbot

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating chatbot {chatbot_id}: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update chatbot"
        )


@router.delete("/{chatbot_id}")
async def delete_chatbot(
    chatbot_id: UUID,
    organization_id: Annotated[str, Depends(verify_organization)],
    db: AsyncSession = Depends(get_db),
    user_id: str = Depends(get_user_id)
):
    """Delete a chatbot (soft delete by setting is_active=False)."""
    try:
        # Check permissions for deleting chatbots
        # await check_permissions(user_id, organization_id, "delete_chatbot")

        # Get chatbot
        query = select(CustomChatbot).where(
            and_(
                CustomChatbot.id == chatbot_id,
                CustomChatbot.organization_id == organization_id,
                CustomChatbot.is_sample == False,  # Can't delete sample chatbots
                CustomChatbot.is_active == True
            )
        )
        
        result = await db.execute(query)
        chatbot = result.scalar_one_or_none()
        
        if not chatbot:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Chatbot not found or cannot be deleted"
            )

        # Soft delete
        chatbot.is_active = False
        await db.commit()

        # Invalidate chatbot factory cache
        from app.chatbots.chatbot_factory import chatbot_factory
        chatbot_factory.invalidate_registry_cache()

        logger.info(f"Deleted chatbot: {chatbot.name} (ID: {chatbot.id})")
        return {"message": "Chatbot deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting chatbot {chatbot_id}: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete chatbot"
        )
