"""
MCP API routes for EllumAI agents.

This module provides REST API endpoints for interacting with the MCP server
and accessing research and calendar agent capabilities.
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field

from app.utils.logger import get_logger
from app.mcp_server.integration import get_mcp_integration
from app.mcp_server.client import MC<PERSON>lient
from app.core.config import settings

logger = get_logger(__name__)

router = APIRouter()


# Request/Response Models
class MCPToolRequest(BaseModel):
    """Request model for MCP tool calls."""
    tool_name: str = Field(description="Name of the MCP tool to call")
    parameters: Dict[str, Any] = Field(description="Tool parameters")


class MCPResourceRequest(BaseModel):
    """Request model for MCP resource access."""
    resource_uri: str = Field(description="URI of the resource to access")


class MCPResponse(BaseModel):
    """Response model for MCP operations."""
    success: bool = Field(description="Whether the operation was successful")
    data: Any = Field(description="Response data")
    error: Optional[str] = Field(default=None, description="Error message if any")


class ServerStatusResponse(BaseModel):
    """Response model for server status."""
    mcp_enabled: bool = Field(description="Whether MCP is enabled")
    server_running: bool = Field(description="Whether MCP server is running")
    host: str = Field(description="MCP server host")
    port: int = Field(description="MCP server port")


# Dependency to get MCP integration
async def get_mcp_integration_dep():
    """Dependency to get MCP integration instance."""
    try:
        return await get_mcp_integration()
    except Exception as e:
        logger.error(f"Error getting MCP integration: {e}")
        raise HTTPException(status_code=500, detail="MCP integration not available")


@router.get("/status", response_model=ServerStatusResponse)
async def get_mcp_status(
    integration = Depends(get_mcp_integration_dep)
):
    """Get MCP server status."""
    try:
        return ServerStatusResponse(
            mcp_enabled=settings.MCP_ENABLED,
            server_running=integration.is_server_running(),
            host=settings.MCP_HOST,
            port=settings.MCP_PORT
        )
    except Exception as e:
        logger.error(f"Error getting MCP status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tools", response_model=MCPResponse)
async def list_mcp_tools(
    integration = Depends(get_mcp_integration_dep)
):
    """List available MCP tools."""
    try:
        if not integration.is_server_running():
            # If server not running, get tools from integration
            tools = []
            for agent_type in ["deep_research_agent", "content_calendar_agent"]:
                capabilities = await integration.get_agent_capabilities(agent_type)
                if "mcp_tools" in capabilities:
                    for tool in capabilities["mcp_tools"]:
                        tools.append({"name": tool, "agent": agent_type})
            return MCPResponse(success=True, data=tools)

        # If server is running, get from server
        server = await integration.get_mcp_server()
        tools = server.list_tools()
        return MCPResponse(success=True, data=tools)

    except Exception as e:
        logger.error(f"Error listing MCP tools: {e}")
        return MCPResponse(success=False, error=str(e))


@router.get("/resources", response_model=MCPResponse)
async def list_mcp_resources(
    integration = Depends(get_mcp_integration_dep)
):
    """List available MCP resources."""
    try:
        if not integration.is_server_running():
            # If server not running, get resources from integration
            resources = []
            for agent_type in ["deep_research_agent", "content_calendar_agent"]:
                capabilities = await integration.get_agent_capabilities(agent_type)
                if "mcp_resources" in capabilities:
                    for resource in capabilities["mcp_resources"]:
                        resources.append({"name": resource, "agent": agent_type})
            return MCPResponse(success=True, data=resources)

        # If server is running, get from server
        server = await integration.get_mcp_server()
        resources = server.list_resources()
        return MCPResponse(success=True, data=resources)

    except Exception as e:
        logger.error(f"Error listing MCP resources: {e}")
        return MCPResponse(success=False, error=str(e))


@router.post("/tools/call", response_model=MCPResponse)
async def call_mcp_tool(
    request: MCPToolRequest,
    integration = Depends(get_mcp_integration_dep)
):
    """Call an MCP tool."""
    try:
        # Use integration to call tool
        result = await integration.process_mcp_request(
            agent_type="auto",  # Will be determined by tool name
            tool_name=request.tool_name,
            parameters=request.parameters
        )

        if "error" in result:
            return MCPResponse(success=False, error=result["error"])

        return MCPResponse(success=True, data=result)

    except Exception as e:
        logger.error(f"Error calling MCP tool: {e}")
        return MCPResponse(success=False, error=str(e))


@router.post("/resources/get", response_model=MCPResponse)
async def get_mcp_resource(
    request: MCPResourceRequest,
    integration = Depends(get_mcp_integration_dep)
):
    """Get an MCP resource."""
    try:
        # Get server instance and call resource method
        server = await integration.get_mcp_server()
        result = await server.get_resource(request.resource_uri)

        if "error" in result:
            return MCPResponse(success=False, error=result["error"])

        return MCPResponse(success=True, data=result)

    except Exception as e:
        logger.error(f"Error getting MCP resource: {e}")
        return MCPResponse(success=False, error=str(e))


# Research Agent Endpoints
@router.post("/research/competitor-analysis", response_model=MCPResponse)
async def research_competitor_analysis(
    competitor_name: str,
    organization_id: str,
    user_id: str,
    keywords: List[str] = None,
    time_period: str = "1w",
    region: str = "",
    focus_areas: List[str] = None
):
    """Perform competitor analysis using the research agent."""
    try:
        async with MCPClient() as client:
            result = await client.competitor_analysis(
                competitor_name=competitor_name,
                organization_id=organization_id,
                user_id=user_id,
                keywords=keywords or [],
                time_period=time_period,
                region=region,
                focus_areas=focus_areas or []
            )
            
            if "error" in result:
                return MCPResponse(success=False, error=result["error"])
            
            return MCPResponse(success=True, data=result)
            
    except Exception as e:
        logger.error(f"Error in competitor analysis: {e}")
        return MCPResponse(success=False, error=str(e))


@router.post("/research/trend-analysis", response_model=MCPResponse)
async def research_trend_analysis(
    topic: str,
    organization_id: str,
    user_id: str,
    keywords: List[str] = None,
    time_period: str = "1m",
    region: str = "",
    sources: List[str] = None
):
    """Perform trend analysis using the research agent."""
    try:
        async with MCPClient() as client:
            result = await client.trend_analysis(
                topic=topic,
                organization_id=organization_id,
                user_id=user_id,
                keywords=keywords or [],
                time_period=time_period,
                region=region,
                sources=sources or []
            )
            
            if "error" in result:
                return MCPResponse(success=False, error=result["error"])
            
            return MCPResponse(success=True, data=result)
            
    except Exception as e:
        logger.error(f"Error in trend analysis: {e}")
        return MCPResponse(success=False, error=str(e))


# Calendar Agent Endpoints
@router.post("/calendar/content-planning", response_model=MCPResponse)
async def calendar_content_planning(
    topic: str,
    organization_id: str,
    user_id: str,
    auth_token: str,
    platforms: List[str] = None,
    content_types: List[str] = None,
    time_period: str = "1w",
    target_audience: str = ""
):
    """Plan content using the calendar agent."""
    try:
        async with MCPClient() as client:
            result = await client.content_planning(
                topic=topic,
                organization_id=organization_id,
                user_id=user_id,
                auth_token=auth_token,
                platforms=platforms or [],
                content_types=content_types or [],
                time_period=time_period,
                target_audience=target_audience
            )
            
            if "error" in result:
                return MCPResponse(success=False, error=result["error"])
            
            return MCPResponse(success=True, data=result)
            
    except Exception as e:
        logger.error(f"Error in content planning: {e}")
        return MCPResponse(success=False, error=str(e))


@router.post("/calendar/content-scheduling", response_model=MCPResponse)
async def calendar_content_scheduling(
    content: str,
    platform: str,
    organization_id: str,
    user_id: str,
    auth_token: str,
    scheduled_time: str = None,
    content_type: str = "post",
    auto_optimize_timing: bool = True
):
    """Schedule content using the calendar agent."""
    try:
        async with MCPClient() as client:
            result = await client.content_scheduling(
                content=content,
                platform=platform,
                organization_id=organization_id,
                user_id=user_id,
                auth_token=auth_token,
                scheduled_time=scheduled_time,
                content_type=content_type,
                auto_optimize_timing=auto_optimize_timing
            )
            
            if "error" in result:
                return MCPResponse(success=False, error=result["error"])
            
            return MCPResponse(success=True, data=result)
            
    except Exception as e:
        logger.error(f"Error in content scheduling: {e}")
        return MCPResponse(success=False, error=str(e))


# Resource Access Endpoints
@router.get("/research/reports/{organization_id}", response_model=MCPResponse)
async def get_research_reports(organization_id: str, report_type: str = None):
    """Get research reports resource."""
    try:
        async with MCPClient() as client:
            result = await client.get_research_reports(organization_id, report_type)
            
            if "error" in result:
                return MCPResponse(success=False, error=result["error"])
            
            return MCPResponse(success=True, data=result)
            
    except Exception as e:
        logger.error(f"Error getting research reports: {e}")
        return MCPResponse(success=False, error=str(e))


@router.get("/calendar/content/{organization_id}", response_model=MCPResponse)
async def get_content_calendar(organization_id: str, date_range: str = None):
    """Get content calendar resource."""
    try:
        async with MCPClient() as client:
            result = await client.get_content_calendar(organization_id, date_range)
            
            if "error" in result:
                return MCPResponse(success=False, error=result["error"])
            
            return MCPResponse(success=True, data=result)
            
    except Exception as e:
        logger.error(f"Error getting content calendar: {e}")
        return MCPResponse(success=False, error=str(e))
