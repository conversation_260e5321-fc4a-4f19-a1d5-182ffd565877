"""
API routes for AI provider management.
"""

from typing import Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

from app.services.ai_provider_service import ai_provider_service
from app.utils.auth import get_current_user
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/ai-provider", tags=["ai-provider"])


class ProviderSwitchRequest(BaseModel):
    """Request model for switching AI provider."""
    provider: str  # "openai" or "gemini"


class ProviderTestRequest(BaseModel):
    """Request model for testing AI provider."""
    provider: Optional[str] = None  # Test specific provider, defaults to current


@router.get("/info")
async def get_provider_info(token: dict = Depends(get_current_user)):
    """Get current AI provider information."""
    try:
        info = ai_provider_service.get_provider_info()
        return {
            "success": True,
            "data": info
        }
    except Exception as e:
        logger.error(f"Error getting provider info: {e}")
        raise HTTPException(status_code=500, detail="Error getting provider information")


@router.post("/switch")
async def switch_provider(
    request: ProviderSwitchRequest,
    token: dict = Depends(get_current_user)
):
    """Switch AI provider."""
    try:
        ai_provider_service.switch_provider(request.provider)
        
        return {
            "success": True,
            "message": f"Successfully switched to {request.provider}",
            "current_provider": request.provider
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error switching provider: {e}")
        raise HTTPException(status_code=500, detail="Error switching provider")


@router.post("/test")
async def test_provider(
    request: ProviderTestRequest,
    token: dict = Depends(get_current_user)
):
    """Test AI provider functionality."""
    try:
        result = ai_provider_service.test_provider(request.provider)
        
        return {
            "success": True,
            "data": result
        }
        
    except Exception as e:
        logger.error(f"Error testing provider: {e}")
        raise HTTPException(status_code=500, detail="Error testing provider")


@router.get("/test-all")
async def test_all_providers(token: dict = Depends(get_current_user)):
    """Test all available AI providers."""
    try:
        results = {}
        
        # Test both providers
        for provider in ["openai", "gemini"]:
            results[provider] = ai_provider_service.test_provider(provider)
        
        return {
            "success": True,
            "data": results
        }
        
    except Exception as e:
        logger.error(f"Error testing all providers: {e}")
        raise HTTPException(status_code=500, detail="Error testing providers")


@router.get("/quota-status")
async def get_quota_status(token: dict = Depends(get_current_user)):
    """Get quota status for current provider (if available)."""
    try:
        provider_info = ai_provider_service.get_provider_info()
        current_provider = provider_info["current_provider"]
        
        # Test current provider to see if it's working
        test_result = ai_provider_service.test_provider()
        
        status = {
            "provider": current_provider,
            "status": test_result["status"],
            "working": test_result["status"] == "success",
            "error": test_result.get("error"),
            "recommendations": []
        }
        
        # Add recommendations based on status
        if test_result["status"] == "failed":
            error_msg = test_result.get("error", "").lower()
            
            if "quota" in error_msg or "rate limit" in error_msg or "429" in error_msg:
                status["quota_exceeded"] = True
                status["recommendations"].append(
                    f"Switch to alternative provider to continue research"
                )
                
                # Suggest the other provider
                other_provider = "openai" if current_provider == "gemini" else "gemini"
                other_configured = provider_info.get(f"{other_provider}_configured", False)
                
                if other_configured:
                    status["recommendations"].append(
                        f"Try switching to {other_provider} provider"
                    )
                else:
                    status["recommendations"].append(
                        f"Configure {other_provider.upper()}_API_KEY in .env file"
                    )
            else:
                status["quota_exceeded"] = False
                status["recommendations"].append("Check API key configuration")
        else:
            status["quota_exceeded"] = False
            status["recommendations"].append("Provider is working normally")
        
        return {
            "success": True,
            "data": status
        }
        
    except Exception as e:
        logger.error(f"Error getting quota status: {e}")
        raise HTTPException(status_code=500, detail="Error getting quota status")
