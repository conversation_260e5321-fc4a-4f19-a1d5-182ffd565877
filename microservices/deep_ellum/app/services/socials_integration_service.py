"""
Socials Service Integration for Content Calendar Agent.
Handles communication with the socials_service for scheduling content and analytics.
"""

import httpx
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from app.core.config import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)


class SocialsIntegrationService:
    """Service for integrating with the socials_service for content scheduling and analytics."""

    def __init__(self):
        self.socials_service_url = getattr(settings, 'SOCIALS_SERVICE_URL', 'http://localhost:8005')
        self.timeout = 30.0

    async def get_connected_platforms(self, organization_id: str, auth_token: str) -> List[str]:
        """Get list of connected social media platforms for the organization."""
        try:
            url = f"{self.socials_service_url}/api/v1/socials/platforms"
            headers = {"Authorization": f"Bearer {auth_token}"}
            params = {"organisation_id": organization_id}

            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(url, params=params, headers=headers)

                if response.status_code == 200:
                    data = response.json()
                    # Extract connected platforms
                    connected_platforms = []
                    for account in data.get("accounts", []):
                        if account.get("login_status", False):
                            connected_platforms.append(account.get("platform"))
                    
                    logger.info(f"Found {len(connected_platforms)} connected platforms for org {organization_id}")
                    return connected_platforms
                else:
                    logger.warning(f"Failed to get platforms: {response.status_code}")
                    return []

        except Exception as e:
            logger.error(f"Error getting connected platforms: {e}")
            return []

    async def get_audience_insights(self, organization_id: str, auth_token: str, platform: str = None) -> Dict[str, Any]:
        """Get audience insights and engagement data for optimal timing analysis."""
        try:
            insights = {}
            
            # Get posting schedule analytics if available
            if platform == "twitter":
                insights["posting_schedule"] = await self._get_twitter_posting_insights(organization_id, auth_token)
            elif platform == "facebook":
                insights["audience_demographics"] = await self._get_facebook_audience_insights(organization_id, auth_token)
            
            # Get general engagement metrics
            insights["engagement_metrics"] = await self._get_engagement_metrics(organization_id, auth_token)
            
            return insights

        except Exception as e:
            logger.error(f"Error getting audience insights: {e}")
            return {}

    async def _get_twitter_posting_insights(self, organization_id: str, auth_token: str) -> Dict[str, Any]:
        """Get Twitter posting schedule insights."""
        try:
            # This would call the Twitter analytics endpoint
            # For now, return default optimal times based on general best practices
            return {
                "optimal_hours": [9, 12, 15, 18],  # 9 AM, 12 PM, 3 PM, 6 PM
                "optimal_days": [1, 2, 3, 4],  # Tuesday through Friday
                "engagement_peak": "12:00-15:00"
            }
        except Exception as e:
            logger.error(f"Error getting Twitter insights: {e}")
            return {}

    async def _get_facebook_audience_insights(self, organization_id: str, auth_token: str) -> Dict[str, Any]:
        """Get Facebook audience demographics and optimal posting times."""
        try:
            # This would call the Facebook analytics endpoint
            return {
                "optimal_hours": [10, 13, 16, 19],  # 10 AM, 1 PM, 4 PM, 7 PM
                "optimal_days": [2, 3, 4],  # Wednesday through Friday
                "engagement_peak": "13:00-16:00"
            }
        except Exception as e:
            logger.error(f"Error getting Facebook insights: {e}")
            return {}

    async def _get_engagement_metrics(self, organization_id: str, auth_token: str) -> Dict[str, Any]:
        """Get general engagement metrics across platforms."""
        try:
            url = f"{self.socials_service_url}/api/v1/socials/post-analytics"
            headers = {"Authorization": f"Bearer {auth_token}"}
            params = {"organisation_id": organization_id, "months": 3}

            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(url, params=params, headers=headers)

                if response.status_code == 200:
                    return response.json()
                else:
                    logger.warning(f"Failed to get engagement metrics: {response.status_code}")
                    return {}

        except Exception as e:
            logger.error(f"Error getting engagement metrics: {e}")
            return {}

    async def schedule_content(
        self, 
        organization_id: str, 
        auth_token: str,
        content: str,
        platform: str,
        post_time: datetime,
        media_links: Optional[List[str]] = None,
        reviewer_ids: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Schedule content on the specified platform."""
        try:
            url = f"{self.socials_service_url}/api/v1/socials/schedule-content"
            headers = {
                "Authorization": f"Bearer {auth_token}",
                "Content-Type": "application/json"
            }
            params = {"organisation_id": organization_id}

            payload = {
                "content": content,
                "post_time": post_time.isoformat(),
                "platforms": [platform],
                "reviewer_ids": reviewer_ids or [],
                "media_links": media_links or [],
                "status": "scheduled"
            }

            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(url, json=payload, params=params, headers=headers)

                if response.status_code in [200, 201]:
                    data = response.json()
                    logger.info(f"Successfully scheduled content for {platform} at {post_time}")
                    return {
                        "success": True,
                        "scheduled_content_id": data.get("data", {}).get("id"),
                        "platform": platform,
                        "post_time": post_time.isoformat(),
                        "status": "scheduled"
                    }
                else:
                    logger.error(f"Failed to schedule content: {response.status_code} - {response.text}")
                    return {
                        "success": False,
                        "error": f"Failed to schedule content: {response.status_code}",
                        "details": response.text
                    }

        except Exception as e:
            logger.error(f"Error scheduling content: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_calendar_view(self, organization_id: str, auth_token: str) -> Dict[str, Any]:
        """Get the calendar view of scheduled content."""
        try:
            url = f"{self.socials_service_url}/api/v1/socials/calendar-view"
            headers = {"Authorization": f"Bearer {auth_token}"}
            params = {"organisation_id": organization_id}

            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(url, params=params, headers=headers)

                if response.status_code == 200:
                    return response.json()
                else:
                    logger.warning(f"Failed to get calendar view: {response.status_code}")
                    return {"scheduled_content": []}

        except Exception as e:
            logger.error(f"Error getting calendar view: {e}")
            return {"scheduled_content": []}


socials_integration_service = SocialsIntegrationService()
