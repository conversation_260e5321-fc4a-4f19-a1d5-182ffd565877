"""
Firecrawl integration service for deep web content extraction.
"""

import requests
import json
from typing import Dict, List, Optional, Any
from app.utils.logger import get_logger
from app.core.config import settings

logger = get_logger(__name__)


class FirecrawlService:
    """Service for deep web content extraction using Firecrawl API."""
    
    def __init__(self):
        self.api_key = getattr(settings, 'FIRECRAWL_API_KEY', None)
        self.base_url = "https://api.firecrawl.dev/v0"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    async def crawl_url(self, url: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Crawl a single URL and extract structured content.
        
        Args:
            url: URL to crawl
            options: Additional crawling options
            
        Returns:
            Dict containing extracted content and metadata
        """
        try:
            if not self.api_key:
                logger.warning("Firecrawl API key not configured, using fallback extraction")
                return await self._fallback_extraction(url)
            
            # Default crawling options
            default_options = {
                "crawlerOptions": {
                    "includes": [],
                    "excludes": [],
                    "generateImgAltText": True,
                    "returnOnlyUrls": False
                },
                "pageOptions": {
                    "onlyMainContent": True,
                    "includeHtml": False,
                    "screenshot": False
                }
            }
            
            # Merge with provided options
            if options:
                default_options.update(options)
            
            # Make API request
            payload = {
                "url": url,
                **default_options
            }
            
            response = requests.post(
                f"{self.base_url}/scrape",
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                return {
                    "success": True,
                    "content": data.get("data", {}).get("content", ""),
                    "title": data.get("data", {}).get("title", ""),
                    "description": data.get("data", {}).get("description", ""),
                    "url": url,
                    "metadata": data.get("data", {}).get("metadata", {}),
                    "extraction_method": "firecrawl"
                }
            else:
                logger.error(f"Firecrawl API error {response.status_code}: {response.text}")
                return await self._fallback_extraction(url)
                
        except Exception as e:
            logger.error(f"Error crawling URL {url}: {e}")
            return await self._fallback_extraction(url)
    
    async def batch_crawl(self, urls: List[str], max_concurrent: int = 5) -> List[Dict[str, Any]]:
        """
        Crawl multiple URLs concurrently.
        
        Args:
            urls: List of URLs to crawl
            max_concurrent: Maximum concurrent requests
            
        Returns:
            List of crawl results
        """
        import asyncio
        
        # Create semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def crawl_with_semaphore(url: str) -> Dict[str, Any]:
            async with semaphore:
                return await self.crawl_url(url)
        
        # Execute crawls concurrently
        tasks = [crawl_with_semaphore(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Error crawling {urls[i]}: {result}")
                processed_results.append(await self._fallback_extraction(urls[i]))
            else:
                processed_results.append(result)
        
        return processed_results
    
    async def _fallback_extraction(self, url: str) -> Dict[str, Any]:
        """
        Fallback content extraction when Firecrawl is not available.
        Uses basic HTTP request and simple text extraction.
        """
        try:
            import requests
            from bs4 import BeautifulSoup
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract title
            title = ""
            if soup.title:
                title = soup.title.string.strip()
            
            # Extract main content
            content = ""
            
            # Try to find main content areas
            main_selectors = [
                'main', 'article', '.content', '.post-content', 
                '.entry-content', '.article-content', '#content'
            ]
            
            for selector in main_selectors:
                main_content = soup.select_one(selector)
                if main_content:
                    content = main_content.get_text(strip=True, separator=' ')
                    break
            
            # Fallback to body if no main content found
            if not content and soup.body:
                content = soup.body.get_text(strip=True, separator=' ')
            
            # Clean up content
            content = ' '.join(content.split())  # Remove extra whitespace
            
            return {
                "success": True,
                "content": content[:5000],  # Limit content length
                "title": title,
                "description": "",
                "url": url,
                "metadata": {},
                "extraction_method": "fallback"
            }
            
        except Exception as e:
            logger.error(f"Fallback extraction failed for {url}: {e}")
            return {
                "success": False,
                "content": "",
                "title": "",
                "description": "",
                "url": url,
                "metadata": {},
                "extraction_method": "failed",
                "error": str(e)
            }
    
    def is_available(self) -> bool:
        """Check if Firecrawl service is available."""
        return bool(self.api_key)


# Global service instance
firecrawl_service = FirecrawlService()
