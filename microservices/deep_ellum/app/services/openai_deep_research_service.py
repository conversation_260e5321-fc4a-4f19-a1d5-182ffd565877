"""
OpenAI Deep Research Service for comprehensive research tasks.
Integrates with OpenAI's o3-deep-research and o4-mini-deep-research models.
"""

import asyncio
from typing import Dict, Any, List, Optional, Union
from openai import OpenAI
from app.core.config import settings
from app.utils.logger import get_logger
import json
import time

logger = get_logger(__name__)


class OpenAIDeepResearchService:
    """Service for OpenAI Deep Research API integration."""
    
    def __init__(self):
        """Initialize the OpenAI Deep Research service."""
        if not settings.OPENAI_API_KEY:
            raise ValueError("OPENAI_API_KEY is required for Deep Research service")
        
        self.client = OpenAI(
            api_key=settings.OPENAI_API_KEY,
            timeout=3600  # 1 hour timeout for deep research
        )
        self.default_model = "o3-deep-research"
        self.mini_model = "o4-mini-deep-research"
        logger.info("OpenAI Deep Research Service initialized")
    
    async def conduct_deep_research(
        self,
        research_query: str,
        model: str = None,
        background: bool = True,
        max_tool_calls: int = None,
        include_code_interpreter: bool = True,
        include_web_search: bool = True,
        instructions: str = None
    ) -> Dict[str, Any]:
        """
        Conduct deep research using OpenAI's deep research models.
        
        Args:
            research_query: The research question or topic
            model: Model to use (o3-deep-research or o4-mini-deep-research)
            background: Whether to run in background mode
            max_tool_calls: Maximum number of tool calls to make
            include_code_interpreter: Whether to include code interpreter tool
            include_web_search: Whether to include web search tool
            instructions: Additional instructions for the research
            
        Returns:
            Dict containing research results and metadata
        """
        try:
            # Prepare the research input
            research_input = self._prepare_research_input(research_query, instructions)
            
            # Prepare tools
            tools = self._prepare_tools(include_web_search, include_code_interpreter)
            
            # Prepare request parameters
            request_params = {
                "model": model or self.default_model,
                "input": research_input,
                "tools": tools
            }
            
            # Add optional parameters
            if background:
                request_params["background"] = True
                request_params["reasoning"] = {"summary": "auto"}
            
            if max_tool_calls:
                request_params["max_tool_calls"] = max_tool_calls
            
            logger.info(f"Starting deep research with model: {request_params['model']}")
            
            # Execute research in background thread to avoid blocking
            if background:
                response = await self._execute_background_research(request_params)
            else:
                response = await self._execute_sync_research(request_params)
            
            # Process and return results
            return self._process_research_response(response)
            
        except Exception as e:
            logger.error(f"Error conducting deep research: {e}")
            raise
    
    def _prepare_research_input(self, query: str, instructions: str = None) -> str:
        """Prepare the research input with proper formatting."""
        base_instructions = """
        Conduct comprehensive research on the given topic. Your research should:
        
        1. **Gather Information**: Use web search to find reliable, up-to-date sources
        2. **Analyze Data**: Examine the information critically and identify patterns
        3. **Synthesize Findings**: Create a coherent narrative from multiple sources
        4. **Provide Citations**: Include inline citations and source metadata
        5. **Generate Insights**: Offer analytical insights and implications
        
        Focus on:
        - Accuracy and reliability of sources
        - Comprehensive coverage of the topic
        - Clear, well-structured presentation
        - Actionable insights and recommendations
        """
        
        if instructions:
            base_instructions += f"\n\nAdditional Instructions:\n{instructions}"
        
        research_input = f"{base_instructions}\n\nResearch Query: {query}"
        return research_input
    
    def _prepare_tools(self, include_web_search: bool, include_code_interpreter: bool) -> List[Dict[str, Any]]:
        """Prepare tools for the research request."""
        tools = []
        
        if include_web_search:
            tools.append({"type": "web_search_preview"})
        
        if include_code_interpreter:
            tools.append({
                "type": "code_interpreter",
                "container": {"type": "auto"}
            })
        
        return tools
    
    async def _execute_background_research(self, request_params: Dict[str, Any]) -> Any:
        """Execute research in background mode."""
        loop = asyncio.get_event_loop()
        
        def _make_request():
            return self.client.responses.create(**request_params)
        
        # Run in thread pool to avoid blocking
        response = await loop.run_in_executor(None, _make_request)
        
        # If background mode, we need to poll for completion
        if request_params.get("background"):
            response = await self._poll_background_completion(response)
        
        return response
    
    async def _execute_sync_research(self, request_params: Dict[str, Any]) -> Any:
        """Execute research in synchronous mode."""
        # Remove background flag for sync execution
        request_params.pop("background", None)
        
        loop = asyncio.get_event_loop()
        
        def _make_request():
            return self.client.responses.create(**request_params)
        
        return await loop.run_in_executor(None, _make_request)
    
    async def _poll_background_completion(self, initial_response: Any, poll_interval: int = 10) -> Any:
        """Poll for background task completion."""
        if not hasattr(initial_response, 'id'):
            return initial_response
        
        response_id = initial_response.id
        max_polls = 360  # 1 hour with 10-second intervals
        
        for _ in range(max_polls):
            try:
                # Check response status
                current_response = await self._get_response_status(response_id)
                
                if hasattr(current_response, 'status'):
                    if current_response.status == 'completed':
                        return current_response
                    elif current_response.status == 'failed':
                        raise Exception(f"Background research failed: {getattr(current_response, 'error', 'Unknown error')}")
                
                # Wait before next poll
                await asyncio.sleep(poll_interval)
                
            except Exception as e:
                logger.error(f"Error polling background research: {e}")
                break
        
        raise TimeoutError("Background research timed out")
    
    async def _get_response_status(self, response_id: str) -> Any:
        """Get the status of a background response."""
        loop = asyncio.get_event_loop()
        
        def _get_status():
            return self.client.responses.retrieve(response_id)
        
        return await loop.run_in_executor(None, _get_status)
    
    def _process_research_response(self, response: Any) -> Dict[str, Any]:
        """Process the research response into a structured format."""
        try:
            result = {
                "success": True,
                "response_id": getattr(response, 'id', None),
                "model": getattr(response, 'model', None),
                "output_text": "",
                "citations": [],
                "tool_calls": [],
                "reasoning_summary": "",
                "metadata": {}
            }
            
            # Extract main output text
            if hasattr(response, 'output_text'):
                result["output_text"] = response.output_text
            
            # Extract output array for detailed analysis
            if hasattr(response, 'output') and response.output:
                for output_item in response.output:
                    if hasattr(output_item, 'type'):
                        if output_item.type == 'message':
                            # Extract message content and citations
                            if hasattr(output_item, 'content'):
                                for content_item in output_item.content:
                                    if hasattr(content_item, 'type') and content_item.type == 'output_text':
                                        if hasattr(content_item, 'text'):
                                            result["output_text"] = content_item.text
                                        
                                        # Extract citations
                                        if hasattr(content_item, 'annotations'):
                                            result["citations"] = self._extract_citations(content_item.annotations)
                        
                        elif output_item.type in ['web_search_call', 'code_interpreter_call', 'mcp_tool_call']:
                            # Track tool calls
                            result["tool_calls"].append({
                                "type": output_item.type,
                                "id": getattr(output_item, 'id', None),
                                "status": getattr(output_item, 'status', None),
                                "action": getattr(output_item, 'action', None)
                            })
            
            # Extract reasoning summary if available
            if hasattr(response, 'reasoning') and response.reasoning:
                if hasattr(response.reasoning, 'summary'):
                    result["reasoning_summary"] = response.reasoning.summary
            
            # Add metadata
            result["metadata"] = {
                "created_at": getattr(response, 'created_at', None),
                "usage": getattr(response, 'usage', None),
                "tool_calls_count": len(result["tool_calls"])
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing research response: {e}")
            return {
                "success": False,
                "error": str(e),
                "raw_response": str(response)
            }
    
    def _extract_citations(self, annotations: List[Any]) -> List[Dict[str, Any]]:
        """Extract citations from annotations."""
        citations = []
        
        for annotation in annotations:
            if hasattr(annotation, 'url') and hasattr(annotation, 'title'):
                citations.append({
                    "url": annotation.url,
                    "title": annotation.title,
                    "start_index": getattr(annotation, 'start_index', None),
                    "end_index": getattr(annotation, 'end_index', None)
                })
        
        return citations
    
    async def get_research_status(self, response_id: str) -> Dict[str, Any]:
        """Get the status of a research task."""
        try:
            response = await self._get_response_status(response_id)
            
            return {
                "response_id": response_id,
                "status": getattr(response, 'status', 'unknown'),
                "created_at": getattr(response, 'created_at', None),
                "completed_at": getattr(response, 'completed_at', None),
                "model": getattr(response, 'model', None)
            }
            
        except Exception as e:
            logger.error(f"Error getting research status: {e}")
            return {
                "response_id": response_id,
                "status": "error",
                "error": str(e)
            }


# Global service instance
openai_deep_research_service = OpenAIDeepResearchService()
