"""
Deep Research Storage Service for managing research results and metadata.
"""

from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc
from app.models.models import DeepResearchTask, DeepResearchCitation, DeepResearchToolCall
from app.models.schemas import DeepResearchResponse, DeepResearchStatus
from app.utils.logger import get_logger
from datetime import datetime
import uuid

logger = get_logger(__name__)


class DeepResearchStorageService:
    """Service for storing and retrieving deep research results."""
    
    async def create_research_task(
        self,
        db: AsyncSession,
        organization_id: str,
        user_id: str,
        query: str,
        model: str,
        background_mode: bool = True,
        max_tool_calls: Optional[int] = None,
        include_code_interpreter: bool = True,
        include_web_search: bool = True,
        instructions: Optional[str] = None,
        openai_response_id: Optional[str] = None
    ) -> DeepResearchTask:
        """Create a new deep research task record."""
        try:
            task = DeepResearchTask(
                organization_id=organization_id,
                user_id=user_id,
                openai_response_id=openai_response_id,
                query=query,
                model=model,
                status="pending",
                background_mode=background_mode,
                max_tool_calls=max_tool_calls,
                include_code_interpreter=include_code_interpreter,
                include_web_search=include_web_search,
                instructions=instructions,
                started_at=datetime.utcnow() if not background_mode else None
            )
            
            db.add(task)
            await db.commit()
            await db.refresh(task)
            
            logger.info(f"Created deep research task: {task.id}")
            return task
            
        except Exception as e:
            logger.error(f"Error creating research task: {e}")
            await db.rollback()
            raise
    
    async def update_research_task(
        self,
        db: AsyncSession,
        task_id: uuid.UUID,
        research_result: Dict[str, Any]
    ) -> Optional[DeepResearchTask]:
        """Update a research task with results."""
        try:
            # Get the task
            result = await db.execute(
                select(DeepResearchTask).where(DeepResearchTask.id == task_id)
            )
            task = result.scalar_one_or_none()
            
            if not task:
                logger.warning(f"Research task not found: {task_id}")
                return None
            
            # Update task with results
            if research_result.get("success"):
                task.status = "completed"
                task.output_text = research_result.get("output_text", "")
                task.reasoning_summary = research_result.get("reasoning_summary", "")
                task.tool_calls_count = len(research_result.get("tool_calls", []))
                task.task_metadata = research_result.get("metadata", {})
                task.completed_at = datetime.utcnow()
                
                # Store citations
                citations = research_result.get("citations", [])
                for citation_data in citations:
                    citation = DeepResearchCitation(
                        task_id=task.id,
                        url=citation_data.get("url", ""),
                        title=citation_data.get("title", ""),
                        start_index=citation_data.get("start_index"),
                        end_index=citation_data.get("end_index")
                    )
                    db.add(citation)
                
                # Store tool calls
                tool_calls = research_result.get("tool_calls", [])
                for tool_call_data in tool_calls:
                    tool_call = DeepResearchToolCall(
                        task_id=task.id,
                        openai_tool_call_id=tool_call_data.get("id"),
                        tool_type=tool_call_data.get("type", ""),
                        status=tool_call_data.get("status", ""),
                        action_data=tool_call_data.get("action", {})
                    )
                    db.add(tool_call)
            else:
                task.status = "failed"
                task.error_message = research_result.get("error", "Unknown error")
                task.completed_at = datetime.utcnow()
            
            await db.commit()
            await db.refresh(task)
            
            logger.info(f"Updated research task: {task.id} with status: {task.status}")
            return task
            
        except Exception as e:
            logger.error(f"Error updating research task: {e}")
            await db.rollback()
            raise
    
    async def get_research_task(
        self,
        db: AsyncSession,
        task_id: uuid.UUID,
        organization_id: str
    ) -> Optional[DeepResearchTask]:
        """Get a research task by ID."""
        try:
            result = await db.execute(
                select(DeepResearchTask).where(
                    and_(
                        DeepResearchTask.id == task_id,
                        DeepResearchTask.organization_id == organization_id
                    )
                )
            )
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"Error getting research task: {e}")
            return None
    
    async def get_research_tasks(
        self,
        db: AsyncSession,
        organization_id: str,
        user_id: Optional[str] = None,
        status: Optional[str] = None,
        limit: int = 50
    ) -> List[DeepResearchTask]:
        """Get research tasks for an organization."""
        try:
            conditions = [DeepResearchTask.organization_id == organization_id]
            
            if user_id:
                conditions.append(DeepResearchTask.user_id == user_id)
            
            if status:
                conditions.append(DeepResearchTask.status == status)
            
            result = await db.execute(
                select(DeepResearchTask)
                .where(and_(*conditions))
                .order_by(desc(DeepResearchTask.created_at))
                .limit(limit)
            )
            
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Error getting research tasks: {e}")
            return []
    
    async def get_task_citations(
        self,
        db: AsyncSession,
        task_id: uuid.UUID
    ) -> List[DeepResearchCitation]:
        """Get citations for a research task."""
        try:
            result = await db.execute(
                select(DeepResearchCitation).where(
                    DeepResearchCitation.task_id == task_id
                )
            )
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Error getting task citations: {e}")
            return []
    
    async def get_task_tool_calls(
        self,
        db: AsyncSession,
        task_id: uuid.UUID
    ) -> List[DeepResearchToolCall]:
        """Get tool calls for a research task."""
        try:
            result = await db.execute(
                select(DeepResearchToolCall).where(
                    DeepResearchToolCall.task_id == task_id
                )
            )
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Error getting task tool calls: {e}")
            return []
    
    async def update_task_status(
        self,
        db: AsyncSession,
        task_id: uuid.UUID,
        status: str,
        error_message: Optional[str] = None
    ) -> bool:
        """Update the status of a research task."""
        try:
            result = await db.execute(
                select(DeepResearchTask).where(DeepResearchTask.id == task_id)
            )
            task = result.scalar_one_or_none()
            
            if not task:
                return False
            
            task.status = status
            if error_message:
                task.error_message = error_message
            
            if status == "running" and not task.started_at:
                task.started_at = datetime.utcnow()
            elif status in ["completed", "failed"]:
                task.completed_at = datetime.utcnow()
            
            await db.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error updating task status: {e}")
            await db.rollback()
            return False
    
    def task_to_response(self, task: DeepResearchTask) -> DeepResearchResponse:
        """Convert a task model to a response schema."""
        return DeepResearchResponse(
            success=task.status == "completed",
            response_id=str(task.id),
            model=task.model,
            output_text=task.output_text or "",
            citations=[],  # Would need to load separately
            tool_calls=[],  # Would need to load separately
            reasoning_summary=task.reasoning_summary or "",
            metadata=task.task_metadata or {},
            error=task.error_message
        )
    
    def task_to_status(self, task: DeepResearchTask) -> DeepResearchStatus:
        """Convert a task model to a status schema."""
        return DeepResearchStatus(
            response_id=str(task.id),
            status=task.status,
            created_at=task.created_at,
            completed_at=task.completed_at,
            model=task.model,
            error=task.error_message
        )


# Global service instance
deep_research_storage_service = DeepResearchStorageService()
