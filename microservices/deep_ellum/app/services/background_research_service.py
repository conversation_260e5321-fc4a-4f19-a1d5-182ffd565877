"""
Background Research Service for managing long-running deep research tasks.
"""

import asyncio
from typing import Dict, Any, Optional, Callable
from sqlalchemy.ext.asyncio import AsyncSession
from app.services.openai_deep_research_service import openai_deep_research_service
from app.services.deep_research_storage_service import deep_research_storage_service
from app.agents.openai_deep_research_agent import OpenAIDeepResearchAgent
from app.utils.logger import get_logger
from datetime import datetime
import uuid
import json

logger = get_logger(__name__)


class BackgroundResearchService:
    """Service for managing background deep research tasks."""
    
    def __init__(self):
        self.active_tasks: Dict[str, asyncio.Task] = {}
        self.task_callbacks: Dict[str, Callable] = {}
    
    async def start_background_research(
        self,
        db: AsyncSession,
        organization_id: str,
        user_id: str,
        query: str,
        model: str,
        research_params: Dict[str, Any],
        callback: Optional[Callable] = None
    ) -> str:
        """Start a background research task."""
        try:
            # Create task record in database
            task_record = await deep_research_storage_service.create_research_task(
                db=db,
                organization_id=organization_id,
                user_id=user_id,
                query=query,
                model=model,
                background_mode=True,
                max_tool_calls=research_params.get("max_tool_calls"),
                include_code_interpreter=research_params.get("include_code_interpreter", True),
                include_web_search=research_params.get("include_web_search", True),
                instructions=research_params.get("instructions")
            )
            
            task_id = str(task_record.id)
            
            # Create background task
            background_task = asyncio.create_task(
                self._execute_background_research(
                    task_id=task_id,
                    organization_id=organization_id,
                    user_id=user_id,
                    query=query,
                    model=model,
                    research_params=research_params
                )
            )
            
            # Store task reference
            self.active_tasks[task_id] = background_task
            if callback:
                self.task_callbacks[task_id] = callback
            
            logger.info(f"Started background research task: {task_id}")
            return task_id
            
        except Exception as e:
            logger.error(f"Error starting background research: {e}")
            raise
    
    async def _execute_background_research(
        self,
        task_id: str,
        organization_id: str,
        user_id: str,
        query: str,
        model: str,
        research_params: Dict[str, Any]
    ):
        """Execute the actual background research."""
        try:
            logger.info(f"Executing background research: {task_id}")
            
            # Update task status to running
            # Note: We'd need a database session here, but for simplicity
            # we'll handle status updates in the calling code
            
            # Create research agent
            research_agent = OpenAIDeepResearchAgent()
            
            # Prepare context
            context = {
                "organization_id": organization_id,
                "user_id": user_id,
                "research_params": research_params,
                "task_id": task_id
            }
            
            # Conduct the research
            result = await research_agent.process_message(query, context)
            
            # Store results (would need database session)
            research_result = {
                "success": True,
                "output_text": result,
                "model": model,
                "metadata": {
                    "completed_at": datetime.utcnow().isoformat(),
                    "background": True
                }
            }
            
            # Call callback if provided
            if task_id in self.task_callbacks:
                try:
                    await self.task_callbacks[task_id](task_id, research_result)
                except Exception as e:
                    logger.error(f"Error in task callback: {e}")
            
            logger.info(f"Background research completed: {task_id}")
            
        except Exception as e:
            logger.error(f"Background research failed: {task_id}, error: {e}")
            
            # Store error result
            error_result = {
                "success": False,
                "error": str(e),
                "model": model,
                "metadata": {
                    "failed_at": datetime.utcnow().isoformat(),
                    "background": True
                }
            }
            
            # Call callback with error
            if task_id in self.task_callbacks:
                try:
                    await self.task_callbacks[task_id](task_id, error_result)
                except Exception as callback_error:
                    logger.error(f"Error in error callback: {callback_error}")
        
        finally:
            # Clean up task references
            self.active_tasks.pop(task_id, None)
            self.task_callbacks.pop(task_id, None)
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """Get the status of a background task."""
        if task_id in self.active_tasks:
            task = self.active_tasks[task_id]
            if task.done():
                if task.exception():
                    return {
                        "status": "failed",
                        "error": str(task.exception())
                    }
                else:
                    return {"status": "completed"}
            else:
                return {"status": "running"}
        else:
            return {"status": "not_found"}
    
    def cancel_task(self, task_id: str) -> bool:
        """Cancel a background task."""
        if task_id in self.active_tasks:
            task = self.active_tasks[task_id]
            if not task.done():
                task.cancel()
                logger.info(f"Cancelled background task: {task_id}")
                return True
        return False
    
    def get_active_tasks(self) -> List[str]:
        """Get list of active task IDs."""
        return list(self.active_tasks.keys())
    
    def cleanup_completed_tasks(self):
        """Clean up completed tasks from memory."""
        completed_tasks = []
        for task_id, task in self.active_tasks.items():
            if task.done():
                completed_tasks.append(task_id)
        
        for task_id in completed_tasks:
            self.active_tasks.pop(task_id, None)
            self.task_callbacks.pop(task_id, None)
        
        if completed_tasks:
            logger.info(f"Cleaned up {len(completed_tasks)} completed tasks")


class WebhookNotificationService:
    """Service for sending webhook notifications about research completion."""
    
    def __init__(self):
        self.webhook_urls: Dict[str, str] = {}
    
    def register_webhook(self, organization_id: str, webhook_url: str):
        """Register a webhook URL for an organization."""
        self.webhook_urls[organization_id] = webhook_url
        logger.info(f"Registered webhook for organization: {organization_id}")
    
    async def send_completion_notification(
        self,
        organization_id: str,
        task_id: str,
        research_result: Dict[str, Any]
    ):
        """Send a webhook notification about research completion."""
        webhook_url = self.webhook_urls.get(organization_id)
        if not webhook_url:
            logger.debug(f"No webhook registered for organization: {organization_id}")
            return
        
        try:
            import httpx
            
            payload = {
                "event": "research_completed",
                "task_id": task_id,
                "organization_id": organization_id,
                "success": research_result.get("success", False),
                "timestamp": datetime.utcnow().isoformat(),
                "data": {
                    "model": research_result.get("model"),
                    "output_length": len(research_result.get("output_text", "")),
                    "citations_count": len(research_result.get("citations", [])),
                    "tool_calls_count": len(research_result.get("tool_calls", []))
                }
            }
            
            if not research_result.get("success"):
                payload["error"] = research_result.get("error", "Unknown error")
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    webhook_url,
                    json=payload,
                    timeout=30
                )
                
                if response.status_code == 200:
                    logger.info(f"Webhook notification sent successfully: {task_id}")
                else:
                    logger.warning(f"Webhook notification failed: {response.status_code}")
                    
        except Exception as e:
            logger.error(f"Error sending webhook notification: {e}")


# Global service instances
background_research_service = BackgroundResearchService()
webhook_notification_service = WebhookNotificationService()
