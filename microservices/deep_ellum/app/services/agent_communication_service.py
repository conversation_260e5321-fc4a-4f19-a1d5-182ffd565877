"""
Inter-Agent Communication Service for coordinating multi-agent workflows.
"""

from typing import Dict, List, Any, Optional
from pydantic import BaseModel
from datetime import datetime
from app.utils.logger import get_logger
import uuid
import json

logger = get_logger(__name__)


class AgentMessage(BaseModel):
    """Model for messages between agents."""
    id: str
    sender_agent: str
    recipient_agent: Optional[str] = None
    message_type: str  # request, response, context, error
    content: str
    metadata: Dict[str, Any] = {}
    timestamp: datetime
    conversation_id: str
    workflow_id: str


class WorkflowStep(BaseModel):
    """Model for workflow execution steps."""
    id: str
    workflow_id: str
    agent_type: str
    step_order: int
    status: str  # pending, in_progress, completed, failed
    input_message: Optional[str] = None
    output_message: Optional[str] = None
    context: Dict[str, Any] = {}
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None


class AgentWorkflow(BaseModel):
    """Model for multi-agent workflows."""
    id: str
    conversation_id: str
    original_request: str
    workflow_type: str  # sequential, parallel, conditional
    status: str  # pending, in_progress, completed, failed
    steps: List[WorkflowStep] = []
    context: Dict[str, Any] = {}
    created_at: datetime
    completed_at: Optional[datetime] = None
    final_response: Optional[str] = None


class AgentCommunicationService:
    """Service for managing inter-agent communication and workflows."""
    
    def __init__(self):
        self.active_workflows: Dict[str, AgentWorkflow] = {}
        self.message_history: Dict[str, List[AgentMessage]] = {}
        self.agent_contexts: Dict[str, Dict[str, Any]] = {}
    
    def create_workflow(
        self, 
        conversation_id: str, 
        original_request: str, 
        agent_sequence: List[str],
        workflow_type: str = "sequential",
        context: Dict[str, Any] = None
    ) -> AgentWorkflow:
        """Create a new multi-agent workflow."""
        try:
            workflow_id = str(uuid.uuid4())
            
            # Create workflow steps
            steps = []
            for i, agent_type in enumerate(agent_sequence):
                step = WorkflowStep(
                    id=str(uuid.uuid4()),
                    workflow_id=workflow_id,
                    agent_type=agent_type,
                    step_order=i,
                    status="pending",
                    context=context or {}
                )
                steps.append(step)
            
            # Create workflow
            workflow = AgentWorkflow(
                id=workflow_id,
                conversation_id=conversation_id,
                original_request=original_request,
                workflow_type=workflow_type,
                status="pending",
                steps=steps,
                context=context or {},
                created_at=datetime.now()
            )
            
            # Store workflow
            self.active_workflows[workflow_id] = workflow
            
            logger.info(f"Created workflow {workflow_id} with {len(steps)} steps")
            return workflow
            
        except Exception as e:
            logger.error(f"Error creating workflow: {e}")
            raise
    
    async def execute_workflow(self, workflow_id: str, agent_registry: Dict[str, Any]) -> str:
        """Execute a multi-agent workflow."""
        try:
            workflow = self.active_workflows.get(workflow_id)
            if not workflow:
                raise ValueError(f"Workflow {workflow_id} not found")
            
            workflow.status = "in_progress"
            
            if workflow.workflow_type == "sequential":
                return await self._execute_sequential_workflow(workflow, agent_registry)
            elif workflow.workflow_type == "parallel":
                return await self._execute_parallel_workflow(workflow, agent_registry)
            else:
                raise ValueError(f"Unsupported workflow type: {workflow.workflow_type}")
                
        except Exception as e:
            logger.error(f"Error executing workflow {workflow_id}: {e}")
            if workflow_id in self.active_workflows:
                self.active_workflows[workflow_id].status = "failed"
            raise
    
    async def _execute_sequential_workflow(self, workflow: AgentWorkflow, agent_registry: Dict[str, Any]) -> str:
        """Execute workflow steps sequentially."""
        try:
            current_message = workflow.original_request
            responses = []
            
            for step in workflow.steps:
                try:
                    # Update step status
                    step.status = "in_progress"
                    step.started_at = datetime.now()
                    step.input_message = current_message
                    
                    # Get agent instance
                    agent_info = agent_registry.get(step.agent_type)
                    if not agent_info:
                        raise ValueError(f"Agent {step.agent_type} not found in registry")
                    
                    agent_instance = agent_info["instance"]
                    
                    # Prepare context for agent
                    agent_context = {
                        **workflow.context,
                        "workflow_id": workflow.id,
                        "step_id": step.id,
                        "previous_responses": responses,
                        "conversation_history": []
                    }
                    
                    # Execute agent
                    response = await agent_instance.process_message(current_message, agent_context)
                    
                    # Store response
                    step.output_message = response
                    step.status = "completed"
                    step.completed_at = datetime.now()
                    
                    # Add to responses
                    responses.append({
                        "agent": agent_info["name"],
                        "agent_type": step.agent_type,
                        "content": response,
                        "step_order": step.step_order
                    })
                    
                    # Create message for next agent (if not last step)
                    if step.step_order < len(workflow.steps) - 1:
                        current_message = self._create_handoff_message(
                            workflow.original_request, 
                            responses, 
                            workflow.steps[step.step_order + 1].agent_type
                        )
                    
                    logger.info(f"Completed step {step.step_order} with agent {step.agent_type}")
                    
                except Exception as e:
                    step.status = "failed"
                    step.error_message = str(e)
                    step.completed_at = datetime.now()
                    logger.error(f"Error in workflow step {step.step_order}: {e}")
                    # Continue with next step or fail workflow based on error handling strategy
                    continue
            
            # Generate final response
            final_response = await self._aggregate_workflow_responses(workflow, responses)
            workflow.final_response = final_response
            workflow.status = "completed"
            workflow.completed_at = datetime.now()
            
            return final_response
            
        except Exception as e:
            workflow.status = "failed"
            logger.error(f"Error in sequential workflow: {e}")
            raise
    
    async def _execute_parallel_workflow(self, workflow: AgentWorkflow, agent_registry: Dict[str, Any]) -> str:
        """Execute workflow steps in parallel."""
        try:
            import asyncio
            
            # Prepare tasks for parallel execution
            tasks = []
            for step in workflow.steps:
                task = self._execute_workflow_step(step, workflow, agent_registry)
                tasks.append(task)
            
            # Execute all steps in parallel
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            responses = []
            for i, result in enumerate(results):
                step = workflow.steps[i]
                if isinstance(result, Exception):
                    step.status = "failed"
                    step.error_message = str(result)
                    logger.error(f"Error in parallel step {i}: {result}")
                else:
                    responses.append(result)
            
            # Generate final response
            final_response = await self._aggregate_workflow_responses(workflow, responses)
            workflow.final_response = final_response
            workflow.status = "completed"
            workflow.completed_at = datetime.now()
            
            return final_response
            
        except Exception as e:
            workflow.status = "failed"
            logger.error(f"Error in parallel workflow: {e}")
            raise
    
    async def _execute_workflow_step(self, step: WorkflowStep, workflow: AgentWorkflow, agent_registry: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a single workflow step."""
        try:
            step.status = "in_progress"
            step.started_at = datetime.now()
            step.input_message = workflow.original_request
            
            # Get agent instance
            agent_info = agent_registry.get(step.agent_type)
            if not agent_info:
                raise ValueError(f"Agent {step.agent_type} not found in registry")
            
            agent_instance = agent_info["instance"]
            
            # Prepare context
            agent_context = {
                **workflow.context,
                "workflow_id": workflow.id,
                "step_id": step.id,
                "conversation_history": []
            }
            
            # Execute agent
            response = await agent_instance.process_message(workflow.original_request, agent_context)
            
            # Update step
            step.output_message = response
            step.status = "completed"
            step.completed_at = datetime.now()
            
            return {
                "agent": agent_info["name"],
                "agent_type": step.agent_type,
                "content": response,
                "step_order": step.step_order
            }
            
        except Exception as e:
            step.status = "failed"
            step.error_message = str(e)
            step.completed_at = datetime.now()
            raise
    
    def _create_handoff_message(self, original_request: str, previous_responses: List[Dict[str, Any]], next_agent_type: str) -> str:
        """Create a message for handing off between agents."""
        try:
            handoff_message = f"Original request: {original_request}\n\n"
            
            if previous_responses:
                handoff_message += "Previous agent responses:\n"
                for response in previous_responses:
                    handoff_message += f"- {response['agent']}: {response['content'][:200]}...\n"
                handoff_message += "\n"
            
            handoff_message += f"Please provide your specialized input as the {next_agent_type} agent, "
            handoff_message += "building upon the previous responses where relevant."
            
            return handoff_message
            
        except Exception as e:
            logger.error(f"Error creating handoff message: {e}")
            return original_request
    
    async def _aggregate_workflow_responses(self, workflow: AgentWorkflow, responses: List[Dict[str, Any]]) -> str:
        """Aggregate responses from multiple agents."""
        try:
            if not responses:
                return "I apologize, but I wasn't able to get responses from the specialized agents."
            
            if len(responses) == 1:
                return f"{responses[0]['content']}\n\n*Response provided by {responses[0]['agent']} via Orchestrator Agent*"
            
            # Create comprehensive response
            aggregated = f"Here's a comprehensive response to your request:\n\n"
            
            for i, response in enumerate(responses, 1):
                aggregated += f"**{response['agent']} Analysis:**\n"
                aggregated += f"{response['content']}\n\n"
            
            # Add summary
            agent_names = [resp['agent'] for resp in responses]
            aggregated += f"*Coordinated response from: {', '.join(agent_names)} via Orchestrator Agent*"
            
            return aggregated
            
        except Exception as e:
            logger.error(f"Error aggregating responses: {e}")
            # Fallback to simple concatenation
            result = "Here are the responses from the specialized agents:\n\n"
            for response in responses:
                result += f"**{response['agent']}:** {response['content']}\n\n"
            return result
    
    def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get the status of a workflow."""
        try:
            workflow = self.active_workflows.get(workflow_id)
            if not workflow:
                return None
            
            return {
                "id": workflow.id,
                "status": workflow.status,
                "steps_completed": len([s for s in workflow.steps if s.status == "completed"]),
                "total_steps": len(workflow.steps),
                "created_at": workflow.created_at.isoformat(),
                "completed_at": workflow.completed_at.isoformat() if workflow.completed_at else None
            }
            
        except Exception as e:
            logger.error(f"Error getting workflow status: {e}")
            return None
    
    def cleanup_completed_workflows(self, max_age_hours: int = 24):
        """Clean up old completed workflows."""
        try:
            current_time = datetime.now()
            workflows_to_remove = []
            
            for workflow_id, workflow in self.active_workflows.items():
                if workflow.status in ["completed", "failed"]:
                    age_hours = (current_time - workflow.created_at).total_seconds() / 3600
                    if age_hours > max_age_hours:
                        workflows_to_remove.append(workflow_id)
            
            for workflow_id in workflows_to_remove:
                del self.active_workflows[workflow_id]
                if workflow_id in self.message_history:
                    del self.message_history[workflow_id]
            
            if workflows_to_remove:
                logger.info(f"Cleaned up {len(workflows_to_remove)} old workflows")
                
        except Exception as e:
            logger.error(f"Error cleaning up workflows: {e}")


# Global communication service instance
agent_communication_service = AgentCommunicationService()
