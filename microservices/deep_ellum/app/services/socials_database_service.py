"""
Socials Database Service for accessing social media data from socials_service.
Provides intelligent access to social media content, scheduling, and analytics data.
"""

import httpx
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from app.core.config import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)


class SocialsDatabaseService:
    """Service for accessing socials database via socials_service internal API."""

    def __init__(self):
        self.socials_service_url = settings.SOCIALS_SERVICE_URL
        self.timeout = 30.0
        logger.info(f"Socials database service initialized with URL: {self.socials_service_url}")

    async def has_socials_data(self, organization_id: str, auth_token: str) -> bool:
        """Check if organization has social media data available."""
        try:
            # Check if organization has connected platforms
            platforms = await self._get_connected_platforms(organization_id, auth_token)
            has_data = len(platforms) > 0
            
            logger.info(f"Organization {organization_id} has socials data: {has_data}")
            return has_data

        except Exception as e:
            logger.error(f"Error checking socials data availability: {e}")
            return False

    async def _get_connected_platforms(self, organization_id: str, auth_token: str) -> List[str]:
        """Get list of connected social media platforms."""
        try:
            url = f"{self.socials_service_url}/api/v1/socials/org/socials"
            headers = {"Authorization": f"Bearer {auth_token}"}
            params = {"organisation_id": organization_id}

            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(url, params=params, headers=headers)

                if response.status_code == 200:
                    data = response.json()
                    connected_platforms = []
                    for account in data.get("data", []):
                        if account.get("login_status", False):
                            connected_platforms.append(account.get("platform"))
                    return connected_platforms
                else:
                    logger.warning(f"Failed to get platforms: {response.status_code}")
                    return []

        except Exception as e:
            logger.error(f"Error getting connected platforms: {e}")
            return []

    async def get_calendar_data(self, organization_id: str, auth_token: str) -> Dict[str, Any]:
        """Get calendar view of scheduled and published content."""
        try:
            url = f"{self.socials_service_url}/api/v1/socials/view-content"
            headers = {"Authorization": f"Bearer {auth_token}"}
            params = {"organisation_id": organization_id}

            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(url, params=params, headers=headers)

                if response.status_code == 200:
                    data = response.json()
                    logger.info(f"Retrieved calendar data for org {organization_id}")
                    return data
                else:
                    logger.warning(f"Failed to get calendar data: {response.status_code}")
                    return {"scheduled_content": []}

        except Exception as e:
            logger.error(f"Error getting calendar data: {e}")
            return {"scheduled_content": []}

    async def get_published_content(self, organization_id: str, auth_token: str, days_back: int = 30) -> Dict[str, Any]:
        """Get published content from the last N days."""
        try:
            url = f"{self.socials_service_url}/api/v1/socials/view-published-content"
            headers = {"Authorization": f"Bearer {auth_token}"}
            params = {"organisation_id": organization_id}

            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(url, params=params, headers=headers)

                if response.status_code == 200:
                    data = response.json()
                    logger.info(f"Retrieved published content for org {organization_id}")
                    return data
                else:
                    logger.warning(f"Failed to get published content: {response.status_code}")
                    return {"published_content": []}

        except Exception as e:
            logger.error(f"Error getting published content: {e}")
            return {"published_content": []}

    async def get_content_analytics(self, organization_id: str, auth_token: str, months: int = 3) -> Dict[str, Any]:
        """Get content analytics and performance metrics."""
        try:
            url = f"{self.socials_service_url}/api/v1/socials/post-analytics"
            headers = {"Authorization": f"Bearer {auth_token}"}
            params = {"organisation_id": organization_id, "months": months}

            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(url, params=params, headers=headers)

                if response.status_code == 200:
                    data = response.json()
                    logger.info(f"Retrieved analytics for org {organization_id}")
                    return data
                else:
                    logger.warning(f"Failed to get analytics: {response.status_code}")
                    return {}

        except Exception as e:
            logger.error(f"Error getting content analytics: {e}")
            return {}

    async def get_optimal_posting_times(self, organization_id: str, auth_token: str) -> Dict[str, Any]:
        """Get optimal posting times based on historical performance."""
        try:
            # Get analytics data to determine optimal times
            analytics = await self.get_content_analytics(organization_id, auth_token)
            
            # For now, return general best practices
            # This could be enhanced with actual analytics processing
            optimal_times = {
                "general": {
                    "optimal_hours": [9, 12, 15, 18],  # 9 AM, 12 PM, 3 PM, 6 PM
                    "optimal_days": [1, 2, 3, 4],  # Tuesday through Friday
                    "timezone": "UTC"
                },
                "platform_specific": {
                    "facebook": {"optimal_hours": [10, 13, 16, 19]},
                    "twitter": {"optimal_hours": [9, 12, 15, 18]},
                    "instagram": {"optimal_hours": [11, 14, 17, 20]},
                    "linkedin": {"optimal_hours": [8, 12, 17]}
                }
            }
            
            logger.info(f"Generated optimal posting times for org {organization_id}")
            return optimal_times

        except Exception as e:
            logger.error(f"Error getting optimal posting times: {e}")
            return {}

    async def search_content_history(self, organization_id: str, auth_token: str, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search through historical content for patterns and insights."""
        try:
            # Get published content
            published_data = await self.get_published_content(organization_id, auth_token)
            published_content = published_data.get("published_content", [])
            
            # Simple text search through content
            matching_content = []
            query_lower = query.lower()
            
            for content in published_content:
                content_text = content.get("content", "").lower()
                if query_lower in content_text:
                    matching_content.append({
                        "id": content.get("id"),
                        "content": content.get("content"),
                        "platform": content.get("platform"),
                        "post_time": content.get("post_time"),
                        "status": content.get("status"),
                        "engagement_metrics": content.get("engagement_metrics", {})
                    })
                    
                    if len(matching_content) >= limit:
                        break
            
            logger.info(f"Found {len(matching_content)} matching content items for query: {query}")
            return matching_content

        except Exception as e:
            logger.error(f"Error searching content history: {e}")
            return []


socials_database_service = SocialsDatabaseService()
