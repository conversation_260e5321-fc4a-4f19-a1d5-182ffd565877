import httpx
from typing import List, Dict, Any
from app.core.config import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)


class KnowledgebaseService:
    """Service for accessing knowledgebase via FastBot internal API."""

    def __init__(self):
        self.fastbot_url = settings.FASTBOT_SERVICE_URL
        logger.info(f"Knowledgebase service initialized with FastBot URL: {self.fastbot_url}")

    async def has_knowledgebase(self, organization_id: str, auth_token: str) -> bool:
        """Check if organization has a knowledgebase via FastBot API."""
        try:
            url = f"{self.fastbot_url}/api/v1/internal/knowledge/status"
            headers = {"Authorization": f"Bearer {auth_token}"}
            params = {"organization_id": organization_id}

            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(url, params=params, headers=headers)

                if response.status_code == 200:
                    data = response.json()
                    return data.get("has_data", False)
                else:
                    logger.warning(f"Knowledgebase status check failed: {response.status_code}")
                    return False

        except Exception as e:
            logger.error(f"Error checking knowledgebase availability: {e}")
            return False

    async def get_knowledge_context(self, query: str, organization_id: str, auth_token: str) -> List[str]:
        """
        Retrieve relevant knowledge via FastBot internal API.

        Args:
            query: The search query
            organization_id: Organization ID
            auth_token: Authentication token for FastBot API

        Returns:
            List of relevant knowledge snippets
        """
        try:
            url = f"{self.fastbot_url}/api/v1/internal/knowledge/search"
            headers = {
                "Authorization": f"Bearer {auth_token}",
                "Content-Type": "application/json"
            }

            payload = {
                "query": query,
                "organization_id": organization_id,
                "top": 5,
                "threshold": 0.7
            }

            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(url, json=payload, headers=headers)

                if response.status_code == 200:
                    data = response.json()
                    knowledge_results = data.get("results", [])
                    logger.info(f"Retrieved {len(knowledge_results)} knowledge items for query: {query}")
                    return knowledge_results
                elif response.status_code == 404:
                    logger.info(f"No knowledge found for organization {organization_id}")
                    return []
                else:
                    logger.error(f"FastBot API error: {response.status_code} - {response.text}")
                    return []

        except httpx.TimeoutException:
            logger.error("Timeout while connecting to FastBot knowledgebase")
            return []
        except Exception as e:
            logger.error(f"Error retrieving knowledge from FastBot: {e}")
            return []


# Global instance
knowledgebase_service = KnowledgebaseService()
