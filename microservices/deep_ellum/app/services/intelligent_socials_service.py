"""
Intelligent Socials Service for LLM-based social media data analysis.
Uses AI to determine when to query socials data and generate intelligent insights.
"""

from typing import Dict, Any, List, Optional
from app.services.socials_database_service import socials_database_service
from app.services.gemini_service import gemini_service
from app.utils.logger import get_logger

logger = get_logger(__name__)


class IntelligentSocialsService:
    """Service for intelligent social media data access and analysis."""

    def __init__(self):
        self.socials_service = socials_database_service

    async def should_query_socials(self, message: str, organization_id: str, auth_token: str) -> bool:
        """
        Use LLM to determine if the user message would benefit from socials data context.

        Args:
            message: User message
            organization_id: Organization ID
            auth_token: Authentication token for socials API

        Returns:
            True if socials data should be queried
        """
        # First check if socials data exists
        if not await self.socials_service.has_socials_data(organization_id, auth_token):
            return False

        try:
            analysis_prompt = f"""
            Analyze this user message to determine if it would benefit from social media data context.
            
            User message: "{message}"
            
            Consider if the message is asking about:
            - Social media content scheduling or calendar
            - Content performance or analytics
            - Optimal posting times
            - Historical social media posts
            - Social media strategy or planning
            - Content ideas based on past performance
            - Audience engagement patterns
            - Platform-specific insights
            - Content repurposing opportunities
            
            Answer with only "YES" if social media data would be helpful, or "NO" if not needed.
            """
            
            response = await gemini_service.generate_response(analysis_prompt)
            decision = response.strip().upper()
            
            should_query = decision == "YES"
            logger.info(f"LLM decision for socials query: {should_query} (message: '{message[:50]}...')")
            
            return should_query
            
        except Exception as e:
            logger.error(f"Error in LLM analysis for socials query: {e}")
            # Default to querying if we can't determine
            return True

    async def generate_socials_query_type(self, message: str) -> str:
        """
        Use LLM to determine what type of social media data to query.
        
        Args:
            message: Original user message
            
        Returns:
            Query type: 'calendar', 'analytics', 'content_history', 'optimal_times', or 'general'
        """
        try:
            query_prompt = f"""
            Analyze this user message to determine what type of social media data would be most relevant.
            
            User message: "{message}"
            
            Choose the most appropriate data type:
            - "calendar" - for questions about scheduled content, upcoming posts, or content calendar
            - "analytics" - for questions about performance metrics, engagement, or content effectiveness
            - "content_history" - for questions about past posts, content patterns, or historical data
            - "optimal_times" - for questions about best posting times or scheduling optimization
            - "general" - for general social media questions that need multiple data types
            
            Return only one of these exact words: calendar, analytics, content_history, optimal_times, general
            """
            
            query_type = await gemini_service.generate_response(query_prompt)
            query_type = query_type.strip().lower()
            
            # Validate the response
            valid_types = ['calendar', 'analytics', 'content_history', 'optimal_times', 'general']
            if query_type not in valid_types:
                query_type = 'general'
            
            logger.info(f"Generated query type: '{query_type}' for message: '{message[:50]}...'")
            return query_type
            
        except Exception as e:
            logger.error(f"Error generating socials query type: {e}")
            return 'general'

    async def get_socials_context(
        self,
        message: str,
        organization_id: str,
        auth_token: str
    ) -> Dict[str, Any]:
        """
        Get relevant social media context based on the user message.

        Args:
            message: User message
            organization_id: Organization ID
            auth_token: Authentication token

        Returns:
            Dictionary containing relevant social media data
        """
        try:
            # Determine query type
            query_type = await self.generate_socials_query_type(message)
            
            context = {}
            
            if query_type == 'calendar':
                context['calendar'] = await self.socials_service.get_calendar_data(organization_id, auth_token)
            elif query_type == 'analytics':
                context['analytics'] = await self.socials_service.get_content_analytics(organization_id, auth_token)
            elif query_type == 'content_history':
                # Extract search terms from message for content search
                search_query = await self._extract_search_terms(message)
                context['content_history'] = await self.socials_service.search_content_history(
                    organization_id, auth_token, search_query
                )
            elif query_type == 'optimal_times':
                context['optimal_times'] = await self.socials_service.get_optimal_posting_times(organization_id, auth_token)
            else:  # general
                # Get multiple data types for comprehensive context
                context['calendar'] = await self.socials_service.get_calendar_data(organization_id, auth_token)
                context['analytics'] = await self.socials_service.get_content_analytics(organization_id, auth_token)
                context['optimal_times'] = await self.socials_service.get_optimal_posting_times(organization_id, auth_token)
            
            logger.info(f"Retrieved socials context of type '{query_type}' for org {organization_id}")
            return context
            
        except Exception as e:
            logger.error(f"Error getting socials context: {e}")
            return {}

    async def _extract_search_terms(self, message: str) -> str:
        """Extract search terms from user message for content history search."""
        try:
            search_prompt = f"""
            Extract key search terms from this message to search through social media content history.
            
            User message: "{message}"
            
            Extract the most relevant keywords, topics, or phrases that would help find related social media posts.
            Return only the search terms, separated by spaces, without quotes or extra formatting.
            """
            
            search_terms = await gemini_service.generate_response(search_prompt)
            search_terms = search_terms.strip()
            
            logger.info(f"Extracted search terms: '{search_terms}' from message: '{message[:50]}...'")
            return search_terms
            
        except Exception as e:
            logger.error(f"Error extracting search terms: {e}")
            return message  # Fallback to original message

    async def get_socials_status(self, organization_id: str, auth_token: str) -> Dict[str, Any]:
        """
        Get the status of socials data availability for the organization.

        Args:
            organization_id: Organization ID
            auth_token: Authentication token

        Returns:
            Status information about socials data availability
        """
        try:
            has_data = await self.socials_service.has_socials_data(organization_id, auth_token)
            connected_platforms = await self.socials_service._get_connected_platforms(organization_id, auth_token)
            
            return {
                "has_data": has_data,
                "connected_platforms": connected_platforms,
                "platform_count": len(connected_platforms),
                "status": "available" if has_data else "no_data"
            }
            
        except Exception as e:
            logger.error(f"Error getting socials status: {e}")
            return {
                "has_data": False,
                "connected_platforms": [],
                "platform_count": 0,
                "status": "error",
                "error": str(e)
            }

    async def enhance_message_with_socials_context(
        self,
        message: str,
        organization_id: str,
        auth_token: str
    ) -> str:
        """
        Enhance user message with relevant social media context if needed.

        Args:
            message: Original user message
            organization_id: Organization ID
            auth_token: Authentication token

        Returns:
            Enhanced message with social media context or original message
        """
        try:
            # Check if we should query socials data
            should_query = await self.should_query_socials(message, organization_id, auth_token)

            if not should_query:
                logger.debug("LLM determined socials query not needed")
                return message

            # Get socials context
            socials_context = await self.get_socials_context(message, organization_id, auth_token)
            
            if not socials_context:
                logger.debug("No relevant socials data found")
                return message

            # Enhance message with context
            enhanced_message = f"""
            User Query: {message}
            
            Relevant Social Media Context:
            {self._format_socials_context(socials_context)}
            
            Please provide a response that takes into account both the user's question and the social media context provided above.
            """
            
            logger.info(f"Enhanced message with socials context for org {organization_id}")
            return enhanced_message
            
        except Exception as e:
            logger.error(f"Error enhancing message with socials context: {e}")
            return message

    def _format_socials_context(self, context: Dict[str, Any]) -> str:
        """Format socials context for inclusion in enhanced message."""
        formatted_parts = []
        
        if 'calendar' in context and context['calendar'].get('scheduled_content'):
            scheduled_count = len(context['calendar']['scheduled_content'])
            formatted_parts.append(f"- {scheduled_count} scheduled posts in content calendar")
        
        if 'analytics' in context and context['analytics']:
            formatted_parts.append("- Content performance analytics available")
        
        if 'content_history' in context and context['content_history']:
            history_count = len(context['content_history'])
            formatted_parts.append(f"- {history_count} relevant historical posts found")
        
        if 'optimal_times' in context and context['optimal_times']:
            formatted_parts.append("- Optimal posting times data available")
        
        return "\n".join(formatted_parts) if formatted_parts else "- Social media data available"


intelligent_socials_service = IntelligentSocialsService()
