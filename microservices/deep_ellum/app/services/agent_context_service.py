from typing import Dict, Any
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.models import OrganizationSettings
from app.services.intelligent_knowledge_service import intelligent_knowledge_service
from app.utils.logger import get_logger

logger = get_logger(__name__)


class AgentContextService:
    """Service for managing agent context with intelligent knowledgebase integration."""

    async def is_knowledgebase_enabled(
        self,
        db: AsyncSession,
        organization_id: str
    ) -> bool:
        """
        Check if knowledgebase is enabled for the organization.
        """
        try:
            query = select(OrganizationSettings).where(
                OrganizationSettings.organization_id == organization_id
            )
            result = await db.execute(query)
            settings = result.scalar_one_or_none()

            return settings.knowledgebase_enabled if settings else False

        except Exception as e:
            logger.error(f"Error checking knowledgebase settings: {e}")
            return False

    async def is_socials_database_enabled(
        self,
        db: AsyncSession,
        organization_id: str
    ) -> bool:
        """
        Check if socials database is enabled for the organization.
        """
        try:
            query = select(OrganizationSettings).where(
                OrganizationSettings.organization_id == organization_id
            )
            result = await db.execute(query)
            settings = result.scalar_one_or_none()

            return settings.socials_database_enabled if settings else False

        except Exception as e:
            logger.error(f"Error checking socials database settings: {e}")
            return False
    
    async def enhance_message_with_knowledge(
        self,
        message: str,
        organization_id: str,
        auth_token: str,
        db: AsyncSession
    ) -> str:
        """
        Enhance message with knowledgebase context if enabled.

        Args:
            message: User message
            organization_id: Organization ID
            auth_token: Authentication token for FastBot API
            db: Database session

        Returns:
            Enhanced message or original message
        """
        try:
            # Check if knowledgebase is enabled for organization
            if not await self.is_knowledgebase_enabled(db, organization_id):
                return message

            # Use intelligent service to enhance message
            enhanced_message = await intelligent_knowledge_service.enhance_message_with_knowledge(
                message=message,
                organization_id=organization_id,
                auth_token=auth_token
            )

            return enhanced_message

        except Exception as e:
            logger.error(f"Error enhancing message with knowledge: {e}")
            return message




agent_context_service = AgentContextService()
