"""
Unified AI provider service that supports switching between Gemini and OpenAI.
"""

from typing import Optional, Any
from langchain_core.language_models import BaseChatModel
from app.core.config import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)


class AIProviderService:
    """Service for managing AI provider switching between Gemini and OpenAI."""
    
    def __init__(self):
        self._llm_cache = {}
        self.current_provider = settings.AI_PROVIDER.lower()
        logger.info(f"AI Provider Service initialized with provider: {self.current_provider}")
    
    def get_llm(self, temperature: float = 0.2, model: Optional[str] = None) -> BaseChatModel:
        """
        Get LLM instance based on configured provider.
        
        Args:
            temperature: Temperature for the model
            model: Specific model to use (optional)
            
        Returns:
            BaseChatModel instance
        """
        cache_key = f"{self.current_provider}_{temperature}_{model}"
        
        if cache_key in self._llm_cache:
            return self._llm_cache[cache_key]
        
        try:
            if self.current_provider == "openai":
                llm = self._get_openai_llm(temperature, model)
            elif self.current_provider == "gemini":
                llm = self._get_gemini_llm(temperature, model)
            else:
                logger.error(f"Unsupported AI provider: {self.current_provider}")
                raise ValueError(f"Unsupported AI provider: {self.current_provider}")
            
            self._llm_cache[cache_key] = llm
            logger.info(f"Created {self.current_provider} LLM with temperature {temperature}")
            return llm
            
        except Exception as e:
            logger.error(f"Error creating {self.current_provider} LLM: {e}")
            # Fallback to the other provider
            return self._get_fallback_llm(temperature, model)
    
    def _get_openai_llm(self, temperature: float, model: Optional[str] = None) -> BaseChatModel:
        """Get OpenAI LLM instance."""
        try:
            from langchain_openai import ChatOpenAI
            
            if not settings.OPENAI_API_KEY:
                raise ValueError("OPENAI_API_KEY not configured")
            
            # Default to GPT-4 if no model specified
            model_name = model or "gpt-4o-mini"  # Use mini for cost efficiency
            
            return ChatOpenAI(
                model=model_name,
                temperature=temperature,
                api_key=settings.OPENAI_API_KEY,
                max_tokens=4000,
                timeout=30
            )
            
        except ImportError:
            logger.error("langchain_openai not installed. Install with: pip install langchain-openai")
            raise
        except Exception as e:
            logger.error(f"Error creating OpenAI LLM: {e}")
            raise
    
    def _get_gemini_llm(self, temperature: float, model: Optional[str] = None) -> BaseChatModel:
        """Get Gemini LLM instance."""
        try:
            from langchain_google_genai import ChatGoogleGenerativeAI
            
            if not settings.GEMINI_API_KEY:
                raise ValueError("GEMINI_API_KEY not configured")
            
            # Default to flash model if no model specified
            model_name = model or "gemini-1.5-flash"
            
            return ChatGoogleGenerativeAI(
                model=model_name,
                temperature=temperature,
                google_api_key=settings.GEMINI_API_KEY,
                timeout=30
            )
            
        except ImportError:
            logger.error("langchain_google_genai not installed. Install with: pip install langchain-google-genai")
            raise
        except Exception as e:
            logger.error(f"Error creating Gemini LLM: {e}")
            raise
    
    def _get_fallback_llm(self, temperature: float, model: Optional[str] = None) -> BaseChatModel:
        """Get fallback LLM when primary provider fails."""
        fallback_provider = "openai" if self.current_provider == "gemini" else "gemini"
        
        logger.warning(f"Attempting fallback to {fallback_provider}")
        
        try:
            if fallback_provider == "openai":
                return self._get_openai_llm(temperature, model)
            else:
                return self._get_gemini_llm(temperature, model)
        except Exception as e:
            logger.error(f"Fallback to {fallback_provider} also failed: {e}")
            raise RuntimeError(f"Both {self.current_provider} and {fallback_provider} providers failed")
    
    def switch_provider(self, provider: str) -> None:
        """
        Switch AI provider at runtime.
        
        Args:
            provider: "openai" or "gemini"
        """
        provider = provider.lower()
        if provider not in ["openai", "gemini"]:
            raise ValueError(f"Unsupported provider: {provider}")
        
        self.current_provider = provider
        self._llm_cache.clear()  # Clear cache when switching
        logger.info(f"Switched AI provider to: {provider}")
    
    def get_provider_info(self) -> dict:
        """Get current provider information."""
        return {
            "current_provider": self.current_provider,
            "available_providers": ["openai", "gemini"],
            "openai_configured": bool(settings.OPENAI_API_KEY),
            "gemini_configured": bool(settings.GEMINI_API_KEY)
        }
    
    def test_provider(self, provider: Optional[str] = None) -> dict:
        """
        Test if a provider is working.
        
        Args:
            provider: Provider to test, defaults to current
            
        Returns:
            Dict with test results
        """
        test_provider = provider or self.current_provider
        
        try:
            # Temporarily switch if testing different provider
            original_provider = self.current_provider
            if provider and provider != self.current_provider:
                self.switch_provider(provider)
            
            # Test with a simple prompt
            llm = self.get_llm(temperature=0.1)
            response = llm.invoke("Say 'test successful' if you can read this.")
            
            # Restore original provider if we switched
            if provider and provider != original_provider:
                self.switch_provider(original_provider)
            
            return {
                "provider": test_provider,
                "status": "success",
                "response": response.content[:100],  # First 100 chars
                "error": None
            }
            
        except Exception as e:
            # Restore original provider if we switched
            if provider and provider != original_provider:
                self.switch_provider(original_provider)
            
            return {
                "provider": test_provider,
                "status": "failed",
                "response": None,
                "error": str(e)
            }


# Global service instance
ai_provider_service = AIProviderService()
