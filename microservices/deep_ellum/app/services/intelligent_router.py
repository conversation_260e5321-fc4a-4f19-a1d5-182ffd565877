"""
Intelligent LLM-based routing system for research requests.
Uses LLM to analyze requests and autonomously route to appropriate tools and workflows.
"""

import json
from typing import Dict, List, Any, Optional
from pydantic import BaseModel

from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import JsonOutputParser

from app.utils.logger import get_logger
from app.core.config import settings
from app.services.ai_provider_service import ai_provider_service

logger = get_logger(__name__)


class ResearchIntent(BaseModel):
    """Structured research intent extracted by LLM."""
    research_type: str  # 'competitor_analysis', 'trend_analysis', 'market_research', 'data_aggregation'
    confidence: float  # 0.0 to 1.0
    primary_entity: str  # Main subject (company, topic, industry)
    secondary_entities: List[str]  # Additional entities of interest
    time_scope: str  # 'recent', '1w', '1m', '3m', '6m', '1y'
    geographic_scope: str  # 'global', 'US', 'Europe', etc.
    specific_focus: List[str]  # Specific aspects to focus on
    urgency: str  # 'low', 'medium', 'high'
    depth_required: str  # 'overview', 'detailed', 'comprehensive'
    tools_needed: List[str]  # Recommended tools to use


class IntelligentRouter:
    """LLM-powered intelligent routing system for research requests."""
    
    def __init__(self):
        self.llm = ai_provider_service.get_llm(temperature=0.1)
        
        self.intent_parser = JsonOutputParser(pydantic_object=ResearchIntent)
        
        self.routing_prompt = ChatPromptTemplate.from_messages([
            ("system", """You are an expert research analyst AI that intelligently routes research requests to the most appropriate tools and workflows.

Your task is to analyze user research requests and determine:
1. The type of research needed
2. Key entities and parameters to extract
3. The best tools and approach to use
4. The scope and depth required

Research Types:
- competitor_analysis: Analyzing specific companies, their activities, strategies, market position
- trend_analysis: Identifying patterns, emerging trends, market movements in industries/topics
- market_research: General market analysis, industry overviews, market sizing, opportunities
- data_aggregation: Combining information from multiple sources, cross-referencing data

Available Tools:
- web_search: General web search for information gathering
- competitor_monitoring: Specialized competitor tracking and analysis
- trend_analysis: Pattern recognition and trend identification
- data_aggregation: Multi-source data synthesis

Time Scopes:
- recent: Last few days
- 1w: Past week
- 1m: Past month  
- 3m: Past 3 months
- 6m: Past 6 months
- 1y: Past year

Analyze the user's request and extract structured information to route it intelligently.
Be autonomous in your decision-making - don't ask for clarification unless absolutely necessary.

Respond with valid JSON matching the ResearchIntent schema. Return the fields directly at the root level, not nested under a 'research_intent' key.

Example response format:
{{
  "research_type": "trend_analysis",
  "confidence": 0.85,
  "primary_entity": "artificial intelligence",
  "secondary_entities": ["machine learning", "automation"],
  "time_scope": "3m",
  "geographic_scope": "global",
  "specific_focus": ["adoption rates", "market growth"],
  "urgency": "medium",
  "depth_required": "detailed",
  "tools_needed": ["trend_analysis", "web_search"]
}}"""),
            ("human", "Research Request: {request}")
        ])
        
        self.parameter_extraction_prompt = ChatPromptTemplate.from_messages([
            ("system", """You are an expert at extracting specific parameters from research requests.

Given a research request and the determined research type, extract the most relevant parameters:

For competitor_analysis:
- competitor_name: The main company being analyzed
- keywords: Related business terms, products, services, market segments
- comparison_targets: Other companies to compare against

For trend_analysis:
- topic: The main subject/industry/technology being analyzed
- sub_topics: Specific aspects or segments within the topic
- trend_indicators: What signals to look for

For market_research:
- market_segment: Specific market or industry
- research_questions: Key questions to answer
- stakeholders: Important players or groups

For data_aggregation:
- data_sources: Types of sources to gather from
- focus_areas: Specific information to extract
- synthesis_goals: What insights to generate

Extract parameters that will help conduct the most effective research.
Respond with a JSON object containing the relevant parameters."""),
            ("human", "Research Request: {request}\nResearch Type: {research_type}")
        ])
    
    async def analyze_research_intent(self, request: str) -> ResearchIntent:
        """Analyze research request and determine intent using LLM."""
        try:
            # Create the routing chain
            routing_chain = self.routing_prompt | self.llm | self.intent_parser

            # Analyze the request
            result = await routing_chain.ainvoke({"request": request})

            # Handle nested response structure
            if isinstance(result, dict):
                # Check if the result is nested under 'research_intent' key
                if 'research_intent' in result:
                    intent_data = result['research_intent']
                else:
                    intent_data = result

                return ResearchIntent(**intent_data)
            else:
                return result

        except Exception as e:
            logger.error(f"Error analyzing research intent: {e}")
            # Fallback to basic analysis
            return self._fallback_intent_analysis(request)
    
    async def extract_parameters(self, request: str, research_type: str) -> Dict[str, Any]:
        """Extract specific parameters for the research type using LLM."""
        try:
            # Create the parameter extraction chain
            extraction_chain = self.parameter_extraction_prompt | self.llm
            
            # Extract parameters
            result = await extraction_chain.ainvoke({
                "request": request,
                "research_type": research_type
            })
            
            # Parse the JSON response
            if hasattr(result, 'content'):
                content = result.content
            else:
                content = str(result)
            
            # Clean and parse JSON
            content = content.strip()
            if content.startswith('```json'):
                content = content[7:]
            if content.endswith('```'):
                content = content[:-3]
            
            return json.loads(content)
            
        except Exception as e:
            logger.error(f"Error extracting parameters: {e}")
            # Fallback to basic parameter extraction
            return self._fallback_parameter_extraction(request, research_type)
    
    def _fallback_intent_analysis(self, request: str) -> ResearchIntent:
        """Enhanced fallback intent analysis using LLM with simpler prompt."""
        try:
            # Use a simpler, more reliable LLM approach for fallback
            simple_prompt = f"""
            Analyze this research request and classify it:

            Request: "{request}"

            Return only a JSON object:
            {{
                "research_type": "trend_analysis|competitor_analysis|market_research|data_aggregation",
                "confidence": 0.7,
                "primary_entity": "main subject of research",
                "secondary_entities": ["related", "topics"],
                "time_scope": "1w|1m|3m|6m|1y",
                "geographic_scope": "global|US|Europe|etc",
                "specific_focus": ["specific", "aspects"],
                "urgency": "low|medium|high",
                "depth_required": "overview|detailed|comprehensive",
                "tools_needed": ["relevant", "tools"]
            }}
            """

            response = self.llm.invoke(simple_prompt)

            # Parse response
            import json
            import re
            json_match = re.search(r'\{.*\}', response.content, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group())
                return ResearchIntent(**data)

        except Exception as e:
            logger.error(f"Error in enhanced fallback analysis: {e}")

        # Ultimate fallback - basic heuristics
        request_lower = request.lower()

        # Determine research type
        if any(word in request_lower for word in ['competitor', 'competition', 'rival', 'vs', 'versus', 'compare']):
            research_type = 'competitor_analysis'
        elif any(word in request_lower for word in ['trend', 'trending', 'emerging', 'future', 'forecast']):
            research_type = 'trend_analysis'
        elif any(word in request_lower for word in ['aggregate', 'compile', 'combine', 'sources']):
            research_type = 'data_aggregation'
        else:
            research_type = 'market_research'

        # Extract primary entity (first meaningful word)
        words = [w for w in request.split() if len(w) > 2]
        primary_entity = words[0] if words else "general"

        return ResearchIntent(
            research_type=research_type,
            confidence=0.6,  # Lower confidence for fallback
            primary_entity=primary_entity,
            secondary_entities=[],
            time_scope='3m',
            geographic_scope='global',
            specific_focus=[],
            urgency='medium',
            depth_required='detailed',
            tools_needed=[research_type, "web_search"]
        )
    
    def _fallback_parameter_extraction(self, request: str, research_type: str) -> Dict[str, Any]:
        """Fallback parameter extraction using simple heuristics."""
        if research_type == 'competitor_analysis':
            return {
                "competitor_name": self._extract_company_name(request),
                "keywords": ["general", "market", "strategy"],
                "comparison_targets": []
            }
        elif research_type == 'trend_analysis':
            return {
                "topic": self._extract_topic(request),
                "sub_topics": [],
                "trend_indicators": ["growth", "innovation", "adoption"]
            }
        elif research_type == 'market_research':
            return {
                "market_segment": self._extract_topic(request),
                "research_questions": ["market size", "key players", "opportunities"],
                "stakeholders": []
            }
        else:  # data_aggregation
            return {
                "data_sources": ["web", "news", "reports"],
                "focus_areas": ["general"],
                "synthesis_goals": ["comprehensive overview"]
            }
    
    def _extract_company_name(self, request: str) -> str:
        """Extract company name from request."""
        words = request.split()
        for word in words:
            if word[0].isupper() and len(word) > 2:
                return word
        return ""
    
    def _extract_topic(self, request: str) -> str:
        """Extract main topic from request."""
        stop_words = ["analyze", "research", "trends", "in", "the", "of", "for", "about", "study"]
        words = [word for word in request.split() if word.lower() not in stop_words]
        return " ".join(words[:3])
    
    async def route_research_request(self, request: str) -> Dict[str, Any]:
        """Main routing function that analyzes request and returns routing decision."""
        try:
            # Analyze intent
            intent = await self.analyze_research_intent(request)
            
            # Extract specific parameters
            parameters = await self.extract_parameters(request, intent.research_type)
            
            # Create routing decision
            routing_decision = {
                "research_type": intent.research_type,
                "confidence": intent.confidence,
                "workflow": self._determine_workflow(intent),
                "tools": self._determine_tools(intent),
                "parameters": parameters,
                "execution_plan": self._create_execution_plan(intent, parameters),
                "estimated_duration": self._estimate_duration(intent),
                "priority": self._determine_priority(intent)
            }
            
            logger.info(f"Intelligent routing decision: {routing_decision['research_type']} with confidence {routing_decision['confidence']}")
            
            return routing_decision
            
        except Exception as e:
            logger.error(f"Error in intelligent routing: {e}")
            # Return fallback routing
            return {
                "research_type": "market_research",
                "confidence": 0.5,
                "workflow": "general_research",
                "tools": ["web_search"],
                "parameters": {"query": request},
                "execution_plan": ["web_search", "analyze_results", "generate_report"],
                "estimated_duration": "2-3 minutes",
                "priority": "medium"
            }
    
    def _determine_workflow(self, intent: ResearchIntent) -> str:
        """Determine the best workflow based on intent."""
        workflow_mapping = {
            'competitor_analysis': 'competitor_analysis_workflow',
            'trend_analysis': 'trend_analysis_workflow',
            'market_research': 'market_research_workflow',
            'data_aggregation': 'data_aggregation_workflow'
        }
        return workflow_mapping.get(intent.research_type, 'general_research_workflow')
    
    def _determine_tools(self, intent: ResearchIntent) -> List[str]:
        """Determine the best tools based on intent."""
        tool_mapping = {
            'competitor_analysis': ['competitor_monitoring', 'web_search', 'trend_analysis'],
            'trend_analysis': ['trend_analysis', 'web_search', 'data_aggregation'],
            'market_research': ['web_search', 'trend_analysis', 'data_aggregation'],
            'data_aggregation': ['data_aggregation', 'web_search']
        }
        return tool_mapping.get(intent.research_type, ['web_search'])
    
    def _create_execution_plan(self, intent: ResearchIntent, parameters: Dict[str, Any]) -> List[str]:
        """Create step-by-step execution plan."""
        if intent.research_type == 'competitor_analysis':
            return [
                "extract_competitor_info",
                "gather_recent_activities", 
                "analyze_market_position",
                "identify_strategic_moves",
                "generate_competitive_analysis"
            ]
        elif intent.research_type == 'trend_analysis':
            return [
                "identify_trend_indicators",
                "gather_market_data",
                "analyze_patterns",
                "predict_future_directions",
                "generate_trend_report"
            ]
        elif intent.research_type == 'market_research':
            return [
                "define_market_scope",
                "gather_market_data",
                "analyze_key_players",
                "identify_opportunities",
                "generate_market_report"
            ]
        else:  # data_aggregation
            return [
                "identify_data_sources",
                "gather_information",
                "cross_reference_data",
                "synthesize_insights",
                "generate_comprehensive_report"
            ]
    
    def _estimate_duration(self, intent: ResearchIntent) -> str:
        """Estimate research duration based on complexity."""
        if intent.depth_required == 'overview':
            return "1-2 minutes"
        elif intent.depth_required == 'detailed':
            return "2-4 minutes"
        else:  # comprehensive
            return "4-6 minutes"
    
    def _determine_priority(self, intent: ResearchIntent) -> str:
        """Determine research priority."""
        if intent.urgency == 'high' or intent.confidence > 0.9:
            return "high"
        elif intent.urgency == 'low' or intent.confidence < 0.6:
            return "low"
        else:
            return "medium"


# Global intelligent router instance
intelligent_router = IntelligentRouter()
