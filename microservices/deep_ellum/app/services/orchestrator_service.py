"""
Orchestrator Service for managing agent registry, discovery, and coordination.
"""

from typing import Dict, List, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from app.models.models import CustomAgent
from app.agents.sample_agents import SAMPLE_AGENTS
from app.agents.agent_factory import AgentFactory
from app.utils.logger import get_logger

logger = get_logger(__name__)


class OrchestratorService:
    """Service for orchestrating agents within Deep Ellum service."""

    def __init__(self):
        self.agent_factory = AgentFactory()
    
    async def get_agent_registry(self, db: AsyncSession, organization_id: str = None, force_refresh: bool = False) -> Dict[str, Any]:
        """Get registry of all available agents for the organization."""
        try:
            if force_refresh:
                # Force refresh - get fresh data
                return await self.agent_factory.get_fresh_agent_registry(db, organization_id)
            else:
                # Use existing method for backward compatibility
                return await self.agent_factory.get_fresh_agent_registry(db, organization_id)

        except Exception as e:
            logger.error(f"Error building agent registry: {e}")
            return {}

    async def refresh_agent_registry(self, db: AsyncSession, organization_id: str = None) -> Dict[str, Any]:
        """Force refresh the agent registry and clear all caches."""
        try:
            # Clear all caches
            self.agent_factory.clear_cache()

            # Get fresh registry
            registry = await self.agent_factory.get_fresh_agent_registry(db, organization_id)

            logger.info(f"Refreshed agent registry with {len(registry)} agents")
            return registry

        except Exception as e:
            logger.error(f"Error refreshing agent registry: {e}")
            return {}
    

    
    async def discover_available_agents(self, db: AsyncSession, organization_id: str = None) -> List[Dict[str, Any]]:
        """Discover all available agents and their capabilities."""
        try:
            agents = []
            
            # Get sample agents
            for agent_type, agent_class in SAMPLE_AGENTS.items():
                try:
                    agent_instance = agent_class()
                    agents.append({
                        "id": agent_type,
                        "name": agent_instance.name,
                        "description": agent_instance.description,
                        "capabilities": [cap.value for cap in agent_instance.capabilities],
                        "type": "sample",
                        "available": True
                    })
                except Exception as e:
                    logger.error(f"Error discovering sample agent {agent_type}: {e}")
            
            # Get custom agents
            if db and organization_id:
                try:
                    custom_agents_query = select(CustomAgent).where(
                        and_(
                            CustomAgent.organization_id == organization_id,
                            CustomAgent.is_active == True,
                            CustomAgent.is_sample == False
                        )
                    )
                    
                    result = await db.execute(custom_agents_query)
                    custom_agents = result.scalars().all()
                    
                    for agent_record in custom_agents:
                        agents.append({
                            "id": str(agent_record.id),
                            "name": agent_record.name,
                            "description": agent_record.description,
                            "capabilities": agent_record.capabilities or [],
                            "type": "custom",
                            "available": True
                        })
                        
                except Exception as e:
                    logger.error(f"Error discovering custom agents: {e}")
            
            return agents
            
        except Exception as e:
            logger.error(f"Error discovering agents: {e}")
            return []
    
    async def get_organization_context(self, organization_id: str, user_id: str = None) -> Dict[str, Any]:
        """Get organization context for agent operations."""
        try:
            context = {
                "organization_id": organization_id,
                "user_id": user_id,
                "agent_count": 0
            }

            return context

        except Exception as e:
            logger.error(f"Error getting organization context: {e}")
            return {"organization_id": organization_id, "user_id": user_id}
    
    def get_agent_recommendations(self, task_description: str) -> List[str]:
        """Get recommended agents for a given task description using dynamic discovery."""
        try:
            recommendations = []
            task_lower = task_description.lower()

            # Get all available sample agents dynamically
            for agent_type, agent_class in SAMPLE_AGENTS.items():
                if agent_type == "orchestrator_agent":
                    continue  # Skip orchestrator itself

                try:
                    agent_instance = agent_class()
                    agent_description = agent_instance.description.lower()
                    agent_capabilities = [cap.value for cap in agent_instance.capabilities]

                    # Check if task matches agent's domain
                    relevance_score = 0

                    # Check description overlap
                    task_words = set(task_lower.split())
                    desc_words = set(agent_description.split())
                    common_words = task_words.intersection(desc_words)
                    relevance_score += len(common_words) * 2

                    # Check capability relevance
                    for capability in agent_capabilities:
                        if any(word in task_lower for word in capability.split('_')):
                            relevance_score += 3

                    # Add to recommendations if relevant
                    if relevance_score > 0:
                        recommendations.append((agent_type, relevance_score))

                except Exception as e:
                    logger.warning(f"Error evaluating agent {agent_type}: {e}")

            # Sort by relevance score and return agent types
            recommendations.sort(key=lambda x: x[1], reverse=True)
            recommended_agents = [agent_type for agent_type, _ in recommendations[:3]]  # Top 3

            # If no specific recommendations, suggest orchestrator
            if not recommended_agents:
                recommended_agents.append("orchestrator_agent")

            return recommended_agents

        except Exception as e:
            logger.error(f"Error getting agent recommendations: {e}")
            return ["orchestrator_agent"]

    def get_all_available_agents(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all available agents."""
        try:
            agents_info = {}

            # Sample agents
            for agent_type, agent_class in SAMPLE_AGENTS.items():
                try:
                    agent_instance = agent_class()
                    agents_info[agent_type] = {
                        "name": agent_instance.name,
                        "description": agent_instance.description,
                        "capabilities": [cap.value for cap in agent_instance.capabilities],
                        "type": "sample"
                    }
                except Exception as e:
                    logger.warning(f"Error getting info for {agent_type}: {e}")

            return agents_info

        except Exception as e:
            logger.error(f"Error getting all available agents: {e}")
            return {}

    async def get_microservice_tools(self, organization_id: str = None) -> Dict[str, Any]:
        """Get available microservice tools for the organization."""
        try:
            # For now, return empty dict since we're working within deep_ellum service only
            # In the future, this could include tools to interact with other microservices
            tools = {
                "available_tools": [],
                "organization_id": organization_id,
                "service": "deep_ellum"
            }

            logger.info(f"Retrieved microservice tools for organization: {organization_id}")
            return tools

        except Exception as e:
            logger.error(f"Error getting microservice tools: {e}")
            return {}


# Global orchestrator service instance
orchestrator_service = OrchestratorService()
