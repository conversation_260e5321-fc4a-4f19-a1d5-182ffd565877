"""
Intelligent Routing Service for orchestrator agent task delegation.
"""

from typing import Dict, List, Any, Optional
from pydantic import BaseModel
from app.services.langchain_service import langchain_service
from app.utils.logger import get_logger
import json
import re

logger = get_logger(__name__)


class RoutingDecision(BaseModel):
    """Model for routing decisions."""
    strategy: str  # direct_response, single_agent, multi_agent
    reasoning: str
    target_agents: List[str] = []
    workflow_steps: List[str] = []
    confidence: float = 0.0
    priority: str = "medium"  # low, medium, high, urgent
    estimated_duration: str = "1-2 minutes"


class IntelligentRoutingService:
    """Service for intelligent LLM-based task routing and delegation."""

    def __init__(self):
        self.agent_capabilities = self._discover_agent_capabilities()

    def _discover_agent_capabilities(self) -> Dict[str, Dict[str, Any]]:
        """Dynamically discover available agents and their capabilities."""
        try:
            from app.agents.sample_agents import SAMPLE_AGENTS

            capabilities = {}
            for agent_type, agent_class in SAMPLE_AGENTS.items():
                if agent_type == "orchestrator_agent":
                    continue  # Skip self to avoid circular references

                try:
                    agent_instance = agent_class()
                    capabilities[agent_type] = {
                        "name": agent_instance.name,
                        "capabilities": [cap.value for cap in agent_instance.capabilities],
                        "description": agent_instance.description
                    }
                except Exception as e:
                    logger.warning(f"Could not discover capabilities for {agent_type}: {e}")

            return capabilities

        except Exception as e:
            logger.error(f"Error discovering agent capabilities: {e}")
            # Fallback to empty dict - will be handled gracefully
            return {}

    def refresh_agent_capabilities(self):
        """Refresh the agent capabilities cache - call this when new agents are added."""
        try:
            self.agent_capabilities = self._discover_agent_capabilities()
            logger.info(f"Refreshed agent capabilities. Found {len(self.agent_capabilities)} agents.")
        except Exception as e:
            logger.error(f"Error refreshing agent capabilities: {e}")

    def get_available_agents(self) -> List[str]:
        """Get list of currently available agent types."""
        return list(self.agent_capabilities.keys())

    def get_agent_info(self, agent_type: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific agent type."""
        return self.agent_capabilities.get(agent_type)
        

    
    async def analyze_task_intent(self, message: str, context: Dict[str, Any] = None) -> RoutingDecision:
        """Analyze task intent using LLM and determine routing strategy."""
        try:
            # First, try keyword-based routing for critical requests
            keyword_routing = self._keyword_based_routing(message)
            if keyword_routing:
                logger.info(f"Keyword-based routing: {keyword_routing.strategy} -> {keyword_routing.target_agents}")
                return keyword_routing

            # Create comprehensive analysis prompt
            analysis_prompt = self._create_analysis_prompt(message, context)

            # Use LLM for analysis
            response = await langchain_service.process_message(
                message=analysis_prompt,
                instructions="You are an expert task analysis system. Analyze the request and provide routing decisions in the exact JSON format requested. Be precise and thorough. ALWAYS route specialized tasks to appropriate agents.",
                conversation_history=[],
                capabilities=["task_analysis", "routing", "decision_making"]
            )

            # Parse and validate response
            routing_decision = self._parse_routing_response(response, message)

            # Enhance with additional analysis
            routing_decision = self._enhance_routing_decision(routing_decision, message, context)

            logger.info(f"Routing decision: {routing_decision.strategy} with confidence {routing_decision.confidence}")
            return routing_decision

        except Exception as e:
            logger.error(f"Error analyzing task intent: {e}")
            # Use keyword-based routing as backup instead of generic fallback
            keyword_routing = self._keyword_based_routing(message)
            if keyword_routing:
                return keyword_routing
            # If all else fails, route to content calendar for scheduling or general response
            return self._intelligent_fallback_routing(message)
    
    def _create_analysis_prompt(self, message: str, context: Dict[str, Any] = None) -> str:
        """Create comprehensive analysis prompt for LLM."""

        # Build expertise areas dynamically (without revealing agent architecture)
        expertise_areas = []

        if self.agent_capabilities:
            for agent_type, info in self.agent_capabilities.items():
                name = info.get('name', agent_type.replace('_', ' ').title())
                description = info.get('description', 'Specialized expertise')
                capabilities = info.get('capabilities', [])

                # Create expertise description without revealing agents
                expertise_desc = f"- **{name}**: {description}"
                if capabilities:
                    # Convert capabilities to readable format
                    readable_caps = [cap.replace('_', ' ').title() for cap in capabilities]
                    expertise_desc += f"\n  Areas: {', '.join(readable_caps)}"

                expertise_areas.append(expertise_desc)

            # Add note about custom expertise
            expertise_areas.append("- **Custom Expertise**: Organization-specific specialized knowledge areas")

            expertise_section = f"""
        ## Available Expertise Areas:
        {chr(10).join(expertise_areas)}

        ## Routing Strategies:

        1. **single_agent** - Use when:
           - Task requires specific domain expertise from one area
           - Clear, focused request that maps to one specialization
           - User needs deep expertise in one domain
           - Content scheduling, planning, or calendar requests
           - Research, documentation, or code generation tasks

        2. **multi_agent** - Use when:
           - Task requires expertise from multiple domains
           - Complex project that benefits from different perspectives
           - User request spans multiple specializations
           - Task requires coordination between different types of expertise

        ## Critical Routing Rules:
        - ALWAYS route content scheduling, posting, or calendar requests to Content Calendar Agent
        - ALWAYS route research requests to Deep Research Agent
        - ALWAYS route code generation requests to Code Generator Agent
        - NEVER use direct_response for specialized tasks that have dedicated agents
        - Match requests to the most appropriate specialized agent based on capabilities

        ## Analysis Guidelines:
        - Prioritize specialized agent routing over direct responses
        - Match the request to the most appropriate available expertise
        - Consider the depth and breadth of knowledge needed
        - Evaluate if the task requires specialized domain knowledge
        - Determine if multiple perspectives would add value
        - Assess the complexity and scope of the request"""
        else:
            expertise_section = f"""
        ## Current Expertise Status:
        No specialized agents available - this should not happen in production

        ## Fallback Strategy:
        - Log error about missing agent capabilities
        - Route to single_agent with content_calendar_agent for scheduling requests
        - Route to single_agent with appropriate agent type based on request keywords"""

        prompt = f"""
        You are an expert AI task router. Analyze this user request and determine the optimal routing strategy.

        User Request: "{message}"

        {expertise_section}

        ## Response Format:
        Provide your analysis in this exact JSON format:
        {{
            "strategy": "direct_response|single_agent|multi_agent",
            "reasoning": "detailed explanation of your routing decision including why this approach is optimal",
            "target_agents": ["agent_name1", "agent_name2"] or [],
            "workflow_steps": ["step1", "step2"] or [],
            "confidence": 0.85,
            "priority": "low|medium|high|urgent",
            "estimated_duration": "1-2 minutes"
        }}

        Be thorough in your reasoning and specific about which agents to use and why.
        """
        
        return prompt
    
    def _parse_routing_response(self, response: str, original_message: str) -> RoutingDecision:
        """Parse LLM response into RoutingDecision object."""
        try:
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                json_data = json.loads(json_match.group())
                
                # Validate required fields
                if "strategy" not in json_data:
                    raise ValueError("Missing strategy field")
                
                # Create RoutingDecision object
                return RoutingDecision(**json_data)
            else:
                raise ValueError("No JSON found in response")
                
        except (json.JSONDecodeError, ValueError) as e:
            logger.warning(f"Failed to parse routing response: {e}")
            return self._fallback_routing_decision(original_message)
    
    def _enhance_routing_decision(self, decision: RoutingDecision, message: str, context: Dict[str, Any] = None) -> RoutingDecision:
        """Enhance routing decision with validation and workflow generation."""
        try:
            # Validate agent selections
            valid_agents = []
            for agent in decision.target_agents:
                if agent in self.agent_capabilities:
                    valid_agents.append(agent)
                else:
                    logger.warning(f"Invalid agent in routing decision: {agent}")
            decision.target_agents = valid_agents

            # Generate workflow steps if missing
            if not decision.workflow_steps:
                decision.workflow_steps = self._generate_workflow_steps(decision)

            # Ensure confidence is within valid range
            decision.confidence = max(0.0, min(1.0, decision.confidence))

            return decision

        except Exception as e:
            logger.error(f"Error enhancing routing decision: {e}")
            return decision
    

    
    def _generate_workflow_steps(self, decision: RoutingDecision) -> List[str]:
        """Generate workflow steps based on routing decision."""
        try:
            steps = []
            
            if decision.strategy == "direct_response":
                steps = ["analyze_request", "provide_direct_response"]
            elif decision.strategy == "single_agent":
                if decision.target_agents:
                    steps = [f"delegate_to_{decision.target_agents[0]}", "return_response"]
            elif decision.strategy == "multi_agent":
                for agent in decision.target_agents:
                    steps.append(f"delegate_to_{agent}")
                steps.append("aggregate_responses")
            elif decision.strategy == "microservice":
                if decision.target_services:
                    steps = [f"integrate_with_{decision.target_services[0]}", "process_response"]
            
            return steps
            
        except Exception as e:
            logger.error(f"Error generating workflow steps: {e}")
            return ["execute_task", "return_response"]
    
    def _keyword_based_routing(self, message: str) -> Optional[RoutingDecision]:
        """Provide keyword-based routing for critical requests."""
        message_lower = message.lower()

        # Content scheduling and calendar requests
        if any(keyword in message_lower for keyword in [
            "schedule", "post this", "publish this", "schedule the first", "schedule content",
            "calendar", "content calendar", "plan content", "schedule for me"
        ]):
            return RoutingDecision(
                strategy="single_agent",
                reasoning="Content scheduling request detected - routing to Content Calendar Agent",
                target_agents=["content_calendar_agent"],
                workflow_steps=["delegate_to_content_calendar_agent", "return_response"],
                confidence=0.95,
                priority="high",
                estimated_duration="2-3 minutes"
            )

        # Research requests
        if any(keyword in message_lower for keyword in [
            "research", "analyze", "investigate", "study", "competitor", "market analysis"
        ]):
            return RoutingDecision(
                strategy="single_agent",
                reasoning="Research request detected - routing to Deep Research Agent",
                target_agents=["deep_research_agent"],
                workflow_steps=["delegate_to_deep_research_agent", "return_response"],
                confidence=0.9,
                priority="medium",
                estimated_duration="3-5 minutes"
            )

        # Code generation requests
        if any(keyword in message_lower for keyword in [
            "code", "generate", "write code", "programming", "function", "class", "script"
        ]):
            return RoutingDecision(
                strategy="single_agent",
                reasoning="Code generation request detected - routing to Code Generator Agent",
                target_agents=["code_generator"],
                workflow_steps=["delegate_to_code_generator", "return_response"],
                confidence=0.9,
                priority="medium",
                estimated_duration="2-4 minutes"
            )

        return None

    def _intelligent_fallback_routing(self, message: str) -> RoutingDecision:
        """Provide intelligent fallback routing when all else fails."""
        message_lower = message.lower()

        # If it mentions content, scheduling, or social media, route to content calendar
        if any(keyword in message_lower for keyword in [
            "content", "social", "post", "tweet", "instagram", "facebook", "linkedin"
        ]):
            return RoutingDecision(
                strategy="single_agent",
                reasoning="Content-related request detected in fallback - routing to Content Calendar Agent",
                target_agents=["content_calendar_agent"],
                workflow_steps=["delegate_to_content_calendar_agent", "return_response"],
                confidence=0.7,
                priority="medium",
                estimated_duration="2-3 minutes"
            )

        # For any other request, route to deep research as it can handle general queries
        return RoutingDecision(
            strategy="single_agent",
            reasoning="General request - routing to Deep Research Agent for comprehensive assistance",
            target_agents=["deep_research_agent"],
            workflow_steps=["delegate_to_deep_research_agent", "return_response"],
            confidence=0.6,
            priority="medium",
            estimated_duration="2-3 minutes"
        )


# Global routing service instance
intelligent_routing_service = IntelligentRoutingService()
