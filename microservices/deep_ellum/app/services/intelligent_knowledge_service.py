from typing import Dict, Any, Optional, List
from app.services.knowledgebase_service import KnowledgebaseService
from app.services.gemini_service import gemini_service
from app.utils.logger import get_logger

logger = get_logger(__name__)


class IntelligentKnowledgeService:
    """Service that uses LLM to intelligently determine when and how to query knowledgebase."""
    
    def __init__(self):
        self.kb_service = KnowledgebaseService()
    
    async def should_query_knowledgebase(self, message: str, organization_id: str, auth_token: str) -> bool:
        """
        Use LLM to determine if the user message would benefit from knowledgebase context.

        Args:
            message: User message
            organization_id: Organization ID
            auth_token: Authentication token for FastBot API

        Returns:
            True if knowledgebase should be queried
        """
        # First check if knowledgebase exists
        if not await self.kb_service.has_knowledgebase(organization_id, auth_token):
            return False
        
        try:
            # Use LLM to analyze if the message needs organizational context
            analysis_prompt = f"""
            Analyze this user message and determine if it would benefit from organizational knowledge context.
            
            User message: "{message}"
            
            Consider if the message:
            - Asks about company/organization-specific information
            - References products, services, or processes that might be documented
            - Needs context about business operations, policies, or procedures
            - Could be answered better with internal documentation
            - Asks for specific data, metrics, or information that might be in documents
            
            Respond with only "YES" if knowledgebase context would be helpful, or "NO" if it's a general question that doesn't need organizational context.
            """
            
            response = await gemini_service.generate_text(analysis_prompt)
            decision = response.strip().upper()
            
            should_query = decision == "YES"
            logger.info(f"LLM decision for knowledgebase query: {should_query} (message: '{message[:50]}...')")
            
            return should_query
            
        except Exception as e:
            logger.error(f"Error in LLM analysis for knowledgebase query: {e}")
            # Default to querying if we can't determine
            return True
    
    async def generate_knowledge_query(self, message: str) -> str:
        """
        Use LLM to generate an optimized search query for the knowledgebase.
        
        Args:
            message: Original user message
            
        Returns:
            Optimized search query
        """
        try:
            query_prompt = f"""
            Generate an optimized search query to find relevant information in a knowledgebase for this user message.
            
            User message: "{message}"
            
            Create a search query that:
            - Extracts key concepts and entities
            - Uses relevant keywords that would be in documentation
            - Focuses on the core information need
            - Is concise but comprehensive
            
            Return only the search query, nothing else.
            """
            
            search_query = await gemini_service.generate_text(query_prompt)
            optimized_query = search_query.strip()
            
            logger.info(f"Generated search query: '{optimized_query}' for message: '{message[:50]}...'")
            return optimized_query
            
        except Exception as e:
            logger.error(f"Error generating search query: {e}")
            # Fallback to original message
            return message
    
    async def enhance_message_with_knowledge(
        self,
        message: str,
        organization_id: str,
        auth_token: str
    ) -> str:
        """
        Intelligently enhance a user message with knowledgebase context if beneficial.

        Args:
            message: Original user message
            organization_id: Organization ID
            auth_token: Authentication token for FastBot API

        Returns:
            Enhanced message with knowledge context or original message
        """
        try:
            # Check if we should query knowledgebase
            should_query = await self.should_query_knowledgebase(message, organization_id, auth_token)

            if not should_query:
                logger.debug("LLM determined knowledgebase query not needed")
                return message

            # Generate optimized search query
            search_query = await self.generate_knowledge_query(message)

            # Get knowledge context
            knowledge_results = await self.kb_service.get_knowledge_context(
                query=search_query,
                organization_id=organization_id,
                auth_token=auth_token
            )
            
            if not knowledge_results:
                logger.debug("No relevant knowledge found")
                return message
            
            # Format enhanced message with knowledge context
            enhanced_message = self._format_enhanced_message(message, knowledge_results)
            
            logger.info(f"Enhanced message with {len(knowledge_results)} knowledge items")
            return enhanced_message
            
        except Exception as e:
            logger.error(f"Error enhancing message with knowledge: {e}")
            return message
    
    def _format_enhanced_message(self, original_message: str, knowledge_results: List[str]) -> str:
        """
        Format the enhanced message with knowledge context.
        
        Args:
            original_message: Original user message
            knowledge_results: List of relevant knowledge snippets
            
        Returns:
            Formatted enhanced message
        """
        if not knowledge_results:
            return original_message
        
        context_parts = [
            "## Relevant Organizational Knowledge",
            "Here is relevant information from your organization's knowledge base:",
            ""
        ]
        
        for i, snippet in enumerate(knowledge_results, 1):
            context_parts.append(f"**Knowledge {i}:** {snippet.strip()}")
            context_parts.append("")
        
        context_parts.extend([
            "---",
            "## User Question",
            original_message
        ])
        
        return "\n".join(context_parts)
    
    async def get_knowledge_status(self, organization_id: str, auth_token: str) -> Dict[str, Any]:
        """
        Get the status of knowledgebase for an organization.

        Args:
            organization_id: Organization ID
            auth_token: Authentication token for FastBot API

        Returns:
            Dictionary with knowledgebase status
        """
        try:
            has_kb = await self.kb_service.has_knowledgebase(organization_id, auth_token)
            collection_name = f"kb_{organization_id}"

            status = {
                "has_knowledgebase": has_kb,
                "collection_name": collection_name,
                "organization_id": organization_id,
                "status": "active" if has_kb else "empty"
            }

            return status

        except Exception as e:
            logger.error(f"Error getting knowledge status: {e}")
            return {
                "has_knowledgebase": False,
                "collection_name": f"kb_{organization_id}",
                "organization_id": organization_id,
                "status": "error",
                "error": str(e)
            }


# Global instance
intelligent_knowledge_service = IntelligentKnowledgeService()
