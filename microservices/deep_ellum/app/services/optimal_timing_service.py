"""
Optimal Timing Analysis Service for Content Calendar Agent.
Analyzes audience data and engagement patterns to determine optimal posting times.
"""

from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import pytz
from app.utils.logger import get_logger

logger = get_logger(__name__)


class OptimalTimingService:
    """Service for analyzing optimal posting times based on audience insights and engagement data."""

    def __init__(self):
        # Default optimal times based on general social media best practices
        self.default_optimal_times = {
            "facebook": {
                "optimal_hours": [10, 13, 16, 19],  # 10 AM, 1 PM, 4 PM, 7 PM
                "optimal_days": [2, 3, 4],  # Wednesday through Friday
                "peak_engagement": "13:00-16:00",
                "timezone": "UTC"
            },
            "twitter": {
                "optimal_hours": [9, 12, 15, 18],  # 9 AM, 12 PM, 3 PM, 6 PM
                "optimal_days": [1, 2, 3, 4],  # Tuesday through Friday
                "peak_engagement": "12:00-15:00",
                "timezone": "UTC"
            },
            "instagram": {
                "optimal_hours": [11, 14, 17, 20],  # 11 AM, 2 PM, 5 PM, 8 PM
                "optimal_days": [1, 2, 3, 4, 5],  # Tuesday through Saturday
                "peak_engagement": "14:00-17:00",
                "timezone": "UTC"
            },
            "linkedin": {
                "optimal_hours": [8, 10, 12, 17],  # 8 AM, 10 AM, 12 PM, 5 PM
                "optimal_days": [1, 2, 3, 4],  # Tuesday through Friday
                "peak_engagement": "08:00-12:00",
                "timezone": "UTC"
            }
        }

    async def analyze_optimal_posting_times(
        self, 
        platform: str, 
        content_type: str,
        audience_insights: Dict[str, Any] = None,
        timezone: str = "UTC"
    ) -> Dict[str, Any]:
        """
        Analyze optimal posting times for the given platform and content type.
        
        Args:
            platform: Social media platform (facebook, twitter, instagram, linkedin)
            content_type: Type of content (text, image, video, carousel)
            audience_insights: Audience data and engagement metrics
            timezone: Target timezone for scheduling
            
        Returns:
            Dictionary with optimal timing recommendations
        """
        try:
            # Get base optimal times for the platform
            base_times = self.default_optimal_times.get(platform.lower(), self.default_optimal_times["facebook"])
            
            # Adjust based on audience insights if available
            if audience_insights:
                adjusted_times = await self._adjust_times_for_audience(base_times, audience_insights, platform)
            else:
                adjusted_times = base_times.copy()
            
            # Adjust based on content type
            content_adjusted_times = await self._adjust_times_for_content_type(adjusted_times, content_type, platform)
            
            # Generate specific time recommendations
            recommendations = await self._generate_time_recommendations(
                content_adjusted_times, timezone, content_type
            )
            
            return {
                "platform": platform,
                "content_type": content_type,
                "timezone": timezone,
                "optimal_times": recommendations,
                "reasoning": await self._generate_timing_reasoning(platform, content_type, audience_insights),
                "confidence_score": await self._calculate_confidence_score(audience_insights)
            }

        except Exception as e:
            logger.error(f"Error analyzing optimal posting times: {e}")
            return self._get_fallback_recommendations(platform, timezone)

    async def _adjust_times_for_audience(
        self, 
        base_times: Dict[str, Any], 
        audience_insights: Dict[str, Any],
        platform: str
    ) -> Dict[str, Any]:
        """Adjust optimal times based on audience insights."""
        adjusted_times = base_times.copy()
        
        try:
            # Check for platform-specific insights
            if platform == "twitter" and "posting_schedule" in audience_insights:
                schedule_data = audience_insights["posting_schedule"]
                if "optimal_hours" in schedule_data:
                    adjusted_times["optimal_hours"] = schedule_data["optimal_hours"]
                if "optimal_days" in schedule_data:
                    adjusted_times["optimal_days"] = schedule_data["optimal_days"]
            
            elif platform == "facebook" and "audience_demographics" in audience_insights:
                demo_data = audience_insights["audience_demographics"]
                if "optimal_hours" in demo_data:
                    adjusted_times["optimal_hours"] = demo_data["optimal_hours"]
            
            # Adjust based on engagement metrics
            if "engagement_metrics" in audience_insights:
                engagement = audience_insights["engagement_metrics"]
                # If engagement data shows different peak times, adjust accordingly
                # This is a simplified implementation - in practice, you'd analyze the data more thoroughly
                
        except Exception as e:
            logger.warning(f"Error adjusting times for audience: {e}")
        
        return adjusted_times

    async def _adjust_times_for_content_type(
        self, 
        base_times: Dict[str, Any], 
        content_type: str,
        platform: str
    ) -> Dict[str, Any]:
        """Adjust optimal times based on content type."""
        adjusted_times = base_times.copy()
        
        try:
            # Video content typically performs better in evening hours
            if content_type.lower() in ["video", "carousel"]:
                if platform.lower() in ["instagram", "facebook"]:
                    # Shift times slightly later for video content
                    adjusted_times["optimal_hours"] = [h + 1 for h in adjusted_times["optimal_hours"] if h + 1 <= 21]
            
            # Text content performs well during business hours
            elif content_type.lower() == "text":
                if platform.lower() == "linkedin":
                    # Keep business hours for LinkedIn text content
                    adjusted_times["optimal_hours"] = [8, 10, 12, 14, 17]
            
            # Image content is versatile but performs well during lunch and evening
            elif content_type.lower() == "image":
                # No major adjustments needed for image content
                pass
                
        except Exception as e:
            logger.warning(f"Error adjusting times for content type: {e}")
        
        return adjusted_times

    async def _generate_time_recommendations(
        self, 
        optimal_times: Dict[str, Any], 
        timezone: str,
        content_type: str
    ) -> List[Dict[str, Any]]:
        """Generate specific time recommendations for the next week."""
        recommendations = []
        
        try:
            tz = pytz.timezone(timezone)
            now = datetime.now(tz)
            
            # Generate recommendations for the next 7 days
            for day_offset in range(7):
                target_date = now + timedelta(days=day_offset)
                day_of_week = target_date.weekday()  # 0=Monday, 6=Sunday
                
                # Check if this day is in optimal days
                if day_of_week in optimal_times.get("optimal_days", []):
                    for hour in optimal_times.get("optimal_hours", []):
                        recommended_time = target_date.replace(
                            hour=hour, 
                            minute=0, 
                            second=0, 
                            microsecond=0
                        )
                        
                        # Only recommend future times
                        if recommended_time > now:
                            recommendations.append({
                                "datetime": recommended_time.isoformat(),
                                "day_name": recommended_time.strftime("%A"),
                                "time_display": recommended_time.strftime("%I:%M %p"),
                                "priority": await self._calculate_time_priority(hour, day_of_week, content_type),
                                "reasoning": f"Optimal {content_type} posting time based on audience engagement patterns"
                            })
            
            # Sort by priority (highest first)
            recommendations.sort(key=lambda x: x["priority"], reverse=True)
            
            # Return top 10 recommendations
            return recommendations[:10]
            
        except Exception as e:
            logger.error(f"Error generating time recommendations: {e}")
            return []

    async def _calculate_time_priority(self, hour: int, day_of_week: int, content_type: str) -> float:
        """Calculate priority score for a specific time slot."""
        priority = 0.5  # Base priority
        
        # Higher priority for peak engagement hours
        if 12 <= hour <= 15:  # Lunch time peak
            priority += 0.3
        elif 18 <= hour <= 20:  # Evening peak
            priority += 0.2
        elif 9 <= hour <= 11:  # Morning peak
            priority += 0.1
        
        # Higher priority for weekdays
        if 0 <= day_of_week <= 4:  # Monday to Friday
            priority += 0.2
        
        # Content type adjustments
        if content_type.lower() == "video" and 17 <= hour <= 20:
            priority += 0.1
        elif content_type.lower() == "text" and 9 <= hour <= 17:
            priority += 0.1
        
        return min(priority, 1.0)  # Cap at 1.0

    async def _generate_timing_reasoning(
        self, 
        platform: str, 
        content_type: str,
        audience_insights: Dict[str, Any] = None
    ) -> str:
        """Generate human-readable reasoning for the timing recommendations."""
        reasoning_parts = []
        
        # Platform-specific reasoning
        platform_reasoning = {
            "facebook": "Facebook users are most active during lunch hours and early evening",
            "twitter": "Twitter engagement peaks during business hours and early afternoon",
            "instagram": "Instagram users engage most during lunch and evening hours",
            "linkedin": "LinkedIn is most effective during business hours on weekdays"
        }
        
        reasoning_parts.append(platform_reasoning.get(platform, "General social media best practices"))
        
        # Content type reasoning
        content_reasoning = {
            "video": "Video content performs better in the evening when users have more time to watch",
            "image": "Image content is versatile and performs well throughout peak hours",
            "text": "Text content is most effective during business hours when users are actively reading",
            "carousel": "Carousel posts benefit from evening engagement when users browse more leisurely"
        }
        
        reasoning_parts.append(content_reasoning.get(content_type, "Content optimized for general engagement"))
        
        # Audience insights reasoning
        if audience_insights and "engagement_metrics" in audience_insights:
            reasoning_parts.append("Timing adjusted based on your audience's historical engagement patterns")
        else:
            reasoning_parts.append("Recommendations based on industry best practices (connect analytics for personalized timing)")
        
        return ". ".join(reasoning_parts) + "."

    async def _calculate_confidence_score(self, audience_insights: Dict[str, Any] = None) -> float:
        """Calculate confidence score for the recommendations."""
        if not audience_insights:
            return 0.6  # Medium confidence with default data
        
        confidence = 0.6
        
        # Increase confidence if we have engagement metrics
        if "engagement_metrics" in audience_insights:
            confidence += 0.2
        
        # Increase confidence if we have platform-specific insights
        if "posting_schedule" in audience_insights or "audience_demographics" in audience_insights:
            confidence += 0.2
        
        return min(confidence, 1.0)

    def _get_fallback_recommendations(self, platform: str, timezone: str) -> Dict[str, Any]:
        """Get fallback recommendations if analysis fails."""
        return {
            "platform": platform,
            "content_type": "general",
            "timezone": timezone,
            "optimal_times": [],
            "reasoning": "Using default recommendations due to analysis error",
            "confidence_score": 0.3
        }


# Singleton instance
optimal_timing_service = OptimalTimingService()
