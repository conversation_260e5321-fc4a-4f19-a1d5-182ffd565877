from sqlalchemy import Column, String, Text, DateTime, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, JSON, Float
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database.database import Base
import uuid


class CustomAgent(Base):
    """Model for storing custom AI agents (true autonomous agents only)."""
    __tablename__ = "custom_agents"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    personality = Column(Text, nullable=True)
    instructions = Column(Text, nullable=False)
    capabilities = Column(JSON, nullable=True)  # List of capabilities/tools
    is_active = Column(Boolean, default=True)
    is_sample = Column(Boolean, default=False)  # True for pre-built sample agents
    created_by = Column(String(255), nullable=False)  # User ID
    organization_id = Column(String(255), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    conversations = relationship("AgentConversation", back_populates="agent")


class CustomChatbot(Base):
    """Model for storing custom AI chatbots (conversational assistants)."""
    __tablename__ = "custom_chatbots"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    personality = Column(Text, nullable=True)
    instructions = Column(Text, nullable=False)
    capabilities = Column(JSON, nullable=True)  # List of capabilities/tools
    is_active = Column(Boolean, default=True)
    is_sample = Column(Boolean, default=False)  # True for pre-built sample chatbots
    created_by = Column(String(255), nullable=False)  # User ID
    organization_id = Column(String(255), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    conversations = relationship("ChatbotConversation", back_populates="chatbot")


class AgentConversation(Base):
    """Model for storing conversations with agents."""
    __tablename__ = "agent_conversations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    agent_id = Column(UUID(as_uuid=True), ForeignKey("custom_agents.id"), nullable=False)
    user_id = Column(String(255), nullable=False)
    title = Column(String(500), nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    agent = relationship("CustomAgent", back_populates="conversations")
    messages = relationship("ConversationMessage", back_populates="conversation")


class ChatbotConversation(Base):
    """Model for storing conversations with chatbots."""
    __tablename__ = "chatbot_conversations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    chatbot_id = Column(UUID(as_uuid=True), ForeignKey("custom_chatbots.id"), nullable=False)
    user_id = Column(String(255), nullable=False)
    title = Column(String(500), nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    chatbot = relationship("CustomChatbot", back_populates="conversations")
    messages = relationship("ChatbotMessage", back_populates="conversation")


class OrganizationSettings(Base):
    """Model for storing organization-wide settings."""
    __tablename__ = "organization_settings"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(String(255), nullable=False, unique=True)  # One setting per org
    knowledgebase_enabled = Column(Boolean, default=False)
    socials_database_enabled = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class ConversationMessage(Base):
    """Model for storing individual messages in agent conversations."""
    __tablename__ = "conversation_messages"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    conversation_id = Column(UUID(as_uuid=True), ForeignKey("agent_conversations.id"), nullable=False)
    role = Column(String(50), nullable=False)  # 'user', 'assistant', 'system'
    content = Column(Text, nullable=False)
    message_metadata = Column(JSON, nullable=True)  # Additional message metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    conversation = relationship("AgentConversation", back_populates="messages")


class ChatbotMessage(Base):
    """Model for storing individual messages in chatbot conversations."""
    __tablename__ = "chatbot_messages"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    conversation_id = Column(UUID(as_uuid=True), ForeignKey("chatbot_conversations.id"), nullable=False)
    role = Column(String(50), nullable=False)  # 'user', 'assistant', 'system'
    content = Column(Text, nullable=False)
    message_metadata = Column(JSON, nullable=True)  # Additional message metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    conversation = relationship("ChatbotConversation", back_populates="messages")


class AgentTemplate(Base):
    """Model for storing agent templates that users can customize."""
    __tablename__ = "agent_templates"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    category = Column(String(100), nullable=False)  # e.g., 'development', 'accessibility', 'writing'
    template_instructions = Column(Text, nullable=False)
    default_capabilities = Column(JSON, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class ResearchProject(Base):
    """Model for storing research projects and their configurations."""
    __tablename__ = "research_projects"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    organization_id = Column(String(255), nullable=False)  # Organization isolation
    created_by = Column(String(255), nullable=False)  # User ID
    project_type = Column(String(100), nullable=False)  # 'competitor_monitoring', 'trend_analysis', 'market_research'
    configuration = Column(JSON, nullable=True)  # Project-specific settings
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    research_findings = relationship("ResearchFinding", back_populates="project")
    competitor_profiles = relationship("CompetitorProfile", back_populates="project")


class CompetitorProfile(Base):
    """Model for storing competitor information and profiles."""
    __tablename__ = "competitor_profiles"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("research_projects.id"), nullable=False)
    organization_id = Column(String(255), nullable=False)  # Organization isolation
    competitor_name = Column(String(255), nullable=False)
    industry = Column(String(100), nullable=True)
    website = Column(String(500), nullable=True)
    description = Column(Text, nullable=True)
    keywords = Column(JSON, nullable=True)  # List of keywords to monitor
    monitoring_frequency = Column(String(50), default="daily")  # 'hourly', 'daily', 'weekly'
    last_monitored = Column(DateTime(timezone=True), nullable=True)
    profile_data = Column(JSON, nullable=True)  # Additional competitor data
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    project = relationship("ResearchProject", back_populates="competitor_profiles")
    research_findings = relationship("ResearchFinding", back_populates="competitor")


class ResearchFinding(Base):
    """Model for storing individual research findings and insights."""
    __tablename__ = "research_findings"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("research_projects.id"), nullable=False)
    competitor_id = Column(UUID(as_uuid=True), ForeignKey("competitor_profiles.id"), nullable=True)
    organization_id = Column(String(255), nullable=False)  # Organization isolation
    finding_type = Column(String(100), nullable=False)  # 'news', 'trend', 'announcement', 'analysis'
    title = Column(String(500), nullable=False)
    content = Column(Text, nullable=False)
    source_url = Column(String(1000), nullable=True)
    source_type = Column(String(100), nullable=True)  # 'web_search', 'news', 'social_media'
    relevance_score = Column(Float, nullable=True)  # 0.0 to 1.0
    sentiment_score = Column(Float, nullable=True)  # -1.0 to 1.0
    keywords = Column(JSON, nullable=True)  # Keywords that triggered this finding
    finding_metadata = Column(JSON, nullable=True)  # Additional finding metadata
    is_significant = Column(Boolean, default=False)  # Marked as significant finding
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    project = relationship("ResearchProject", back_populates="research_findings")
    competitor = relationship("CompetitorProfile", back_populates="research_findings")


class TrendAnalysis(Base):
    """Model for storing trend analysis results and insights."""
    __tablename__ = "trend_analyses"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(String(255), nullable=False)  # Organization isolation
    topic = Column(String(255), nullable=False)
    time_period = Column(String(50), nullable=False)  # '1w', '1m', '3m', '6m', '1y'
    region = Column(String(100), nullable=True)
    analysis_type = Column(String(100), nullable=False)  # 'market_trend', 'technology_trend', 'consumer_trend'
    trend_data = Column(JSON, nullable=False)  # Structured trend analysis results
    key_insights = Column(JSON, nullable=True)  # Key insights and patterns
    growth_indicators = Column(JSON, nullable=True)  # Growth/decline indicators
    emerging_themes = Column(JSON, nullable=True)  # Emerging themes and topics
    confidence_score = Column(Float, nullable=True)  # 0.0 to 1.0
    data_sources_count = Column(Integer, default=0)  # Number of sources analyzed
    created_by = Column(String(255), nullable=False)  # User ID
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class ResearchReport(Base):
    """Model for storing generated research reports."""
    __tablename__ = "research_reports"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(String(255), nullable=False)  # Organization isolation
    title = Column(String(500), nullable=False)
    report_type = Column(String(100), nullable=False)  # 'competitor_analysis', 'market_research', 'trend_report'
    executive_summary = Column(Text, nullable=True)
    content = Column(Text, nullable=False)  # Full report content
    findings_summary = Column(JSON, nullable=True)  # Structured summary of findings
    recommendations = Column(JSON, nullable=True)  # Strategic recommendations
    data_sources = Column(JSON, nullable=True)  # Sources used in the report
    report_metadata = Column(JSON, nullable=True)  # Additional report metadata
    generated_by = Column(String(255), nullable=False)  # User ID
    is_published = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class ResearchAlert(Base):
    """Model for storing research alerts and notifications."""
    __tablename__ = "research_alerts"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(String(255), nullable=False)
    alert_type = Column(String(100), nullable=False)  # 'competitor_activity', 'trend_change', 'significant_finding'
    title = Column(String(500), nullable=False)
    message = Column(Text, nullable=False)
    severity = Column(String(50), default="medium")  # 'low', 'medium', 'high', 'critical'
    trigger_data = Column(JSON, nullable=True)  # Data that triggered the alert
    related_finding_id = Column(UUID(as_uuid=True), ForeignKey("research_findings.id"), nullable=True)
    related_competitor_id = Column(UUID(as_uuid=True), ForeignKey("competitor_profiles.id"), nullable=True)
    is_read = Column(Boolean, default=False)
    is_dismissed = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    related_finding = relationship("ResearchFinding")
    related_competitor = relationship("CompetitorProfile")


# Temporarily comment out database models to fix startup issue
# Will be added back after fixing the metadata column name conflict

# class DeepResearchTask(Base):
#     """Model for storing OpenAI deep research tasks."""
#     __tablename__ = "deep_research_tasks"
#     # ... model definition will be added back later
