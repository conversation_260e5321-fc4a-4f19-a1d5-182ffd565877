from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from app.models.schemas import AgentCapability
from app.utils.logger import get_logger

logger = get_logger(__name__)


class BaseChatbot(ABC):
    """Base class for all AI chatbots."""
    
    def __init__(
        self, 
        name: str, 
        description: str, 
        instructions: str,
        personality: Optional[str] = None,
        capabilities: List[AgentCapability] = None
    ):
        self.name = name
        self.description = description
        self.instructions = instructions
        self.personality = personality
        self.capabilities = capabilities or []
        self.context = {}
    
    @abstractmethod
    async def process_message(self, message: str, context: Dict[str, Any] = None) -> str:
        """Process a user message and return a response."""
        pass
    
    def add_context(self, key: str, value: Any):
        """Add context information for the chatbot."""
        self.context[key] = value
    
    def get_context(self, key: str, default: Any = None) -> Any:
        """Get context information."""
        return self.context.get(key, default)
    
    def clear_context(self):
        """Clear all context information."""
        self.context.clear()
    
    def get_system_prompt(self) -> str:
        """Get the system prompt for this chatbot."""
        prompt = f"""
        You are {self.name}, an AI chatbot assistant with the following description:
        {self.description}
        
        Instructions:
        {self.instructions}
        """
        
        if self.personality:
            prompt += f"\n\nPersonality and Communication Style:\n{self.personality}"
        
        if self.capabilities:
            capabilities_text = ", ".join([cap.value for cap in self.capabilities])
            prompt += f"\n\nYour capabilities include: {capabilities_text}"
        
        return prompt.strip()


class LangChainChatbot(BaseChatbot):
    """Chatbot implementation using LangChain."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from app.services.langchain_service import langchain_service
        self.langchain_service = langchain_service
    
    async def process_message(self, message: str, context: Dict[str, Any] = None) -> str:
        """Process message using LangChain service."""
        try:
            # Get conversation history from context
            conversation_history = context.get("conversation_history", []) if context else []
            
            # Process the message
            response = await self.langchain_service.process_message(
                message=message,
                instructions=self.instructions,
                conversation_history=conversation_history,
                personality=self.personality,
                capabilities=[cap.value for cap in self.capabilities]
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing message in {self.name}: {e}")
            return "I apologize, but I encountered an error processing your request. Please try again."


class LangGraphChatbot(BaseChatbot):
    """Chatbot implementation using LangGraph workflows."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from app.services.langchain_service import langchain_service
        self.langchain_service = langchain_service
    
    async def process_message(self, message: str, context: Dict[str, Any] = None) -> str:
        """Process message using LangGraph workflow."""
        try:
            # Get conversation history from context
            conversation_history = context.get("conversation_history", []) if context else []
            
            # Run the chatbot workflow
            response = await self.langchain_service.run_agent_workflow(
                message=message,
                instructions=self.instructions,
                conversation_history=conversation_history,
                capabilities=[cap.value for cap in self.capabilities]
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing message in {self.name}: {e}")
            return "I apologize, but I encountered an error processing your request. Please try again."
