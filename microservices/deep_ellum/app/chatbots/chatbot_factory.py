from typing import Dict, Any, Optional, List
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from app.models.models import CustomChatbot
from app.models.schemas import AgentCapability
from app.chatbots.base_chatbot import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LangGraphChatbot
from app.chatbots.sample_chatbots import get_sample_chatbot, SAMPLE_CHATBOTS
from app.utils.logger import get_logger
from datetime import datetime, timedelta

logger = get_logger(__name__)


class ChatbotFactory:
    """Factory for creating and managing chatbot instances."""
    
    def __init__(self):
        self._chatbot_registry = {}
        self._registry_cache_time = None
        self._cache_duration = timedelta(minutes=5)  # Cache for 5 minutes
    
    async def get_chatbot_registry(self, db: AsyncSession) -> Dict[str, Any]:
        """Get registry of all available chatbots with caching."""
        current_time = datetime.now()
        
        # Check if cache is still valid
        if (self._registry_cache_time and 
            current_time - self._registry_cache_time < self._cache_duration and
            self._chatbot_registry):
            return self._chatbot_registry
        
        try:
            # Get all active chatbots from database
            result = await db.execute(
                select(CustomChatbot).where(CustomChatbot.is_active == True)
            )
            chatbots = result.scalars().all()
            
            registry = {}
            
            # Add database chatbots
            for chatbot in chatbots:
                registry[str(chatbot.id)] = {
                    "id": str(chatbot.id),
                    "name": chatbot.name,
                    "description": chatbot.description,
                    "capabilities": chatbot.capabilities or [],
                    "is_sample": chatbot.is_sample,
                    "created_by": chatbot.created_by,
                    "organization_id": chatbot.organization_id
                }
            
            # Add sample chatbots
            for chatbot_type, chatbot_class in SAMPLE_CHATBOTS.items():
                try:
                    chatbot_instance = chatbot_class()
                    registry[f"sample_{chatbot_type}"] = {
                        "id": f"sample_{chatbot_type}",
                        "name": chatbot_instance.name,
                        "description": chatbot_instance.description,
                        "capabilities": [cap.value for cap in chatbot_instance.capabilities],
                        "is_sample": True,
                        "created_by": "system",
                        "organization_id": None
                    }
                except Exception as e:
                    logger.error(f"Error creating sample chatbot {chatbot_type}: {e}")
            
            # Update cache
            self._chatbot_registry = registry
            self._registry_cache_time = current_time
            
            return registry
            
        except Exception as e:
            logger.error(f"Error getting chatbot registry: {e}")
            return {}
    
    def invalidate_registry_cache(self):
        """Invalidate the chatbot registry cache."""
        self._registry_cache_time = None
        self._chatbot_registry = {}
    
    def _create_chatbot_instance(self, chatbot_record: CustomChatbot) -> LangChainChatbot:
        """Create a chatbot instance from a database record."""
        # Convert capabilities from JSON to enum list
        capabilities = []
        if chatbot_record.capabilities:
            for cap in chatbot_record.capabilities:
                try:
                    capabilities.append(AgentCapability(cap))
                except ValueError:
                    logger.warning(f"Unknown capability: {cap}")
        
        # Determine chatbot type based on capabilities or use default
        if AgentCapability.CODE_GENERATION in capabilities:
            chatbot_class = LangGraphChatbot  # Use LangGraph for complex code generation
        else:
            chatbot_class = LangChainChatbot  # Use LangChain for simpler interactions
        
        # Create chatbot instance
        chatbot = chatbot_class(
            name=chatbot_record.name,
            description=chatbot_record.description,
            instructions=chatbot_record.instructions,
            personality=chatbot_record.personality,
            capabilities=capabilities
        )
        
        return chatbot
    
    def create_sample_chatbot(self, chatbot_type: str) -> Optional[Any]:
        """Create a sample chatbot by type."""
        try:
            return get_sample_chatbot(chatbot_type)
        except ValueError as e:
            logger.error(f"Error creating sample chatbot: {e}")
            return None
    
    async def get_chatbot(self, db: AsyncSession, chatbot_id: UUID) -> Optional[Any]:
        """Get a chatbot instance, creating it if necessary."""
        try:
            # First check if this is a sample chatbot by checking the database
            result = await db.execute(
                select(CustomChatbot).where(CustomChatbot.id == chatbot_id)
            )
            chatbot_record = result.scalar_one_or_none()

            if chatbot_record and chatbot_record.is_sample:
                # For sample chatbots, check if we have a specialized implementation
                if chatbot_record.name == "Code Generator Chatbot":
                    from app.chatbots.sample_chatbots import CodeGeneratorChatbot
                    return CodeGeneratorChatbot()
                elif chatbot_record.name == "Accessibility Advisor Chatbot":
                    from app.chatbots.sample_chatbots import AccessibilityAdvisorChatbot
                    return AccessibilityAdvisorChatbot()
                elif chatbot_record.name == "Documentation Specialist Chatbot":
                    from app.chatbots.sample_chatbots import DocumentationSpecialistChatbot
                    return DocumentationSpecialistChatbot()
                elif chatbot_record.name == "Orchestrator Chatbot":
                    from app.chatbots.sample_chatbots import OrchestratorChatbot
                    return OrchestratorChatbot()

            # For non-sample chatbots or sample chatbots without specialized implementations,
            # create from database record
            return await self.create_chatbot_from_db(db, chatbot_id)
            
        except Exception as e:
            logger.error(f"Error getting chatbot {chatbot_id}: {e}")
            return None
    
    async def create_chatbot_from_db(self, db: AsyncSession, chatbot_id: UUID) -> Optional[Any]:
        """Create a chatbot instance from database record."""
        try:
            result = await db.execute(
                select(CustomChatbot).where(
                    and_(
                        CustomChatbot.id == chatbot_id,
                        CustomChatbot.is_active == True
                    )
                )
            )
            chatbot_record = result.scalar_one_or_none()
            
            if not chatbot_record:
                logger.warning(f"Chatbot {chatbot_id} not found or inactive")
                return None
            
            return self._create_chatbot_instance(chatbot_record)
            
        except Exception as e:
            logger.error(f"Error creating chatbot from database: {e}")
            return None
    
    async def list_chatbots_for_organization(
        self, 
        db: AsyncSession, 
        organization_id: str,
        include_samples: bool = True
    ) -> List[Dict[str, Any]]:
        """List all chatbots available to an organization."""
        try:
            conditions = [CustomChatbot.is_active == True]
            
            if include_samples:
                # Include both organization chatbots and sample chatbots
                conditions.append(
                    (CustomChatbot.organization_id == organization_id) |
                    (CustomChatbot.is_sample == True)
                )
            else:
                # Only organization-specific chatbots
                conditions.append(CustomChatbot.organization_id == organization_id)
            
            result = await db.execute(
                select(CustomChatbot).where(and_(*conditions))
            )
            chatbots = result.scalars().all()
            
            chatbot_list = []
            for chatbot in chatbots:
                chatbot_list.append({
                    "id": str(chatbot.id),
                    "name": chatbot.name,
                    "description": chatbot.description,
                    "capabilities": chatbot.capabilities or [],
                    "is_sample": chatbot.is_sample,
                    "created_by": chatbot.created_by,
                    "organization_id": chatbot.organization_id,
                    "created_at": chatbot.created_at,
                    "updated_at": chatbot.updated_at
                })
            
            return chatbot_list
            
        except Exception as e:
            logger.error(f"Error listing chatbots for organization {organization_id}: {e}")
            return []
    
    async def create_custom_chatbot(
        self,
        name: str,
        description: str,
        instructions: str,
        personality: Optional[str] = None,
        capabilities: List[AgentCapability] = None
    ) -> LangChainChatbot:
        """Create a custom chatbot instance without saving to database."""
        capabilities = capabilities or []
        
        # Determine chatbot type
        if AgentCapability.CODE_GENERATION in capabilities:
            chatbot_class = LangGraphChatbot
        else:
            chatbot_class = LangChainChatbot
        
        chatbot = chatbot_class(
            name=name,
            description=description,
            instructions=instructions,
            personality=personality,
            capabilities=capabilities
        )
        
        return chatbot


# Global chatbot factory instance
chatbot_factory = ChatbotFactory()
