"""
LangGraph workflows for automated research processes, competitor monitoring, and report generation.
"""

import json
from datetime import datetime
from typing import Dict, List, Any, TypedDict, Annotated
from uuid import UUID

from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from app.tools.research_tools import RESEARCH_TOOLS
from app.utils.logger import get_logger
from app.core.config import settings
from app.services.ai_provider_service import ai_provider_service

logger = get_logger(__name__)


class ResearchState(TypedDict):
    """State for research workflows."""
    messages: Annotated[List[BaseMessage], add_messages]
    research_query: str
    research_type: str  # 'competitor_analysis', 'trend_analysis', 'market_research'
    organization_id: str
    user_id: str

    # Research parameters
    competitor_name: str
    keywords: List[str]
    topic: str
    time_period: str
    region: str
    sources: List[str]
    focus_areas: List[str]
    research_depth: str  # 'overview', 'detailed', 'comprehensive'
    specific_focus: str  # Specific aspect or angle of research

    # Results
    search_results: Dict[str, Any]
    competitor_data: Dict[str, Any]
    trend_data: Dict[str, Any]
    aggregated_data: Dict[str, Any]
    final_report: str

    # Workflow control
    next_step: str
    error_message: str
    confidence_score: float


class ResearchWorkflows:
    """LangGraph workflows for research automation."""
    
    def __init__(self):
        self.tools = {tool.name: tool for tool in RESEARCH_TOOLS}
        
    def create_competitor_analysis_workflow(self) -> StateGraph:
        """Create a workflow for comprehensive competitor analysis."""
        
        def analyze_competitor_request(state: ResearchState) -> ResearchState:
            """Analyze the competitor research request and extract parameters."""
            try:
                query = state.get("research_query", "")
                
                # Extract competitor name from query (simplified)
                # In production, use NLP for better extraction
                words = query.split()
                competitor_name = ""
                for word in words:
                    if word[0].isupper() and len(word) > 2:
                        competitor_name = word
                        break
                
                if not competitor_name:
                    competitor_name = state.get("competitor_name", "")
                
                # Extract keywords
                business_keywords = ["product", "service", "market", "strategy", "launch", "partnership"]
                keywords = [kw for kw in business_keywords if kw in query.lower()]
                
                state["competitor_name"] = competitor_name
                state["keywords"] = keywords or ["general"]
                state["next_step"] = "gather_competitor_data" if competitor_name else "error"
                
                return state
                
            except Exception as e:
                logger.error(f"Error analyzing competitor request: {e}")
                state["error_message"] = str(e)
                state["next_step"] = "error"
                return state
        
        def gather_competitor_data(state: ResearchState) -> ResearchState:
            """Gather comprehensive competitor data."""
            try:
                competitor_name = state["competitor_name"]
                keywords = ",".join(state["keywords"])
                
                # Use competitor monitoring tool
                tool = self.tools["competitor_monitoring"]
                result = tool.invoke({
                    "competitor_name": competitor_name,
                    "keywords": keywords,
                    "time_range": "1w"
                })
                
                state["competitor_data"] = json.loads(result)
                state["next_step"] = "analyze_market_position"
                
                return state
                
            except Exception as e:
                logger.error(f"Error gathering competitor data: {e}")
                state["error_message"] = str(e)
                state["next_step"] = "error"
                return state
        
        def analyze_market_position(state: ResearchState) -> ResearchState:
            """Analyze competitor's market position and trends."""
            try:
                competitor_name = state["competitor_name"]
                
                # Perform trend analysis for the competitor's industry
                tool = self.tools["trend_analysis"]
                result = tool.invoke({
                    "topic": f"{competitor_name} industry market",
                    "time_period": "3m"
                })
                
                state["trend_data"] = json.loads(result)
                state["next_step"] = "generate_competitor_report"
                
                return state
                
            except Exception as e:
                logger.error(f"Error analyzing market position: {e}")
                state["error_message"] = str(e)
                state["next_step"] = "error"
                return state
        
        def generate_competitor_report(state: ResearchState) -> ResearchState:
            """Generate comprehensive competitor analysis report."""
            try:
                competitor_data = state.get("competitor_data", {})
                trend_data = state.get("trend_data", {})
                competitor_name = state["competitor_name"]
                
                # Generate structured report
                report = f"""# Competitor Analysis Report: {competitor_name}
                
## Executive Summary
{competitor_data.get('summary', 'No summary available')}

## Recent Activities
"""
                
                # Add recent activities
                activities = competitor_data.get("monitoring_results", [])
                for i, activity in enumerate(activities[:5], 1):
                    title = activity.get("title", "No title")
                    snippet = activity.get("snippet", "No description")
                    score = activity.get("relevance_score", 0)
                    report += f"{i}. **{title}** (Relevance: {score})\n   {snippet}\n\n"
                
                # Add trend analysis
                report += "## Market Trends\n"
                trends = trend_data.get("trend_analysis", {})
                for trend in trends.get("key_trends", [])[:3]:
                    report += f"• {trend}\n"
                
                # Add strategic recommendations
                report += "\n## Strategic Recommendations\n"
                report += f"• Monitor {competitor_name}'s product development activities\n"
                report += f"• Track partnership announcements and market expansion\n"
                report += f"• Analyze competitive positioning in key market segments\n"
                
                state["final_report"] = report
                state["confidence_score"] = 0.8
                state["next_step"] = "complete"
                
                return state
                
            except Exception as e:
                logger.error(f"Error generating competitor report: {e}")
                state["error_message"] = str(e)
                state["next_step"] = "error"
                return state
        
        def handle_error(state: ResearchState) -> ResearchState:
            """Handle workflow errors."""
            error_msg = state.get("error_message", "Unknown error occurred")
            state["final_report"] = f"Error in competitor analysis: {error_msg}"
            state["confidence_score"] = 0.0
            return state
        
        def complete_workflow(state: ResearchState) -> ResearchState:
            """Complete the workflow."""
            return state
        
        # Build the workflow graph
        workflow = StateGraph(ResearchState)
        
        # Add nodes
        workflow.add_node("analyze_request", analyze_competitor_request)
        workflow.add_node("gather_data", gather_competitor_data)
        workflow.add_node("analyze_position", analyze_market_position)
        workflow.add_node("generate_report", generate_competitor_report)
        workflow.add_node("error", handle_error)
        workflow.add_node("complete", complete_workflow)
        
        # Add edges
        workflow.set_entry_point("analyze_request")
        
        workflow.add_conditional_edges(
            "analyze_request",
            lambda state: state["next_step"],
            {
                "gather_competitor_data": "gather_data",
                "error": "error"
            }
        )
        
        workflow.add_conditional_edges(
            "gather_data",
            lambda state: state["next_step"],
            {
                "analyze_market_position": "analyze_position",
                "error": "error"
            }
        )
        
        workflow.add_conditional_edges(
            "analyze_position",
            lambda state: state["next_step"],
            {
                "generate_competitor_report": "generate_report",
                "error": "error"
            }
        )
        
        workflow.add_conditional_edges(
            "generate_report",
            lambda state: state["next_step"],
            {
                "complete": "complete",
                "error": "error"
            }
        )
        
        workflow.add_edge("error", END)
        workflow.add_edge("complete", END)
        
        return workflow.compile()
    
    def create_trend_analysis_workflow(self) -> StateGraph:
        """Create a workflow for comprehensive trend analysis."""
        
        def analyze_trend_request(state: ResearchState) -> ResearchState:
            """Analyze the trend research request using LLM intelligence."""
            try:
                query = state.get("research_query", "")

                # Use LLM to intelligently extract research parameters
                llm = ai_provider_service.get_llm(temperature=0.1)

                extraction_prompt = f"""
                Analyze this research request and extract comprehensive research parameters:

                Request: "{query}"

                Extract and return ONLY a JSON object with these fields:
                {{
                    "main_topic": "the primary subject/technology/industry being researched",
                    "specific_focus": "the specific aspect or angle of research",
                    "time_period": "appropriate time period (1w, 1m, 3m, 6m, 1y)",
                    "search_keywords": ["comprehensive", "list", "of", "diverse", "search", "terms", "including", "technical", "business", "market", "terms"],
                    "research_depth": "comprehensive"
                }}

                SEARCH KEYWORD STRATEGY:
                - Include the main topic name and variations
                - Add technical/product-specific terms
                - Include business and market terms
                - Add competitive and industry terms
                - Include recent developments and news terms
                - Add analysis and review terms
                - Minimum 8-12 diverse keywords for comprehensive coverage

                Example:
                Request: "give me in depth research on kimi k2 AI"
                Response: {{
                    "main_topic": "Kimi K2 AI",
                    "specific_focus": "comprehensive analysis of capabilities, market position, and competitive landscape",
                    "time_period": "6m",
                    "search_keywords": [
                        "Kimi K2 AI", "Moonshot AI Kimi K2", "Kimi K2 model", "Kimi K2 capabilities",
                        "Kimi K2 vs ChatGPT", "Kimi K2 vs Claude", "Kimi K2 performance", "Kimi K2 pricing",
                        "Moonshot AI company", "Chinese AI models", "Kimi K2 review", "Kimi K2 analysis",
                        "Kimi K2 features", "Kimi K2 API", "Kimi K2 benchmarks", "Alibaba Moonshot AI"
                    ],
                    "research_depth": "comprehensive"
                }}
                """

                response = llm.invoke(extraction_prompt)

                # Parse LLM response
                import json
                import re

                # Extract JSON from response
                json_match = re.search(r'\{.*\}', response.content, re.DOTALL)
                if json_match:
                    extracted_data = json.loads(json_match.group())

                    state["topic"] = extracted_data.get("main_topic", "")
                    state["time_period"] = extracted_data.get("time_period", "3m")
                    state["keywords"] = extracted_data.get("search_keywords", [])
                    state["research_depth"] = extracted_data.get("research_depth", "detailed")
                    state["specific_focus"] = extracted_data.get("specific_focus", "")

                    if state["topic"]:
                        state["next_step"] = "gather_trend_data"
                    else:
                        state["error_message"] = "Could not extract main topic from request"
                        state["next_step"] = "error"
                else:
                    # Fallback to simple extraction
                    state["topic"] = query
                    state["time_period"] = "3m"
                    state["keywords"] = [query]
                    state["next_step"] = "gather_trend_data"

                return state

            except Exception as e:
                logger.error(f"Error analyzing trend request: {e}")
                state["error_message"] = str(e)
                state["next_step"] = "error"
                return state
        
        def gather_trend_data(state: ResearchState) -> ResearchState:
            """Gather comprehensive trend data using intelligent search."""
            try:
                topic = state["topic"]
                time_period = state["time_period"]
                region = state.get("region", "")
                keywords = state.get("keywords", [topic])

                # Use comprehensive multi-keyword search strategy
                all_results = []
                search_queries = []

                # Create diverse search queries for comprehensive coverage
                for keyword in keywords[:8]:  # Use more keywords for better coverage
                    try:
                        tool = self.tools["web_search"]

                        # Create multiple query variations for each keyword
                        base_query = keyword
                        queries = [
                            base_query,
                            f"{base_query} analysis",
                            f"{base_query} review",
                            f"{base_query} 2024" if "2024" not in base_query else f"{base_query} latest"
                        ]

                        for query in queries[:2]:  # Limit to 2 variations per keyword
                            try:
                                search_query = f"{query} {time_period}" if region == "" else f"{query} {time_period} {region}"
                                search_queries.append(search_query)

                                result = tool.invoke({
                                    "query": search_query,
                                    "num_results": 8  # More results per search
                                })

                                search_data = json.loads(result)
                                results = search_data.get("results", [])
                                all_results.extend(results)

                                logger.info(f"Search '{search_query}' returned {len(results)} results")

                            except Exception as e:
                                logger.error(f"Error with search query '{query}': {e}")
                                continue

                    except Exception as e:
                        logger.error(f"Error searching for keyword '{keyword}': {e}")
                        continue

                # Remove duplicates based on URL
                seen_urls = set()
                unique_results = []
                for result in all_results:
                    url = result.get("url", "")
                    if url and url not in seen_urls:
                        seen_urls.add(url)
                        unique_results.append(result)

                logger.info(f"Collected {len(unique_results)} unique sources from {len(search_queries)} searches")

                # Store comprehensive search results for analysis
                state["trend_data"] = {
                    "topic": topic,
                    "time_period": time_period,
                    "region": region,
                    "search_results": unique_results,
                    "keywords_used": keywords,
                    "search_queries_used": search_queries,
                    "total_sources": len(unique_results),
                    "total_searches": len(search_queries)
                }
                state["next_step"] = "gather_market_data"

                return state

            except Exception as e:
                logger.error(f"Error gathering trend data: {e}")
                state["error_message"] = str(e)
                state["next_step"] = "error"
                return state
        
        def gather_market_data(state: ResearchState) -> ResearchState:
            """Gather comprehensive market data with deep source analysis."""
            try:
                topic = state["topic"]
                keywords = state.get("keywords", [topic])
                research_depth = state.get("research_depth", "detailed")

                # Determine number of sources based on research depth
                source_limits = {
                    "overview": 10,
                    "detailed": 20,
                    "comprehensive": 30
                }
                max_sources = source_limits.get(research_depth, 20)

                # Phase 1: Gather initial sources with targeted searches
                all_sources = []
                search_queries = []

                # Create specific search queries for the topic
                for keyword in keywords[:5]:  # Limit to top 5 keywords
                    search_queries.extend([
                        f"{keyword} impact analysis 2024",
                        f"{keyword} industry implications",
                        f"{keyword} business effects",
                        f"{keyword} market research report"
                    ])

                # Execute searches and collect sources
                for query in search_queries[:8]:  # Limit initial searches
                    try:
                        tool = self.tools["web_search"]
                        result = tool.invoke({
                            "query": query,
                            "num_results": 5
                        })

                        search_data = json.loads(result)
                        for source in search_data.get("results", []):
                            if len(all_sources) < max_sources:
                                # Add source with metadata
                                source_entry = {
                                    "title": source.get("title", ""),
                                    "url": source.get("link", ""),
                                    "snippet": source.get("snippet", ""),
                                    "search_query": query,
                                    "relevance_score": 0,
                                    "content": "",  # Will be filled by deep crawling
                                    "citation_id": f"[{len(all_sources) + 1}]"
                                }
                                all_sources.append(source_entry)

                    except Exception as e:
                        logger.error(f"Error searching for '{query}': {e}")
                        continue

                # Phase 2: Score and rank sources by relevance
                scored_sources = self._score_source_relevance(all_sources, topic, keywords)

                # Phase 3: Deep crawl top sources (implement Firecrawl integration)
                import asyncio
                try:
                    # Run async crawling in sync context
                    enriched_sources = asyncio.run(self._deep_crawl_sources(scored_sources[:max_sources]))
                except Exception as e:
                    logger.error(f"Error in async crawling: {e}")
                    # Fallback to basic sources
                    enriched_sources = scored_sources[:max_sources]
                    for source in enriched_sources:
                        source["content"] = source.get("snippet", "")
                        source["crawl_status"] = "async_failed"

                state["search_results"] = {
                    "sources": enriched_sources,
                    "total_sources": len(enriched_sources),
                    "research_depth": research_depth,
                    "topic": topic
                }
                state["next_step"] = "generate_trend_report"

                return state

            except Exception as e:
                logger.error(f"Error gathering market data: {e}")
                state["error_message"] = str(e)
                state["next_step"] = "error"
                return state
        
        def generate_trend_report(state: ResearchState) -> ResearchState:
            """Generate comprehensive research report with proper source organization and citations."""
            try:
                topic = state["topic"]
                search_results = state.get("search_results", {})
                specific_focus = state.get("specific_focus", "")
                research_depth = state.get("research_depth", "detailed")

                # Extract sources from search results
                sources = search_results.get("sources", [])
                if not sources:
                    # Fallback to basic sources if new structure not available
                    sources = search_results.get("results", [])

                # Prepare content for LLM analysis with citation mapping
                source_content = ""
                citation_map = {}

                for i, source in enumerate(sources, 1):
                    citation_id = f"[{i}]"
                    title = source.get("title", f"Source {i}")
                    content = source.get("content", source.get("snippet", ""))
                    url = source.get("url", source.get("link", ""))

                    # Build citation map for later reference
                    citation_map[citation_id] = {
                        "title": title,
                        "url": url,
                        "citation_id": citation_id
                    }

                    # Add to content for analysis
                    source_content += f"\n{citation_id} {title}\nContent: {content}\nURL: {url}\n\n"

                # Use LLM for intelligent analysis with citation instructions
                llm = ai_provider_service.get_llm(temperature=0.3)

                analysis_prompt = f"""
You are a senior research analyst with expertise in technology, business strategy, and market analysis.
Conduct a comprehensive, in-depth analysis of "{topic}" based on the provided sources.

Research Parameters:
- Focus Areas: {specific_focus}
- Analysis Depth: {research_depth}
- Research Type: {state.get('research_type', 'comprehensive_analysis')}

CRITICAL INSTRUCTIONS:
1. Provide DEEP, DETAILED analysis - not just summaries
2. Extract specific data, statistics, and concrete information
3. Synthesize information across sources to identify patterns and insights
4. Use inline citations [1], [2], [3] for ALL factual claims
5. Provide actionable intelligence and strategic insights

Sources Available ({len(sources)} total):
{source_content[:15000]}

Create a comprehensive research report with this structure:

# In-Depth Research Analysis: {topic}

## Executive Summary
Provide a detailed 4-5 sentence summary covering:
- What {topic} is and its significance [cite sources]
- Key developments and current status [cite sources]
- Primary market implications [cite sources]
- Critical success factors or challenges [cite sources]

## Detailed Findings & Analysis

### Technical/Product Analysis
- Core capabilities and features [with specific details and citations]
- Technical specifications or performance metrics [with data and citations]
- Competitive advantages or unique value propositions [with evidence and citations]
- Development timeline and milestones [with dates and citations]

### Market Position & Competition
- Market size and opportunity [with specific numbers and citations]
- Key competitors and competitive landscape [with detailed comparisons and citations]
- Market share or adoption metrics [with data and citations]
- Pricing strategy and business model [with specifics and citations]

### Business Impact & Strategy
- Revenue potential or financial implications [with projections and citations]
- Strategic partnerships and collaborations [with details and citations]
- Investment and funding information [with amounts and citations]
- Business model innovation or disruption potential [with analysis and citations]

### Industry & Ecosystem Impact
- Broader industry implications [with specific examples and citations]
- Regulatory or compliance considerations [with details and citations]
- Technology trends and innovation patterns [with evidence and citations]
- Ecosystem effects on suppliers, customers, partners [with analysis and citations]

## Strategic Intelligence

### Opportunities
- Specific market opportunities [with sizing and citations]
- Partnership or collaboration possibilities [with rationale and citations]
- Technology or product development opportunities [with evidence and citations]

### Risks & Challenges
- Technical or operational risks [with specific examples and citations]
- Market or competitive threats [with analysis and citations]
- Regulatory or compliance risks [with details and citations]
- Financial or business model risks [with evidence and citations]

### Strategic Recommendations
1. **Immediate Actions** (0-3 months) [with specific steps and supporting evidence]
2. **Short-term Strategy** (3-12 months) [with detailed plans and citations]
3. **Long-term Positioning** (1-3 years) [with strategic vision and supporting data]

## Future Outlook & Predictions
- Technology evolution trajectory [with timeline and supporting evidence]
- Market development predictions [with specific forecasts and citations]
- Competitive landscape changes [with analysis and citations]
- Industry transformation potential [with detailed assessment and citations]

## Research Quality & Confidence Assessment
- Source quality and reliability assessment
- Data completeness and gaps identified
- Confidence level (High/Medium/Low) with detailed reasoning
- Recommendations for additional research if needed

ANALYSIS REQUIREMENTS:
- Extract and cite specific numbers, dates, and concrete data points
- Identify patterns and trends across multiple sources
- Provide strategic context and business implications
- Include competitive intelligence and market positioning
- Synthesize information rather than just summarizing
- Focus on actionable insights and intelligence
- Use professional, analytical tone with specific evidence

CITATION FORMAT:
- Use [1], [2], [3] for inline citations
- Cite ALL factual claims, statistics, quotes, and specific information
- Multiple sources: [1,2,3] or [1-3]
- Every paragraph should have multiple citations
- Professional analysis suitable for executives
"""

                response = llm.invoke(analysis_prompt)

                # Build the complete report with organized sources section
                report_content = response.content

                # Add sources section
                sources_section = "\n\n## Sources\n\n"
                for citation_id, source_info in citation_map.items():
                    sources_section += f"{citation_id} **{source_info['title']}**\n"
                    sources_section += f"   URL: {source_info['url']}\n\n"

                # Combine report with sources
                final_report = report_content + sources_section

                # Calculate confidence based on source quality and quantity
                confidence = min(0.95, 0.5 + (len(sources) * 0.02))  # Base 50% + 2% per source

                # Create structured response
                structured_result = {
                    "report": final_report,
                    "sources": citation_map,
                    "source_count": len(sources),
                    "research_depth": research_depth,
                    "confidence": confidence,
                    "topic": topic
                }

                state["final_report"] = json.dumps(structured_result, indent=2)
                state["confidence_score"] = confidence
                state["next_step"] = "complete"

                return state

            except Exception as e:
                logger.error(f"Error generating research report: {e}")
                # Fallback to basic report
                topic = state.get("topic", "Unknown Topic")
                fallback_report = {
                    "report": f"Error generating detailed analysis for {topic}. Please try again.",
                    "sources": {},
                    "source_count": 0,
                    "confidence": 0.1,
                    "error": str(e)
                }
                state["final_report"] = json.dumps(fallback_report, indent=2)
                state["confidence_score"] = 0.1
                state["error_message"] = str(e)
                state["next_step"] = "error"
                return state
        
        def handle_error(state: ResearchState) -> ResearchState:
            """Handle workflow errors."""
            error_msg = state.get("error_message", "Unknown error occurred")
            state["final_report"] = f"Error in trend analysis: {error_msg}"
            state["confidence_score"] = 0.0
            return state
        
        def complete_workflow(state: ResearchState) -> ResearchState:
            """Complete the workflow."""
            return state
        
        # Build the workflow graph
        workflow = StateGraph(ResearchState)
        
        # Add nodes
        workflow.add_node("analyze_request", analyze_trend_request)
        workflow.add_node("gather_trends", gather_trend_data)
        workflow.add_node("gather_market", gather_market_data)
        workflow.add_node("generate_report", generate_trend_report)
        workflow.add_node("error", handle_error)
        workflow.add_node("complete", complete_workflow)
        
        # Add edges
        workflow.set_entry_point("analyze_request")
        
        workflow.add_conditional_edges(
            "analyze_request",
            lambda state: state["next_step"],
            {
                "gather_trend_data": "gather_trends",
                "error": "error"
            }
        )
        
        workflow.add_conditional_edges(
            "gather_trends",
            lambda state: state["next_step"],
            {
                "gather_market_data": "gather_market",
                "error": "error"
            }
        )
        
        workflow.add_conditional_edges(
            "gather_market",
            lambda state: state["next_step"],
            {
                "generate_trend_report": "generate_report",
                "error": "error"
            }
        )
        
        workflow.add_conditional_edges(
            "generate_report",
            lambda state: state["next_step"],
            {
                "complete": "complete",
                "error": "error"
            }
        )
        
        workflow.add_edge("error", END)
        workflow.add_edge("complete", END)
        
        return workflow.compile()

    def _score_source_relevance(self, sources: List[Dict], topic: str, keywords: List[str]) -> List[Dict]:
        """Score and rank sources by relevance to the research topic."""
        for source in sources:
            score = 0
            title = source.get("title", "").lower()
            snippet = source.get("snippet", "").lower()
            content = f"{title} {snippet}"

            # Score based on topic relevance
            if topic.lower() in content:
                score += 10

            # Score based on keyword matches
            for keyword in keywords:
                if keyword.lower() in content:
                    score += 5

            # Bonus for authoritative sources
            url = source.get("url", "").lower()
            authoritative_domains = [
                "mckinsey.com", "harvard.edu", "mit.edu", "stanford.edu",
                "reuters.com", "bloomberg.com", "wsj.com", "ft.com",
                "techcrunch.com", "venturebeat.com", "wired.com",
                "nature.com", "science.org", "arxiv.org"
            ]

            for domain in authoritative_domains:
                if domain in url:
                    score += 15
                    break

            # Bonus for recent content indicators
            recent_indicators = ["2024", "2025", "latest", "recent", "new", "just released"]
            for indicator in recent_indicators:
                if indicator in content:
                    score += 3

            source["relevance_score"] = score

        # Sort by relevance score (highest first)
        return sorted(sources, key=lambda x: x.get("relevance_score", 0), reverse=True)

    async def _deep_crawl_sources(self, sources: List[Dict]) -> List[Dict]:
        """Deep crawl sources to extract full content using Firecrawl."""
        from app.services.firecrawl_service import firecrawl_service

        enriched_sources = []
        urls_to_crawl = []

        # Prepare URLs for batch crawling
        for source in sources:
            url = source.get("url", "")
            if url:
                urls_to_crawl.append(url)

        # Batch crawl all URLs
        if urls_to_crawl:
            try:
                crawl_results = await firecrawl_service.batch_crawl(urls_to_crawl, max_concurrent=3)

                # Map results back to sources
                url_to_result = {result["url"]: result for result in crawl_results}

                for source in sources:
                    url = source.get("url", "")
                    if url in url_to_result:
                        crawl_result = url_to_result[url]

                        if crawl_result["success"]:
                            source["content"] = crawl_result["content"]
                            source["full_title"] = crawl_result["title"]
                            source["description"] = crawl_result["description"]
                            source["content_length"] = len(crawl_result["content"])
                            source["crawl_status"] = "success"
                            source["extraction_method"] = crawl_result["extraction_method"]
                        else:
                            source["content"] = source.get("snippet", "")
                            source["crawl_status"] = "failed"
                            source["extraction_method"] = "failed"
                    else:
                        source["content"] = source.get("snippet", "")
                        source["crawl_status"] = "no_url"

                    enriched_sources.append(source)

            except Exception as e:
                logger.error(f"Error in batch crawling: {e}")
                # Fallback to using snippets
                for source in sources:
                    source["content"] = source.get("snippet", "")
                    source["crawl_status"] = "batch_failed"
                    enriched_sources.append(source)
        else:
            # No URLs to crawl
            for source in sources:
                source["content"] = source.get("snippet", "")
                source["crawl_status"] = "no_url"
                enriched_sources.append(source)

        return enriched_sources

    def _simulate_deep_crawl(self, source: Dict) -> str:
        """Simulate deep crawling - replace with actual Firecrawl implementation."""
        # This is a placeholder - in real implementation, use Firecrawl API
        title = source.get("title", "")
        snippet = source.get("snippet", "")

        # Simulate expanded content based on the snippet
        simulated_content = f"""
        {title}

        {snippet}

        [Simulated deep content - In production, this would be the full article content
        extracted via Firecrawl, including main text, key points, data, and analysis
        from the source URL: {source.get('url', '')}]

        This content would typically include:
        - Full article text
        - Key statistics and data points
        - Expert quotes and analysis
        - Related information and context
        - Structured data extraction
        """

        return simulated_content.strip()


# Global workflow instances
research_workflows = ResearchWorkflows()
competitor_analysis_workflow = research_workflows.create_competitor_analysis_workflow()
trend_analysis_workflow = research_workflows.create_trend_analysis_workflow()
