from contextlib import asynccontextmanager
import uvicorn
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi
from fastapi.responses import RedirectResponse
from app.utils.logger import get_logger

from app.database.database import create_tables
from app.routes import agents, chat, research, chatbots, chatbot_chat
from app.core.config import settings

logger = get_logger(__name__)
from app.utils.logger import get_logger
from app.agents.research_agents import RESEARCH_AGENTS
from app.agents.calendar_agents import ContentCalendarAgent
from app.agents.orchestrator_agents import ORCHESTRATOR_AGENTS
from app.chatbots.sample_chatbots import SAMPLE_CHATBOTS
from app.models.models import CustomAgent, CustomChatbot
from app.database.database import AsyncSessionLocal

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting Deep Ellum Agent Service...")
    
    try:
        # Create database tables
        await create_tables()
        logger.info("Database tables created successfully")

        # Initialize sample agents and chatbots in database
        await initialize_sample_agents()
        await initialize_sample_chatbots()
        logger.info("Sample agents and chatbots initialized")

        # Initialize MCP server if enabled
        if settings.MCP_ENABLED and settings.MCP_AUTO_START:
            try:
                from app.mcp_server.integration import initialize_mcp_integration, start_mcp_integration
                await initialize_mcp_integration(settings.MCP_HOST, settings.MCP_PORT)
                await start_mcp_integration()
                logger.info(f"MCP server started on {settings.MCP_HOST}:{settings.MCP_PORT}")
            except Exception as mcp_error:
                logger.error(f"Error starting MCP server: {mcp_error}")
                logger.warning("Service will continue without MCP functionality")

    except Exception as e:
        logger.error(f"Error during startup: {e}")
        logger.warning("Service will start without database functionality")

    yield

    # Shutdown
    logger.info("Shutting down Deep Ellum Agent Service...")

    # Stop MCP server if running
    if settings.MCP_ENABLED:
        try:
            from app.mcp_server.integration import stop_mcp_integration
            await stop_mcp_integration()
            logger.info("MCP server stopped")
        except Exception as e:
            logger.error(f"Error stopping MCP server: {e}")


async def initialize_sample_agents():
    """Initialize sample agents (true autonomous agents) in the database."""
    try:
        async with AsyncSessionLocal() as db:
            from sqlalchemy import select, and_

            # Get existing sample agents
            result = await db.execute(
                select(CustomAgent).where(CustomAgent.is_sample == True)
            )
            existing_samples = result.scalars().all()
            existing_names = {agent.name for agent in existing_samples}

            agents_created = 0

            # Create research agents
            for agent_type, agent_class in RESEARCH_AGENTS.items():
                try:
                    agent_instance = agent_class()

                    # Check if this specific agent already exists
                    if agent_instance.name in existing_names:
                        logger.info(f"Sample agent '{agent_instance.name}' already exists, skipping")
                        continue

                    sample_agent = CustomAgent(
                        name=agent_instance.name,
                        description=agent_instance.description,
                        personality=agent_instance.personality,
                        instructions=agent_instance.instructions,
                        capabilities=[cap.value for cap in agent_instance.capabilities],
                        is_active=True,
                        is_sample=True,
                        created_by="system",
                        organization_id=None  # Sample agents are global, not organization-specific
                    )

                    db.add(sample_agent)
                    agents_created += 1
                    logger.info(f"Created sample agent: {agent_instance.name}")

                except Exception as e:
                    logger.error(f"Error creating sample agent {agent_type}: {e}")

            # Create calendar agent
            try:
                calendar_agent = ContentCalendarAgent()
                if calendar_agent.name not in existing_names:
                    sample_agent = CustomAgent(
                        name=calendar_agent.name,
                        description=calendar_agent.description,
                        personality=calendar_agent.personality,
                        instructions=calendar_agent.instructions,
                        capabilities=[cap.value for cap in calendar_agent.capabilities],
                        is_active=True,
                        is_sample=True,
                        created_by="system",
                        organization_id=None
                    )
                    db.add(sample_agent)
                    agents_created += 1
                    logger.info(f"Created sample agent: {calendar_agent.name}")
                else:
                    logger.info(f"Sample agent '{calendar_agent.name}' already exists, skipping")
            except Exception as e:
                logger.error(f"Error creating calendar agent: {e}")

            # Create orchestrator agents
            for agent_type, agent_class in ORCHESTRATOR_AGENTS.items():
                try:
                    agent_instance = agent_class()
                    if agent_instance.name not in existing_names:
                        sample_agent = CustomAgent(
                            name=agent_instance.name,
                            description=agent_instance.description,
                            personality=agent_instance.personality,
                            instructions=agent_instance.instructions,
                            capabilities=[cap.value for cap in agent_instance.capabilities],
                            is_active=True,
                            is_sample=True,
                            created_by="system",
                            organization_id=None
                        )
                        db.add(sample_agent)
                        agents_created += 1
                        logger.info(f"Created sample agent: {agent_instance.name}")
                    else:
                        logger.info(f"Sample agent '{agent_instance.name}' already exists, skipping")
                except Exception as e:
                    logger.error(f"Error creating orchestrator agent {agent_type}: {e}")

            if agents_created > 0:
                await db.commit()
                logger.info(f"Created {agents_created} new sample agents")
            else:
                logger.info("All sample agents already exist")

    except Exception as e:
        logger.error(f"Error initializing sample agents: {e}")
        # Don't raise the error to allow the service to start without database


async def initialize_sample_chatbots():
    """Initialize sample chatbots (conversational assistants) in the database."""
    try:
        async with AsyncSessionLocal() as db:
            from sqlalchemy import select, and_

            # Get existing sample chatbots
            result = await db.execute(
                select(CustomChatbot).where(CustomChatbot.is_sample == True)
            )
            existing_samples = result.scalars().all()
            existing_names = {chatbot.name for chatbot in existing_samples}

            chatbots_created = 0

            # Create sample chatbots that don't exist yet
            for chatbot_type, chatbot_class in SAMPLE_CHATBOTS.items():
                try:
                    chatbot_instance = chatbot_class()

                    # Check if this specific chatbot already exists
                    if chatbot_instance.name in existing_names:
                        logger.info(f"Sample chatbot '{chatbot_instance.name}' already exists, skipping")
                        continue

                    sample_chatbot = CustomChatbot(
                        name=chatbot_instance.name,
                        description=chatbot_instance.description,
                        personality=chatbot_instance.personality,
                        instructions=chatbot_instance.instructions,
                        capabilities=[cap.value for cap in chatbot_instance.capabilities],
                        is_active=True,
                        is_sample=True,
                        created_by="system",
                        organization_id=None 
                    )

                    db.add(sample_chatbot)
                    chatbots_created += 1
                    logger.info(f"Created sample chatbot: {chatbot_instance.name}")

                except Exception as e:
                    logger.error(f"Error creating sample chatbot {chatbot_type}: {e}")

            if chatbots_created > 0:
                await db.commit()
                logger.info(f"Created {chatbots_created} new sample chatbots")
            else:
                logger.info("All sample chatbots already exist")

    except Exception as e:
        logger.error(f"Error initializing sample chatbots: {e}")
        # Don't raise the error to allow the service to start without database


# Create FastAPI app
app = FastAPI(
    title="Deep Ellum AI Agent & Chatbot Service",
    description="A service for creating, managing, and chatting with custom AI agents (autonomous) and chatbots (conversational assistants) using LangChain and LangGraph",
    version="1.0.0",
    openapi_url="/api/v1/openapi.json",
    docs_url="/api/v1/docs",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(agents.router, prefix="/api/v1/agents", tags=["agents"])
app.include_router(chat.router, prefix="/api/v1/chat", tags=["chat"])
app.include_router(chatbots.router, prefix="/api/v1/chatbots", tags=["chatbots"])
app.include_router(chatbot_chat.router, prefix="/api/v1/chatbots", tags=["chatbot-chat"])
app.include_router(research.router, prefix="/api/v1", tags=["research"])

# MCP router
from app.routes import mcp
app.include_router(mcp.router, prefix="/api/v1/mcp", tags=["mcp"])

# Settings router
from app.routes import settings as settings_routes
app.include_router(settings_routes.router, prefix="/api/v1/settings", tags=["settings"])

# AI Provider router
from app.routes import ai_provider
app.include_router(ai_provider.router, prefix="/api/v1", tags=["ai-provider"])

# Deep Research router
try:
    from app.routes import deep_research
    app.include_router(deep_research.router, prefix="/api/v1", tags=["deep-research"])
except ImportError as e:
    logger.warning(f"Deep Research routes not available: {e}")


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    from app.core.config import settings as config_settings
    return {
        "status": "healthy",
        "service": config_settings.SERVICE_NAME,
        "version": "1.0.0"
    }


@app.get("/")
async def root():
    """Root endpoint - public access."""
    return {"message": "Deep Ellum AI Agent & Chatbot Service", "status": "running"}





@app.get("/api/v1/agents/docs")
async def redirect_to_docs():
    """Redirect old docs path to new docs path."""
    return RedirectResponse(url="/api/v1/docs")


@app.get("/agent_status")
async def agent_status():
    """Service status endpoint - public access."""
    from app.core.config import settings as config_settings
    return {
        "message": "Welcome to the Deep Ellum AI Agent & Chatbot Service",
        "service": config_settings.SERVICE_NAME,
        "features": [
            "True AI Agents (Autonomous):",
            "- Deep Research Agent: Competitive intelligence and market research",
            "- Content Calendar Agent: Content planning and scheduling",
            "- Orchestrator Agent: Multi-agent/chatbot workflow coordination and autonomous project management",
            "",
            "AI Chatbots (Conversational Assistants):",
            "- Code Generator Chatbot: Code creation and review",
            "- Accessibility Advisor Chatbot: WCAG compliance and inclusive design",
            "- Documentation Specialist Chatbot: Technical documentation",
            "",
            "Core Technologies:",
            "- LangChain and LangGraph integration",
            "- Google Gemini AI",
            "- OpenAI Deep Research integration",
            "- Conversation management",
            "- Intelligent research routing with LLM",
            "- Multi-step research with web search and code interpretation",
            "- Research-analyst-level reports with citations",
            "- Autonomous workflow orchestration and coordination"
        ]
    }


def custom_openapi():
    """Custom OpenAPI schema to include bearer token authentication."""
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title="Deep Ellum AI Agent & Chatbot Service",
        version="1.0.0",
        description="A comprehensive service for creating and managing custom AI agents (autonomous) and chatbots (conversational assistants)",
        routes=app.routes,
    )

    # Add Bearer token authentication scheme
    openapi_schema["components"]["securitySchemes"] = {
        "Bearer": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}
    }

    # Apply Bearer authentication to all endpoints except public ones
    public_paths = [
        "/docs", "/redoc", "/openapi.json", "/agent_status", "/", "/health",
        "/api/v1/docs", "/api/v1/redoc", "/api/v1/openapi.json", "/api/v1/agents/docs"
    ]

    for path_name, path in openapi_schema["paths"].items():
        # Skip authentication for public paths
        if any(public_path in path_name for public_path in public_paths):
            continue

        for method in path.values():
            if isinstance(method, dict):  # Ensure it's a method object
                if "security" in method:
                    method["security"].append({"Bearer": []})
                else:
                    method["security"] = [{"Bearer": []}]

    # Add custom schema information
    openapi_schema["info"]["x-logo"] = {
        "url": "https://example.com/logo.png"
    }

    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi


if __name__ == "__main__":
    from app.core.config import settings as config_settings
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=config_settings.SERVICE_PORT,
        reload=config_settings.DEBUG,
        log_level="info"
    )
