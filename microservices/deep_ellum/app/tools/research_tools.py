"""
Research tools for the Deep Research Agent using LangChain built-in tools.
These tools provide web search, competitor monitoring, trend analysis, and data aggregation capabilities.
"""

import json
import re
from datetime import datetime
from typing import Dict, List, Optional, Any

from langchain_community.tools import DuckDuckGoSearchRun, DuckDuckGoSearchResults
try:
    from langchain_google_community import GoogleSearchAPIWrapper
except ImportError:
    from langchain_community.utilities import GoogleSearchAPIWrapper
from langchain_core.tools import tool

from app.utils.logger import get_logger
from app.core.config import settings

logger = get_logger(__name__)


# Initialize search tools
def get_search_tool():
    """Get the appropriate search tool based on available configuration."""
    # Try Google Search API first if configured
    google_api_key = getattr(settings, 'GOOGLE_SEARCH_API_KEY', None)
    google_cse_id = getattr(settings, 'GOOGLE_SEARCH_ENGINE_ID', None)
    
    if google_api_key and google_cse_id:
        search = GoogleSearchAPIWrapper(
            google_api_key=google_api_key,
            google_cse_id=google_cse_id
        )
        return search
    
    # Use DuckDuckGo as primary search tool
    return DuckDuckGoSearchResults(max_results=10)

# Initialize the search tool
search_tool = get_search_tool()


@tool
def web_search(query: str, num_results: int = 10) -> str:
    """
    Search the web for information using available search engines.
    
    Args:
        query: Search query to execute
        num_results: Number of results to return (default: 10)
    
    Returns:
        JSON string containing search results with titles, links, and snippets
    """
    try:
        if isinstance(search_tool, GoogleSearchAPIWrapper):
            try:
                # Use Google Search API
                results = search_tool.results(query, num_results)
            except Exception as google_error:
                logger.warning(f"Google Search API failed: {google_error}, falling back to DuckDuckGo")
                # Fallback to DuckDuckGo
                fallback_search = DuckDuckGoSearchResults(max_results=num_results)
                results = fallback_search.run(query)
        else:
            # Use DuckDuckGo
            results = search_tool.run(query)

        # Format results consistently
        formatted_results = {
            "query": query,
            "results": results if isinstance(results, list) else [{"snippet": results}],
            "timestamp": datetime.now().isoformat()
        }

        return json.dumps(formatted_results, indent=2)
        
    except Exception as e:
        logger.error(f"Error in web search: {e}")
        raise


@tool
def competitor_monitoring(competitor_name: str, keywords: str = "", time_range: str = "1w") -> str:
    """
    Monitor competitor activities, news, and developments.
    
    Args:
        competitor_name: Name of the competitor to monitor
        keywords: Comma-separated keywords related to the competitor
        time_range: Time range for monitoring (1d, 1w, 1m)
    
    Returns:
        JSON string containing competitor monitoring results
    """
    try:
        keyword_list = [k.strip() for k in keywords.split(",") if k.strip()] if keywords else []
        
        # Build comprehensive search queries for competitor monitoring
        search_queries = [
            f'"{competitor_name}" news',
            f'"{competitor_name}" announcement',
            f'"{competitor_name}" product launch',
            f'"{competitor_name}" funding',
            f'"{competitor_name}" partnership'
        ]
        
        # Add keyword-specific searches
        for keyword in keyword_list:
            search_queries.append(f'"{competitor_name}" {keyword}')
        
        all_results = []
        
        # Execute searches
        for query in search_queries[:3]:  # Limit to avoid rate limits
            try:
                search_result = web_search.invoke({"query": query, "num_results": 5})
                search_data = json.loads(search_result)
                all_results.extend(search_data.get("results", []))
            except Exception as e:
                logger.error(f"Error in competitor search for query '{query}': {e}")
                continue
        
        # Deduplicate and score results
        unique_results = _deduplicate_results(all_results)
        scored_results = _score_competitor_results(unique_results, competitor_name, keyword_list)
        
        monitoring_results = {
            "competitor": competitor_name,
            "keywords": keyword_list,
            "time_range": time_range,
            "monitoring_results": scored_results[:10],  # Top 10 results
            "total_found": len(scored_results),
            "timestamp": datetime.now().isoformat(),
            "summary": _generate_competitor_summary(scored_results, competitor_name)
        }
        
        return json.dumps(monitoring_results, indent=2)
        
    except Exception as e:
        logger.error(f"Error in competitor monitoring: {e}")
        raise


@tool
def trend_analysis(topic: str, time_period: str = "3m", region: str = "") -> str:
    """
    Analyze trends and patterns in specific topics or industries.
    
    Args:
        topic: Topic or industry to analyze trends for
        time_period: Time period for trend analysis (default: 3m)
        region: Geographic region for analysis (optional)
    
    Returns:
        JSON string containing trend analysis results
    """
    try:
        # Build trend-focused search queries
        trend_queries = [
            f"{topic} trends {time_period}",
            f"{topic} market analysis",
            f"{topic} industry report",
            f"{topic} growth forecast",
            f"future of {topic}",
            f"{topic} statistics {datetime.now().year}"
        ]
        
        if region:
            trend_queries = [f"{query} {region}" for query in trend_queries]
        
        all_results = []
        
        for query in trend_queries[:4]:  # Limit searches
            try:
                search_result = web_search.invoke({"query": query, "num_results": 5})
                search_data = json.loads(search_result)
                all_results.extend(search_data.get("results", []))
            except Exception as e:
                logger.error(f"Error in trend search for query '{query}': {e}")
                continue
        
        # Analyze trends from results
        trend_insights = _analyze_trend_patterns(all_results, topic)
        
        analysis_results = {
            "topic": topic,
            "time_period": time_period,
            "region": region,
            "trend_analysis": trend_insights,
            "data_sources": len(all_results),
            "timestamp": datetime.now().isoformat()
        }
        
        return json.dumps(analysis_results, indent=2)
        
    except Exception as e:
        logger.error(f"Error in trend analysis: {e}")
        raise


@tool
def data_aggregation(sources: str, focus_areas: str = "") -> str:
    """
    Aggregate and synthesize data from multiple sources.
    
    Args:
        sources: Comma-separated list of search terms or topics to aggregate
        focus_areas: Comma-separated list of specific areas to focus on during aggregation
    
    Returns:
        JSON string containing aggregated data and insights
    """
    try:
        source_list = [s.strip() for s in sources.split(",") if s.strip()]
        focus_list = [f.strip() for f in focus_areas.split(",") if f.strip()] if focus_areas else []
        
        aggregated_data = {
            "sources_processed": 0,
            "focus_areas": focus_list,
            "aggregated_insights": {},
            "source_summaries": [],
            "timestamp": datetime.now().isoformat()
        }
        
        for source in source_list[:5]:  # Limit to 5 sources
            try:
                # Treat each source as a search query
                search_result = web_search.invoke({"query": source, "num_results": 3})
                search_data = json.loads(search_result)
                
                aggregated_data["source_summaries"].append({
                    "source": source,
                    "type": "search",
                    "results": search_data.get("results", [])
                })
                
                aggregated_data["sources_processed"] += 1
                
            except Exception as e:
                logger.error(f"Error processing source '{source}': {e}")
                continue
        
        # Generate insights across all sources
        aggregated_data["aggregated_insights"] = _generate_cross_source_insights(
            aggregated_data["source_summaries"], focus_list
        )
        
        return json.dumps(aggregated_data, indent=2)
        
    except Exception as e:
        logger.error(f"Error in data aggregation: {e}")
        raise


# Helper functions
def _deduplicate_results(results: List[Dict]) -> List[Dict]:
    """Remove duplicate results based on URL or title."""
    seen = set()
    unique_results = []
    
    for result in results:
        # Create a unique identifier from URL or title
        identifier = result.get("link", result.get("title", ""))
        if identifier and identifier not in seen:
            seen.add(identifier)
            unique_results.append(result)
    
    return unique_results


def _score_competitor_results(results: List[Dict], competitor_name: str, keywords: List[str]) -> List[Dict]:
    """Score results based on relevance to competitor and keywords."""
    scored_results = []
    
    for result in results:
        score = 0
        title = result.get("title", "").lower()
        snippet = result.get("snippet", "").lower()
        content = f"{title} {snippet}"
        
        # Score based on competitor name mentions
        if competitor_name.lower() in content:
            score += 10
        
        # Score based on keyword matches
        for keyword in keywords:
            if keyword.lower() in content:
                score += 5
        
        # Bonus for recent news indicators
        news_indicators = ["announces", "launches", "releases", "partnership", "funding", "acquisition"]
        for indicator in news_indicators:
            if indicator in content:
                score += 3
        
        result["relevance_score"] = score
        scored_results.append(result)
    
    # Sort by score descending
    return sorted(scored_results, key=lambda x: x.get("relevance_score", 0), reverse=True)


def _generate_competitor_summary(results: List[Dict], competitor_name: str) -> str:
    """Generate a summary of competitor monitoring results."""
    if not results:
        return f"No recent activity found for {competitor_name}"
    
    top_results = results[:3]
    summary_points = []
    
    for result in top_results:
        title = result.get("title", "")
        snippet = result.get("snippet", "")
        if title and snippet:
            summary_points.append(f"• {title}: {snippet[:100]}...")
    
    return f"Recent activity for {competitor_name}:\n" + "\n".join(summary_points)


def _analyze_trend_patterns(results: List[Dict], topic: str) -> Dict[str, Any]:
    """Analyze patterns and extract trend insights using LLM intelligence."""
    try:
        # Prepare content for LLM analysis
        all_content = ""
        for result in results:
            title = result.get('title', '')
            snippet = result.get('snippet', '')
            if title and snippet:
                all_content += f"Title: {title}\nContent: {snippet}\n\n"

        if not all_content.strip():
            return {
                "key_trends": [],
                "growth_indicators": [],
                "market_signals": [],
                "emerging_themes": []
            }

        # Use LLM for intelligent analysis
        from app.services.ai_provider_service import ai_provider_service

        llm = ai_provider_service.get_llm(temperature=0.1)

        analysis_prompt = f"""
Analyze the following search results about "{topic}" and extract trend insights.

Search Results:
{all_content[:6000]}

Extract and return ONLY a JSON object with these insights:
{{
    "key_trends": ["list of 3-5 specific trends mentioned in the content"],
    "growth_indicators": ["list of signals indicating growth, expansion, or positive momentum"],
    "market_signals": ["list of important market developments or changes"],
    "emerging_themes": ["list of new innovations, technologies, or approaches mentioned"]
}}

Focus on:
- Specific, actionable trends (not generic statements)
- Evidence-based insights from the content
- Recent developments and innovations
- Market dynamics and business implications

Return only the JSON object, no additional text.
"""

        response = llm.invoke(analysis_prompt)

        # Parse LLM response
        import json
        json_match = re.search(r'\{.*\}', response.content, re.DOTALL)
        if json_match:
            insights = json.loads(json_match.group())
            return insights
        else:
            # Fallback to basic analysis
            return _basic_trend_analysis(results, topic)

    except Exception as e:
        logger.error(f"Error in LLM trend analysis: {e}")
        # Fallback to basic analysis
        return _basic_trend_analysis(results, topic)


def _basic_trend_analysis(results: List[Dict], topic: str) -> Dict[str, Any]:
    """Fallback basic trend analysis using keyword matching."""
    insights = {
        "key_trends": [],
        "growth_indicators": [],
        "market_signals": [],
        "emerging_themes": []
    }

    # Keywords that indicate trends
    growth_keywords = ["growth", "increase", "rising", "expanding", "surge", "boom"]
    decline_keywords = ["decline", "decrease", "falling", "shrinking", "drop"]
    emerging_keywords = ["emerging", "new", "innovative", "breakthrough", "disruption"]

    all_content = ""
    for result in results:
        content = f"{result.get('title', '')} {result.get('snippet', '')}".lower()
        all_content += content + " "

    # Analyze growth indicators
    for keyword in growth_keywords:
        if keyword in all_content:
            insights["growth_indicators"].append(f"Positive signal: '{keyword}' mentioned in relation to {topic}")

    for keyword in decline_keywords:
        if keyword in all_content:
            insights["growth_indicators"].append(f"Caution signal: '{keyword}' mentioned in relation to {topic}")

    # Identify emerging themes
    for keyword in emerging_keywords:
        if keyword in all_content:
            insights["emerging_themes"].append(f"Innovation signal: '{keyword}' associated with {topic}")

    # Extract key trends (simplified pattern matching)
    trend_patterns = re.findall(r'\b\w+(?:\s+\w+){0,2}\s+trend\b', all_content)
    insights["key_trends"] = list(set(trend_patterns[:5]))  # Top 5 unique trends

    return insights


def _generate_cross_source_insights(source_summaries: List[Dict], focus_areas: List[str]) -> Dict[str, Any]:
    """Generate insights by analyzing patterns across multiple sources."""
    insights = {
        "common_themes": [],
        "conflicting_information": [],
        "data_gaps": [],
        "confidence_level": "medium"
    }
    
    # Simple pattern analysis across sources
    all_content = ""
    for summary in source_summaries:
        for result in summary.get("results", []):
            all_content += result.get("snippet", "") + " "
    
    # Identify common themes
    for focus_area in focus_areas:
        if focus_area.lower() in all_content.lower():
            insights["common_themes"].append(f"Multiple sources mention {focus_area}")
    
    # Set confidence based on number of sources
    if len(source_summaries) >= 3:
        insights["confidence_level"] = "high"
    elif len(source_summaries) >= 2:
        insights["confidence_level"] = "medium"
    else:
        insights["confidence_level"] = "low"
    
    return insights


# Tool registry for easy access
RESEARCH_TOOLS = [
    web_search,
    competitor_monitoring,
    trend_analysis,
    data_aggregation
]
