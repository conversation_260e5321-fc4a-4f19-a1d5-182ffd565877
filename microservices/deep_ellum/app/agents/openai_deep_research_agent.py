"""
OpenAI Deep Research Agent using OpenAI's o3-deep-research and o4-mini-deep-research models.
"""

from typing import Dict, Any, Optional
from app.agents.base_agent import BaseAgent
from app.models.schemas import AgentCapability, DeepResearchModel
from app.services.openai_deep_research_service import openai_deep_research_service
from app.utils.logger import get_logger
import json
import re

logger = get_logger(__name__)


class OpenAIDeepResearchAgent(BaseAgent):
    """
    Advanced research agent using OpenAI's deep research models.
    
    This agent leverages OpenAI's o3-deep-research and o4-mini-deep-research models
    to conduct comprehensive, multi-step research with web search and code analysis.
    """
    
    def __init__(self):
        super().__init__(
            name="OpenAI Deep Research Agent",
            description="""An advanced AI research specialist powered by OpenAI's deep research models.
            I conduct comprehensive research using multi-step analysis, web search, and code interpretation
            to provide detailed, well-cited research reports on any topic. I excel at finding, analyzing,
            and synthesizing information from hundreds of sources to create research-analyst-level reports.""",
            instructions="""
            You are an OpenAI Deep Research Agent, an expert researcher powered by OpenAI's most advanced
            research models (o3-deep-research and o4-mini-deep-research). Your capabilities include:

            ## Core Research Capabilities:

            **Comprehensive Research:**
            - Conduct multi-step research using web search and data analysis
            - Find, analyze, and synthesize information from hundreds of sources
            - Generate research-analyst-level reports with proper citations
            - Provide detailed analysis with supporting evidence

            **Advanced Analysis:**
            - Use code interpretation for complex data analysis
            - Cross-reference information from multiple sources
            - Identify patterns, trends, and insights
            - Validate findings and assess source reliability

            **Research Specializations:**
            - Legal and scientific research
            - Market analysis and competitive intelligence
            - Technical and academic research
            - Policy analysis and regulatory research
            - Industry trends and emerging technologies

            ## Research Process:

            1. **Query Analysis**: Understand the research scope and objectives
            2. **Information Gathering**: Use web search to find relevant sources
            3. **Data Analysis**: Apply code interpretation for complex analysis
            4. **Source Validation**: Cross-reference and verify information
            5. **Synthesis**: Create coherent narrative from multiple sources
            6. **Citation**: Provide inline citations and source metadata
            7. **Insights**: Generate actionable insights and recommendations

            ## Communication Style:

            - Provide structured, well-organized research findings
            - Use data-driven insights with supporting evidence
            - Include proper citations and source references
            - Highlight key findings and implications
            - Offer specific, actionable recommendations
            - Maintain objectivity and analytical rigor

            ## Quality Standards:

            - Prioritize reliable, up-to-date sources
            - Include specific figures, trends, and statistics
            - Provide measurable outcomes and data-backed reasoning
            - Ensure comprehensive coverage of the research topic
            - Maintain high standards of accuracy and reliability

            Always provide:
            - Executive summary of key findings
            - Detailed analysis with supporting data
            - Inline citations and source metadata
            - Strategic implications and recommendations
            - Confidence levels for findings
            - Suggestions for follow-up research

            Remember: Your goal is to provide comprehensive, accurate, and actionable
            research that meets the highest standards of analytical rigor.
            """,
            personality="""Professional, analytical, and thorough. You approach research with scientific
            rigor while maintaining focus on practical applications. You're meticulous about accuracy,
            comprehensive in your analysis, and clear in your communication. You provide evidence-based
            insights and maintain objectivity throughout your research process.""",
            capabilities=[
                AgentCapability.DEEP_RESEARCH,
                AgentCapability.WEB_SEARCH,
                AgentCapability.DATA_AGGREGATION,
                AgentCapability.REPORT_GENERATION,
                AgentCapability.MARKET_RESEARCH,
                AgentCapability.TREND_ANALYSIS,
                AgentCapability.RESEARCH
            ]
        )
        
        self.research_service = openai_deep_research_service
    
    async def process_message(self, message: str, context: Dict[str, Any] = None) -> str:
        """Process research requests using OpenAI's deep research models."""
        try:
            # Extract research parameters from message and context
            research_params = self._extract_research_parameters(message, context)
            
            # Determine the appropriate model based on complexity
            model = self._select_research_model(message, research_params)
            
            # Enhance the research query with context
            enhanced_query = self._enhance_research_query(message, context)
            
            # Conduct the deep research
            logger.info(f"Starting deep research with model: {model}")
            research_result = await self.research_service.conduct_deep_research(
                research_query=enhanced_query,
                model=model,
                background=research_params.get("background", True),
                max_tool_calls=research_params.get("max_tool_calls"),
                include_code_interpreter=research_params.get("include_code_interpreter", True),
                include_web_search=research_params.get("include_web_search", True),
                instructions=research_params.get("instructions")
            )
            
            # Format and return the research results
            return self._format_research_response(research_result, message)
            
        except Exception as e:
            logger.error(f"Error in OpenAI Deep Research Agent: {e}")
            return self._handle_research_error(e, message)
    
    def _extract_research_parameters(self, message: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Extract research parameters from the message and context."""
        params = {
            "background": True,
            "include_code_interpreter": True,
            "include_web_search": True
        }
        
        # Extract parameters from message
        message_lower = message.lower()
        
        # Check for model preference
        if "mini" in message_lower or "quick" in message_lower or "fast" in message_lower:
            params["prefer_mini"] = True
        
        # Check for synchronous execution request
        if any(keyword in message_lower for keyword in ["immediate", "sync", "wait", "now"]):
            params["background"] = False
        
        # Check for tool preferences
        if "no code" in message_lower or "without code" in message_lower:
            params["include_code_interpreter"] = False
        
        if "no web" in message_lower or "without web" in message_lower:
            params["include_web_search"] = False
        
        # Extract max tool calls if specified
        tool_call_match = re.search(r"max(?:imum)?\s+(\d+)\s+(?:tool\s+)?calls?", message_lower)
        if tool_call_match:
            params["max_tool_calls"] = int(tool_call_match.group(1))
        
        # Add context-based parameters
        if context:
            params.update(context.get("research_params", {}))
        
        return params
    
    def _select_research_model(self, message: str, params: Dict[str, Any]) -> str:
        """Select the appropriate research model based on complexity and preferences."""
        # Check for explicit model preference
        if params.get("prefer_mini"):
            return DeepResearchModel.O4_MINI_DEEP_RESEARCH.value
        
        # Analyze message complexity
        message_length = len(message)
        complexity_indicators = [
            "comprehensive", "detailed", "thorough", "in-depth", "extensive",
            "analysis", "compare", "contrast", "evaluate", "assess",
            "legal", "scientific", "technical", "academic", "research"
        ]
        
        complexity_score = sum(1 for indicator in complexity_indicators if indicator in message.lower())
        
        # Use mini model for simpler requests
        if message_length < 100 and complexity_score < 2:
            return DeepResearchModel.O4_MINI_DEEP_RESEARCH.value
        
        # Use full model for complex research
        return DeepResearchModel.O3_DEEP_RESEARCH.value
    
    def _enhance_research_query(self, message: str, context: Dict[str, Any] = None) -> str:
        """Enhance the research query with additional context and instructions."""
        enhanced_query = message
        
        # Add context from conversation history
        if context and context.get("conversation_history"):
            recent_context = self._extract_conversation_context(context["conversation_history"])
            if recent_context:
                enhanced_query = f"Context from previous conversation: {recent_context}\n\nCurrent research request: {message}"
        
        # Add organization-specific context if available
        if context and context.get("organization_context"):
            org_context = context["organization_context"]
            enhanced_query += f"\n\nOrganization context: {org_context}"
        
        return enhanced_query
    
    def _extract_conversation_context(self, conversation_history: list) -> str:
        """Extract relevant context from conversation history."""
        if not conversation_history:
            return ""
        
        # Get last few messages for context
        recent_messages = conversation_history[-3:]
        context_parts = []
        
        for msg in recent_messages:
            if msg.get("role") == "user" and len(msg.get("content", "")) > 20:
                context_parts.append(msg["content"][:200])
        
        return " | ".join(context_parts) if context_parts else ""
    
    def _format_research_response(self, research_result: Dict[str, Any], original_query: str) -> str:
        """Format the research results into a comprehensive response."""
        if not research_result.get("success"):
            return f"I apologize, but I encountered an error during research: {research_result.get('error', 'Unknown error')}"
        
        # Build the formatted response
        response_parts = []
        
        # Add executive summary header
        response_parts.append("# Deep Research Results")
        response_parts.append(f"**Research Query:** {original_query}")
        response_parts.append("")
        
        # Add main research content
        output_text = research_result.get("output_text", "")
        if output_text:
            response_parts.append(output_text)
        else:
            response_parts.append("No research content was generated.")
        
        # Add citations section
        citations = research_result.get("citations", [])
        if citations:
            response_parts.append("\n## Sources and Citations")
            for i, citation in enumerate(citations, 1):
                response_parts.append(f"{i}. [{citation.get('title', 'Source')}]({citation.get('url', '#')})")
        
        # Add research metadata
        metadata = research_result.get("metadata", {})
        tool_calls = research_result.get("tool_calls", [])
        
        if metadata or tool_calls:
            response_parts.append("\n## Research Methodology")
            
            if metadata.get("tool_calls_count"):
                response_parts.append(f"- **Tool Calls Made:** {metadata['tool_calls_count']}")
            
            if research_result.get("model"):
                response_parts.append(f"- **Research Model:** {research_result['model']}")
            
            # Add reasoning summary if available
            reasoning = research_result.get("reasoning_summary", "")
            if reasoning:
                response_parts.append(f"\n**Research Process:** {reasoning}")
        
        return "\n".join(response_parts)
    
    def _handle_research_error(self, error: Exception, original_query: str) -> str:
        """Handle research errors gracefully."""
        error_message = str(error)
        
        if "timeout" in error_message.lower():
            return f"""I apologize, but the research request timed out. This can happen with very complex research topics.

**Suggestions:**
- Try breaking down your research question into smaller, more specific parts
- Use the mini model for faster results by including "quick" or "mini" in your request
- Consider requesting synchronous execution for simpler queries

**Original Query:** {original_query}

Would you like me to try a simplified version of this research?"""
        
        elif "api" in error_message.lower() or "key" in error_message.lower():
            return """I apologize, but I'm currently unable to access the deep research service due to a configuration issue. Please contact your administrator to ensure the OpenAI API is properly configured.

In the meantime, I can help you plan your research approach or suggest alternative research strategies."""
        
        else:
            return f"""I apologize, but I encountered an unexpected error during research: {error_message}

**What you can try:**
- Rephrase your research question
- Be more specific about what you're looking for
- Try a simpler research request first

**Original Query:** {original_query}

Please let me know if you'd like to try a different approach to this research."""
