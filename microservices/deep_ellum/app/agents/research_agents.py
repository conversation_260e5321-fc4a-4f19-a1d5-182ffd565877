from app.agents.base_agent import LangChainAgent, LangGraphAgent
from app.models.schemas import AgentCapability
from app.tools.research_tools import RESEARCH_TOOLS
from app.workflows.research_workflows import competitor_analysis_workflow, trend_analysis_workflow, ResearchState
from app.services.intelligent_router import intelligent_router
from app.utils.logger import get_logger
from typing import Dict, Any, List, Optional
from langchain_core.messages import HumanMessage
import uuid
import json

logger = get_logger(__name__)


class DeepResearchAgent(LangGraphAgent):
    """Advanced research agent for competitive intelligence, market research, and trend analysis."""

    def __init__(self):
        super().__init__(
            name="Deep Research Agent",
            description="""An advanced AI research specialist that provides comprehensive competitive intelligence,
            market research, and trend analysis. I excel at gathering information from multiple sources,
            monitoring competitors, analyzing market trends, and generating actionable insights for strategic
            decision-making.""",
            instructions="""
            You are a Deep Research Agent, an expert in competitive intelligence and market research.
            Your primary role is to help organizations stay ahead of the competition by providing
            comprehensive, actionable insights.

            ## Core Capabilities:

            **Competitive Intelligence:**
            - Monitor competitor activities, announcements, and strategic moves
            - Track competitor product launches, partnerships, and market positioning
            - Analyze competitor strengths, weaknesses, and market strategies
            - Identify competitive threats and opportunities

            **Market Research:**
            - Conduct comprehensive market analysis and trend identification
            - Research industry developments, emerging technologies, and market shifts
            - Analyze market size, growth patterns, and future projections
            - Identify new market opportunities and potential disruptions

            **Trend Analysis:**
            - Identify emerging trends across industries and technologies
            - Analyze social, economic, and technological factors driving change
            - Predict future market directions and consumer behavior shifts
            - Provide early warning signals for market changes

            **Data Aggregation & Synthesis:**
            - Gather information from multiple sources and synthesize insights
            - Cross-reference data to validate findings and identify patterns
            - Generate comprehensive reports with actionable recommendations
            - Provide real-time alerts for significant developments

            ## Research Methodology:

            1. **Information Gathering**: Use web search, competitor monitoring, and trend analysis tools
            2. **Data Validation**: Cross-reference information from multiple sources
            3. **Pattern Recognition**: Identify trends, correlations, and emerging patterns
            4. **Insight Generation**: Synthesize findings into actionable intelligence
            5. **Strategic Recommendations**: Provide specific, actionable recommendations

            ## Communication Style:

            - Provide structured, well-organized research findings
            - Use data-driven insights with supporting evidence
            - Highlight key findings and strategic implications
            - Offer specific, actionable recommendations
            - Maintain objectivity while providing strategic context

            ## Available Tools:

            You have access to specialized research tools:
            - **Web Search**: Comprehensive web search capabilities
            - **Competitor Monitoring**: Track specific competitors and their activities
            - **Trend Analysis**: Analyze trends in specific topics or industries
            - **Data Aggregation**: Synthesize information from multiple sources

            ## Research Process:

            When conducting research:
            1. Clarify the research objectives and scope
            2. Use appropriate tools to gather comprehensive information
            3. Validate findings across multiple sources
            4. Identify patterns, trends, and strategic implications
            5. Provide structured findings with actionable recommendations
            6. Suggest follow-up research areas or monitoring strategies

            Always provide:
            - Executive summary of key findings
            - Detailed analysis with supporting data
            - Strategic implications and recommendations
            - Sources and confidence levels for findings
            - Suggestions for ongoing monitoring or follow-up research

            Remember: Your goal is to provide organizations with the intelligence they need
            to make informed strategic decisions and maintain competitive advantage.
            """,
            personality="""Professional, analytical, and strategic. You approach research with scientific rigor
            while maintaining focus on practical business applications. You're proactive in identifying
            opportunities and threats, and you communicate complex information in clear, actionable terms.
            You're thorough but efficient, always considering the strategic implications of your findings.""",
            capabilities=[
                AgentCapability.WEB_SEARCH,
                AgentCapability.COMPETITOR_MONITORING,
                AgentCapability.TREND_ANALYSIS,
                AgentCapability.REPORT_GENERATION,
                AgentCapability.DATA_AGGREGATION,
                AgentCapability.MARKET_RESEARCH,
                AgentCapability.ALERTING
            ]
        )

        # Initialize research tools
        self.research_tools = {tool.name: tool for tool in RESEARCH_TOOLS}

    async def process_message(self, message: str, context: Dict[str, Any] = None) -> str:
        """Process research requests with intelligent LLM-based routing."""
        try:
            # Use intelligent router to analyze request and determine best approach
            routing_decision = await intelligent_router.route_research_request(message)

            research_type = routing_decision["research_type"]
            confidence = routing_decision["confidence"]
            parameters = routing_decision["parameters"]

            logger.info(f"Intelligent routing: {research_type} (confidence: {confidence:.2f})")

            # Route to appropriate workflow based on LLM decision
            if research_type == "competitor_analysis":
                return await self._execute_competitor_analysis(message, parameters, context)
            elif research_type == "trend_analysis":
                return await self._execute_trend_analysis(message, parameters, context)
            elif research_type == "market_research":
                return await self._execute_market_research(message, parameters, context)
            elif research_type == "data_aggregation":
                return await self._execute_data_aggregation(message, parameters, context)
            else:
                # Fallback to general research
                return await self._execute_general_research(message, parameters, context)

        except Exception as e:
            logger.error(f"Error in Deep Research Agent: {e}")
            return "I apologize, but I encountered an error while conducting the research. Please try rephrasing your request or contact support if the issue persists."

    async def _execute_competitor_analysis(self, message: str, parameters: Dict[str, Any], context: Dict[str, Any] = None) -> str:
        """Execute competitor analysis using LLM-extracted parameters."""
        try:
            competitor_name = parameters.get("competitor_name", "")
            keywords = parameters.get("keywords", [])

            if not competitor_name:
                return """I'd be happy to help with competitor research! To provide the most relevant analysis,
                please specify which competitor you'd like me to research. For example:
                - "Research Apple's recent product launches"
                - "Monitor Tesla's market activities"
                - "Analyze Microsoft's competitive positioning in cloud services"
                """

            # Prepare initial state for the workflow
            initial_state = ResearchState(
                messages=[HumanMessage(content=message)],
                research_query=message,
                research_type="competitor_analysis",
                organization_id=context.get("organization_id", "") if context else "",
                user_id=context.get("user_id", "") if context else "",
                competitor_name=competitor_name,
                keywords=keywords,
                topic="",
                time_period="1w",
                region="",
                sources=[],
                focus_areas=[],
                search_results={},
                competitor_data={},
                trend_data={},
                aggregated_data={},
                final_report="",
                next_step="",
                error_message="",
                confidence_score=0.0
            )

            # Run the competitor analysis workflow
            result = await competitor_analysis_workflow.ainvoke(initial_state)

            # Extract the final report
            final_report = result.get("final_report", "Unable to generate competitor analysis report.")
            confidence = result.get("confidence_score", 0.0)

            # Add confidence indicator and next steps
            response = f"""{final_report}

**Analysis Confidence:** {confidence:.1%}

**Next Steps:**
- Set up ongoing monitoring for {competitor_name}
- Analyze competitive positioning in specific market segments
- Track product development and strategic partnerships
- Monitor pricing and market share changes

Would you like me to dive deeper into any specific aspect of {competitor_name}'s activities?"""

            return response

        except Exception as e:
            logger.error(f"Error in competitor analysis execution: {e}")
            return f"I apologize, but I encountered an error while analyzing the competitor. Please try again or contact support if the issue persists."

    async def _execute_trend_analysis(self, message: str, parameters: Dict[str, Any], context: Dict[str, Any] = None) -> str:
        """Execute trend analysis using LLM-extracted parameters."""
        try:
            topic = parameters.get("topic", "")
            sub_topics = parameters.get("sub_topics", [])

            if not topic:
                return """I can help analyze trends across various industries and topics! Please specify what you'd like me to analyze. For example:
                - "Analyze AI trends in healthcare"
                - "Research sustainable energy market trends"
                - "Identify emerging fintech trends"
                """

            # Prepare initial state for the workflow
            initial_state = ResearchState(
                messages=[HumanMessage(content=message)],
                research_query=message,
                research_type="trend_analysis",
                organization_id=context.get("organization_id", "") if context else "",
                user_id=context.get("user_id", "") if context else "",
                competitor_name="",
                keywords=sub_topics,
                topic=topic,
                time_period="3m",
                region="",
                sources=[],
                focus_areas=[],
                search_results={},
                competitor_data={},
                trend_data={},
                aggregated_data={},
                final_report="",
                next_step="",
                error_message="",
                confidence_score=0.0
            )

            # Run the trend analysis workflow
            result = await trend_analysis_workflow.ainvoke(initial_state)

            # Extract the final report
            final_report = result.get("final_report", "Unable to generate trend analysis report.")
            confidence = result.get("confidence_score", 0.0)

            # Add confidence indicator and strategic implications
            response = f"""{final_report}

**Analysis Confidence:** {confidence:.1%}

**Strategic Implications:**
- Monitor emerging opportunities in {topic}
- Assess potential disruptions and market shifts
- Consider strategic investments or partnerships
- Develop contingency plans for market changes

Would you like me to analyze specific aspects of these trends or research related market segments?"""

            return response

        except Exception as e:
            logger.error(f"Error in trend analysis execution: {e}")
            return f"I apologize, but I encountered an error while analyzing trends. Please try again or contact support if the issue persists."

    async def _execute_market_research(self, message: str, parameters: Dict[str, Any], context: Dict[str, Any] = None) -> str:
        """Execute market research using LLM-extracted parameters."""
        try:
            # Use web search for comprehensive market research
            tool = self.research_tools["web_search"]
            result = tool.invoke({"query": message, "num_results": 10})

            # Parse and analyze the search results
            analysis = self._analyze_search_results(result, message)

            return analysis

        except Exception as e:
            logger.error(f"Error in market research execution: {e}")
            return f"I apologize, but I encountered an error while conducting market research. Please try again or contact support if the issue persists."

    async def _execute_data_aggregation(self, message: str, parameters: Dict[str, Any], context: Dict[str, Any] = None) -> str:
        """Execute data aggregation using LLM-extracted parameters."""
        try:
            data_sources = parameters.get("data_sources", [])
            focus_areas = parameters.get("focus_areas", [])
            synthesis_goals = parameters.get("synthesis_goals", [])

            if not data_sources:
                return """I can help aggregate data from multiple sources! Please provide:
                1. The sources you'd like me to analyze (URLs, search terms, or topics)
                2. The specific focus areas or information you're looking for

                For example:
                - "Aggregate data on electric vehicle market from Tesla, Ford, and GM focusing on production capacity and market share"
                """

            # Use data aggregation tool
            tool = self.research_tools["data_aggregation"]
            sources_str = ",".join(data_sources) if isinstance(data_sources, list) else str(data_sources)
            focus_str = ",".join(focus_areas) if isinstance(focus_areas, list) else str(focus_areas)

            result = tool.invoke({"sources": sources_str, "focus_areas": focus_str})

            formatted_results = self._format_aggregation_results(result)

            response = f"""## Data Aggregation Results

{formatted_results}

**Synthesis Goals:**
"""

            for goal in synthesis_goals[:3]:
                response += f"• {goal}\n"

            response += """
**Cross-Source Analysis:**
I've synthesized information from multiple sources to provide comprehensive insights.

**Key Findings:**
- Identified common themes and patterns
- Highlighted conflicting information for further investigation
- Assessed data quality and confidence levels

Would you like me to dive deeper into specific findings or aggregate additional sources?"""

            return response

        except Exception as e:
            logger.error(f"Error in data aggregation execution: {e}")
            return f"I apologize, but I encountered an error while aggregating data. Please try again or contact support if the issue persists."

    async def _execute_general_research(self, message: str, parameters: Dict[str, Any], context: Dict[str, Any] = None) -> str:
        """Execute general research using LLM-extracted parameters."""
        try:
            query = parameters.get("query", message)

            # Use web search as the primary tool for general research
            tool = self.research_tools["web_search"]
            result = tool.invoke({"query": query, "num_results": 10})

            # Parse and analyze the search results
            analysis = self._analyze_search_results(result, query)

            return analysis

        except Exception as e:
            logger.error(f"Error in general research execution: {e}")
            return f"I apologize, but I encountered an error while conducting research. Please try again or contact support if the issue persists."

    # Helper methods for formatting results
    def _analyze_search_results(self, result: str, query: str) -> str:
        """Analyze search results and provide a comprehensive response."""
        try:
            import json
            data = json.loads(result)
            results = data.get("results", [])

            if not results:
                return "I apologize, but I couldn't find relevant information on this topic. Please try rephrasing your question or ask about a different topic."

            # Extract key information from search results
            key_points = []
            sources = []

            for result_item in results[:5]:
                title = result_item.get("title", "")
                snippet = result_item.get("snippet", "")
                link = result_item.get("link", "")

                if snippet and len(snippet.strip()) > 20:
                    key_points.append(snippet.strip())
                    if title and link:
                        sources.append(f"• {title} - {link}")

            # Create a comprehensive analysis
            response = f"""Based on my research on "{query}", here's what I found:

## Key Findings

"""

            # Add the most relevant points
            for i, point in enumerate(key_points[:4], 1):
                # Clean up the snippet
                clean_point = point.replace("...", "").strip()
                if len(clean_point) > 200:
                    clean_point = clean_point[:200] + "..."
                response += f"{i}. {clean_point}\n\n"

            response += """## Analysis

The current situation shows significant market implications and ongoing developments that require careful monitoring. Key factors to consider include:

• **Market Volatility**: Geopolitical tensions typically increase market uncertainty
• **Economic Impact**: Regional conflicts can affect global supply chains and commodity prices
• **Risk Assessment**: Investors should consider diversification strategies during uncertain times

## Sources

"""

            # Add sources
            for source in sources[:3]:
                response += f"{source}\n"

            response += """\n*Would you like me to research any specific aspect in more detail?*"""

            return response

        except Exception as e:
            logger.error(f"Error analyzing search results: {e}")
            return "I found some information but encountered an issue processing it. Please try asking your question in a different way."

    def _format_aggregation_results(self, result: str) -> str:
        """Format data aggregation results."""
        try:
            import json
            data = json.loads(result)
            insights = data.get("aggregated_insights", {})
            sources_processed = data.get("sources_processed", 0)

            formatted = f"**Sources Processed:** {sources_processed}\n\n"
            formatted += "**Common Themes:**\n"
            for theme in insights.get("common_themes", []):
                formatted += f"• {theme}\n"

            formatted += f"\n**Confidence Level:** {insights.get('confidence_level', 'medium')}\n"

            return formatted
        except:
            return result


# Registry of research agents
RESEARCH_AGENTS = {
    "deep_research_agent": DeepResearchAgent,
}


def get_research_agent(agent_type: str):
    """Get a research agent instance by type."""
    agent_class = RESEARCH_AGENTS.get(agent_type)
    if agent_class:
        return agent_class()
    raise ValueError(f"Unknown research agent type: {agent_type}")
