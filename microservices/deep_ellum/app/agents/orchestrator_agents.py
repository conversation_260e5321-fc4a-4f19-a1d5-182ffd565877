from app.agents.base_agent import LangChainAgent, LangGraphAgent
from app.models.schemas import AgentCapability
from app.utils.logger import get_logger
from typing import Dict, Any, List, Optional

logger = get_logger(__name__)


class OrchestratorAgent(LangGraphAgent):
    """Orchestrator agent for coordinating multiple agents and chatbots and managing complex workflows."""

    def __init__(self):
        super().__init__(
            name="Orchestrator Agent",
            description="AI agent that coordinates multiple specialized agents and chatbots to handle complex, multi-step tasks autonomously",
            instructions="""You are an Orchestrator Agent, a sophisticated autonomous coordinator that manages complex workflows
            by delegating tasks to specialized agents and chatbots and synthesizing their outputs.

            ## Core Responsibilities:

            **Task Analysis:**
            - Break down complex requests into manageable subtasks
            - Identify which specialized agents or chatbots are needed
            - Determine optimal task sequencing and dependencies
            - Coordinate parallel and sequential workflows

            **Agent & Chatbot Coordination:**
            - Delegate specific tasks to appropriate agents (research, calendar) or chatbots (code gen, docs, accessibility)
            - Manage communication between different AI entities
            - Synthesize outputs from multiple agents and chatbots
            - Ensure consistency and quality across all outputs

            **Workflow Management:**
            - Monitor task progress and handle errors autonomously
            - Adapt workflows based on intermediate results
            - Provide status updates and progress tracking
            - Optimize resource allocation and timing
            - Make autonomous decisions about workflow adjustments

            **Quality Assurance:**
            - Review and validate outputs from specialized agents and chatbots
            - Ensure coherence across multi-agent responses
            - Handle conflicts and inconsistencies
            - Provide comprehensive final deliverables

            ## Available Agents:
            - Deep Research Agent: Competitive intelligence and market research
            - Content Calendar Agent: Content planning and scheduling
            - OpenAI Deep Research Agent: Advanced research capabilities

            ## Available Chatbots:
            - Code Generator Chatbot: Code creation and review
            - Accessibility Advisor Chatbot: Accessibility guidance
            - Documentation Specialist Chatbot: Technical documentation

            ## Workflow Types:
            - Sequential: Tasks that must be completed in order
            - Parallel: Independent tasks that can run simultaneously
            - Conditional: Tasks that depend on previous results
            - Iterative: Tasks that require multiple rounds of refinement
            - Hybrid: Complex workflows combining multiple patterns

            ## Autonomous Capabilities:
            - Make decisions about which agents/chatbots to use
            - Automatically retry failed tasks with different approaches
            - Optimize workflows based on performance metrics
            - Handle complex multi-step processes without human intervention
            - Provide comprehensive project management and coordination

            Always provide clear coordination, autonomous decision-making, and comprehensive results.""",
            personality="Strategic, organized, and autonomous. You excel at seeing the big picture while managing details and making intelligent decisions about workflow optimization.",
            capabilities=[
                AgentCapability.TASK_COORDINATION,
                AgentCapability.WORKFLOW_MANAGEMENT,
                AgentCapability.MULTI_AGENT_COORDINATION,
                AgentCapability.PROJECT_MANAGEMENT,
                AgentCapability.QUALITY_ASSURANCE,
                AgentCapability.AUTONOMOUS_DECISION_MAKING,
                AgentCapability.PROCESS_OPTIMIZATION
            ]
        )

    async def process_message(self, message: str, context: Dict[str, Any] = None) -> str:
        """Process message with autonomous orchestration capabilities."""
        try:
            # Analyze the request to determine if orchestration is needed
            if self._requires_orchestration(message):
                return await self._orchestrate_workflow(message, context)
            else:
                # Handle simple requests directly
                return await super().process_message(message, context)

        except Exception as e:
            logger.error(f"Error in Orchestrator Agent: {e}")
            return "I apologize, but I encountered an error while coordinating the workflow. I'll analyze the issue and adjust my approach. Please try again or provide more specific requirements."

    def _requires_orchestration(self, message: str) -> bool:
        """Determine if a message requires multi-agent/chatbot orchestration."""
        orchestration_keywords = [
            "create and document", "build and test", "generate and review",
            "develop and audit", "code and accessibility", "multiple tasks",
            "end-to-end", "complete solution", "full implementation",
            "research and develop", "analyze and create", "comprehensive project",
            "multi-step", "coordinate", "orchestrate", "manage workflow"
        ]
        
        message_lower = message.lower()
        return any(keyword in message_lower for keyword in orchestration_keywords)

    async def _orchestrate_workflow(self, message: str, context: Dict[str, Any] = None) -> str:
        """Autonomously orchestrate a multi-agent/chatbot workflow."""
        # This is a simplified implementation
        # In a full implementation, this would use the agent communication service
        
        # Analyze the request to determine optimal workflow
        workflow_analysis = self._analyze_workflow_requirements(message)
        
        workflow_plan = f"""## Autonomous Workflow Analysis for: "{message}"

I've analyzed your request and determined it requires coordination between multiple specialized agents and chatbots. As an autonomous orchestrator, I'll manage this workflow intelligently.

### Workflow Strategy:
{workflow_analysis['strategy']}

### Proposed Execution Plan:
{self._format_execution_plan(workflow_analysis['steps'])}

### Autonomous Decisions Made:
- **Agent Selection**: {workflow_analysis['agent_selection_rationale']}
- **Workflow Pattern**: {workflow_analysis['workflow_pattern']}
- **Quality Assurance**: {workflow_analysis['qa_approach']}
- **Error Handling**: Automatic retry with alternative approaches if needed

### Expected Deliverables:
{self._format_deliverables(workflow_analysis['deliverables'])}

### Next Steps:
I'll proceed autonomously with this workflow, making intelligent decisions at each step. I'll:
1. Execute tasks in optimal order
2. Monitor progress and adjust as needed
3. Ensure quality across all outputs
4. Provide you with a comprehensive final result

**Status**: Ready to begin autonomous execution. I'll coordinate all agents and chatbots to deliver your complete solution.

Would you like me to proceed with autonomous execution, or do you have specific preferences for any part of the workflow?"""

        return workflow_plan

    def _analyze_workflow_requirements(self, message: str) -> Dict[str, Any]:
        """Analyze the message to determine optimal workflow requirements."""
        # This would be more sophisticated in a real implementation
        message_lower = message.lower()
        
        # Determine required agents/chatbots
        agents_needed = []
        chatbots_needed = []
        
        if any(keyword in message_lower for keyword in ["research", "analyze", "competitive", "market", "trends"]):
            agents_needed.append("Deep Research Agent")
        
        if any(keyword in message_lower for keyword in ["content", "calendar", "social", "schedule", "post"]):
            agents_needed.append("Content Calendar Agent")
            
        if any(keyword in message_lower for keyword in ["code", "programming", "development", "software"]):
            chatbots_needed.append("Code Generator Chatbot")
            
        if any(keyword in message_lower for keyword in ["accessibility", "wcag", "inclusive", "a11y"]):
            chatbots_needed.append("Accessibility Advisor Chatbot")
            
        if any(keyword in message_lower for keyword in ["documentation", "docs", "guide", "manual"]):
            chatbots_needed.append("Documentation Specialist Chatbot")
        
        # Determine workflow pattern
        if len(agents_needed) + len(chatbots_needed) > 2:
            workflow_pattern = "Hybrid (Sequential + Parallel)"
        elif "and" in message_lower or "then" in message_lower:
            workflow_pattern = "Sequential"
        else:
            workflow_pattern = "Parallel"
        
        return {
            "strategy": f"Multi-stage autonomous coordination involving {len(agents_needed)} agents and {len(chatbots_needed)} chatbots",
            "steps": agents_needed + chatbots_needed,
            "agent_selection_rationale": f"Selected based on task requirements: {', '.join(agents_needed + chatbots_needed)}",
            "workflow_pattern": workflow_pattern,
            "qa_approach": "Continuous validation with final integration review",
            "deliverables": ["Comprehensive solution", "Quality assurance report", "Integration guidance"]
        }

    def _format_execution_plan(self, steps: List[str]) -> str:
        """Format the execution plan for display."""
        if not steps:
            return "- Single-step execution with direct response"
        
        formatted_steps = []
        for i, step in enumerate(steps, 1):
            formatted_steps.append(f"{i}. **{step}**: Execute specialized task")
        
        formatted_steps.append(f"{len(steps) + 1}. **Integration & QA**: Synthesize outputs and ensure quality")
        
        return "\n".join(formatted_steps)

    def _format_deliverables(self, deliverables: List[str]) -> str:
        """Format the expected deliverables for display."""
        return "\n".join([f"- {deliverable}" for deliverable in deliverables])


# Registry of orchestrator agents
ORCHESTRATOR_AGENTS = {
    "orchestrator": OrchestratorAgent,
}


def get_orchestrator_agent(agent_type: str):
    """Get an orchestrator agent instance by type."""
    agent_class = ORCHESTRATOR_AGENTS.get(agent_type)
    if agent_class:
        return agent_class()
    raise ValueError(f"Unknown orchestrator agent type: {agent_type}")
