from typing import Dict, Any, Optional, List
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from app.models.models import CustomAgent
from app.models.schemas import AgentCapability
from app.agents.base_agent import Lang<PERSON>hainAgent, LangGraphAgent
from app.agents.research_agents import get_research_agent, RESEARCH_AGENTS
from app.agents.calendar_agents import ContentCalendarAgent
from app.agents.orchestrator_agents import get_orchestrator_agent, ORCHESTRATOR_AGENTS
from app.utils.logger import get_logger
from datetime import datetime, timedelta

logger = get_logger(__name__)


class AgentFactory:
    """Factory for creating and managing AI agents with dynamic loading and cache management."""

    def __init__(self):
        self._agent_cache: Dict[str, Any] = {}
        self._cache_timestamps: Dict[str, datetime] = {}
        self._cache_ttl = timedelta(minutes=5)  # Cache TTL for dynamic refresh
        self._registry_cache: Optional[Dict[str, Any]] = None
        self._registry_cache_timestamp: Optional[datetime] = None
    
    async def create_agent_from_db(self, db: AsyncSession, agent_id: UUID, force_refresh: bool = False) -> Optional[LangChainAgent]:
        """Create an agent instance from database record."""
        try:
            cache_key = str(agent_id)

            # Check cache first (unless force refresh is requested)
            if not force_refresh and cache_key in self._agent_cache:
                # Check if cache is still valid
                if self._is_cache_valid(cache_key):
                    return self._agent_cache[cache_key]
                else:
                    # Cache expired, remove it
                    self._remove_from_cache(cache_key)

            # Fetch agent from database
            result = await db.execute(
                select(CustomAgent).where(CustomAgent.id == agent_id)
            )
            agent_record = result.scalar_one_or_none()

            if not agent_record:
                logger.warning(f"Agent not found: {agent_id}")
                return None

            # Create agent instance
            agent = self._create_agent_instance(agent_record)

            # Cache the agent with timestamp
            self._agent_cache[cache_key] = agent
            self._cache_timestamps[cache_key] = datetime.now()

            logger.debug(f"Created/cached agent: {agent_record.name} (ID: {agent_id})")
            return agent

        except Exception as e:
            logger.error(f"Error creating agent from DB: {e}")
            return None
    
    def _create_agent_instance(self, agent_record: CustomAgent) -> LangChainAgent:
        """Create an agent instance from a database record."""
        # Convert capabilities from JSON to enum list
        capabilities = []
        if agent_record.capabilities:
            for cap in agent_record.capabilities:
                try:
                    capabilities.append(AgentCapability(cap))
                except ValueError:
                    logger.warning(f"Unknown capability: {cap}")
        
        # Determine agent type based on capabilities or use default
        if AgentCapability.CODE_GENERATION in capabilities:
            agent_class = LangGraphAgent  # Use LangGraph for complex code generation
        else:
            agent_class = LangChainAgent  # Use LangChain for simpler interactions
        
        # Create agent instance
        agent = agent_class(
            name=agent_record.name,
            description=agent_record.description,
            instructions=agent_record.instructions,
            personality=agent_record.personality,
            capabilities=capabilities
        )
        
        return agent
    
    def create_sample_agent(self, agent_type: str) -> Optional[Any]:
        """Create a sample agent by type."""
        try:
            if agent_type in RESEARCH_AGENTS:
                return get_research_agent(agent_type)
            elif agent_type == "content_calendar":
                return ContentCalendarAgent()
            elif agent_type in ORCHESTRATOR_AGENTS:
                return get_orchestrator_agent(agent_type)
            else:
                raise ValueError(f"Unknown sample agent type: {agent_type}")
        except ValueError as e:
            logger.error(f"Error creating sample agent: {e}")
            return None
    
    async def get_agent(self, db: AsyncSession, agent_id: UUID) -> Optional[Any]:
        """Get an agent instance, creating it if necessary."""
        try:
            # First check if this is a sample agent by checking the database
            result = await db.execute(
                select(CustomAgent).where(CustomAgent.id == agent_id)
            )
            agent_record = result.scalar_one_or_none()

            if agent_record and agent_record.is_sample:
                # For sample agents, check if we have a specialized implementation
                if agent_record.name == "Deep Research Agent":
                    from app.agents.research_agents import DeepResearchAgent
                    return DeepResearchAgent()
                elif agent_record.name == "OpenAI Deep Research Agent":
                    try:
                        from app.agents.openai_deep_research_agent import OpenAIDeepResearchAgent
                        return OpenAIDeepResearchAgent()
                    except ImportError:
                        logger.warning("OpenAI Deep Research Agent not available, falling back to regular Deep Research Agent")
                        from app.agents.research_agents import DeepResearchAgent
                        return DeepResearchAgent()
                elif agent_record.name == "Content Calendar Agent":
                    from app.agents.calendar_agents import ContentCalendarAgent
                    return ContentCalendarAgent()
                elif agent_record.name == "Orchestrator Agent":
                    from app.agents.orchestrator_agents import OrchestratorAgent
                    return OrchestratorAgent()

            # For non-sample agents or sample agents without specialized implementations,
            # create from database record
            return await self.create_agent_from_db(db, agent_id)

        except Exception as e:
            logger.error(f"Error getting agent {agent_id}: {e}")
            return None
    
    def clear_cache(self, agent_id: Optional[UUID] = None):
        """Clear agent cache."""
        if agent_id:
            cache_key = str(agent_id)
            self._remove_from_cache(cache_key)
        else:
            self._agent_cache.clear()
            self._cache_timestamps.clear()
            self._registry_cache = None
            self._registry_cache_timestamp = None
            logger.info("Cleared all agent caches")

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cache entry is still valid."""
        if cache_key not in self._cache_timestamps:
            return False

        cache_time = self._cache_timestamps[cache_key]
        return datetime.now() - cache_time < self._cache_ttl

    def _remove_from_cache(self, cache_key: str):
        """Remove specific entry from cache."""
        self._agent_cache.pop(cache_key, None)
        self._cache_timestamps.pop(cache_key, None)

    def invalidate_agent_cache(self, agent_id: UUID):
        """Invalidate cache for a specific agent (called when agent is updated/deleted)."""
        cache_key = str(agent_id)
        self._remove_from_cache(cache_key)
        # Also invalidate registry cache since it contains agent info
        self._registry_cache = None
        self._registry_cache_timestamp = None
        logger.info(f"Invalidated cache for agent: {agent_id}")

    def invalidate_registry_cache(self):
        """Invalidate the entire registry cache (called when new agents are added)."""
        self._registry_cache = None
        self._registry_cache_timestamp = None
        logger.info("Invalidated agent registry cache")
    
    def get_sample_agents_info(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all available sample agents."""
        from app.agents.sample_agents import list_sample_agents
        return list_sample_agents()
    
    async def create_custom_agent(
        self,
        name: str,
        description: str,
        instructions: str,
        personality: Optional[str] = None,
        capabilities: Optional[List[AgentCapability]] = None
    ) -> Any:
        """Create a custom agent instance without saving to database."""
        capabilities = capabilities or []
        
        # Determine agent type
        if AgentCapability.CODE_GENERATION in capabilities:
            agent_class = LangGraphAgent
        else:
            agent_class = LangChainAgent
        
        agent = agent_class(
            name=name,
            description=description,
            instructions=instructions,
            personality=personality,
            capabilities=capabilities
        )
        
        return agent

    async def get_fresh_agent_registry(self, db: AsyncSession, organization_id: Optional[str] = None) -> Dict[str, Any]:
        """Get a fresh agent registry without using cache."""
        try:
            registry = {}

            # Add sample research agents
            for agent_type, agent_class in RESEARCH_AGENTS.items():
                try:
                    agent_instance = agent_class()
                    registry[agent_type] = {
                        "instance": agent_instance,
                        "name": agent_instance.name,
                        "description": agent_instance.description,
                        "capabilities": [cap.value for cap in agent_instance.capabilities],
                        "type": "sample"
                    }
                except Exception as e:
                    logger.error(f"Error creating sample agent {agent_type}: {e}")

            # Add calendar agent
            try:
                calendar_agent = ContentCalendarAgent()
                registry["content_calendar"] = {
                    "instance": calendar_agent,
                    "name": calendar_agent.name,
                    "description": calendar_agent.description,
                    "capabilities": [cap.value for cap in calendar_agent.capabilities],
                    "type": "sample"
                }
            except Exception as e:
                logger.error(f"Error creating calendar agent: {e}")

            # Add orchestrator agents
            for agent_type, agent_class in ORCHESTRATOR_AGENTS.items():
                try:
                    agent_instance = agent_class()
                    registry[agent_type] = {
                        "instance": agent_instance,
                        "name": agent_instance.name,
                        "description": agent_instance.description,
                        "capabilities": [cap.value for cap in agent_instance.capabilities],
                        "type": "sample"
                    }
                except Exception as e:
                    logger.error(f"Error creating orchestrator agent {agent_type}: {e}")

            # Add custom agents from database
            if db and organization_id:
                try:
                    custom_agents_query = select(CustomAgent).where(
                        and_(
                            CustomAgent.organization_id == organization_id,
                            CustomAgent.is_active == True,
                            CustomAgent.is_sample == False
                        )
                    )

                    result = await db.execute(custom_agents_query)
                    custom_agents = result.scalars().all()

                    for agent_record in custom_agents:
                        try:
                            # Force refresh to get latest data
                            agent_instance = await self.create_agent_from_db(db, agent_record.id, force_refresh=True)
                            if agent_instance:
                                registry[f"custom_{agent_record.id}"] = {
                                    "instance": agent_instance,
                                    "name": agent_record.name,
                                    "description": agent_record.description,
                                    "capabilities": agent_record.capabilities or [],
                                    "type": "custom",
                                    "id": str(agent_record.id)
                                }
                        except Exception as e:
                            logger.error(f"Error creating custom agent {agent_record.id}: {e}")

                except Exception as e:
                    logger.error(f"Error fetching custom agents: {e}")

            # Add sample agents from database (global sample agents)
            if db:  # Only if database session is available
                try:
                    sample_agents_query = select(CustomAgent).where(
                        and_(
                            CustomAgent.is_sample == True,
                            CustomAgent.is_active == True
                        )
                    )

                    result = await db.execute(sample_agents_query)
                    db_sample_agents = result.scalars().all()

                    for agent_record in db_sample_agents:
                        # Only add if not already in registry from SAMPLE_AGENTS
                        agent_key = f"db_sample_{agent_record.id}"
                        if agent_key not in registry:
                            try:
                                agent_instance = await self.create_agent_from_db(db, agent_record.id, force_refresh=True)
                                if agent_instance:
                                    registry[agent_key] = {
                                        "instance": agent_instance,
                                        "name": agent_record.name,
                                        "description": agent_record.description,
                                        "capabilities": agent_record.capabilities or [],
                                        "type": "sample",
                                        "id": str(agent_record.id)
                                    }
                            except Exception as e:
                                logger.error(f"Error creating DB sample agent {agent_record.id}: {e}")

                except Exception as e:
                    logger.error(f"Error fetching DB sample agents: {e}")
            else:
                logger.warning("No database session available for fetching sample agents")

            logger.debug(f"Fresh agent registry built with {len(registry)} agents")
            return registry

        except Exception as e:
            logger.error(f"Error building fresh agent registry: {e}")
            return {}
    
    async def test_agent(
        self,
        instructions: str,
        test_message: str,
        personality: Optional[str] = None,
        capabilities: Optional[List[AgentCapability]] = None
    ) -> str:
        """Test an agent configuration with a sample message."""
        try:
            # Create temporary agent
            agent = await self.create_custom_agent(
                name="Test Agent",
                description="Temporary agent for testing",
                instructions=instructions,
                personality=personality,
                capabilities=capabilities
            )
            
            # Process test message
            response = await agent.process_message(test_message)
            return response
            
        except Exception as e:
            logger.error(f"Error testing agent: {e}")
            return f"Error testing agent: {str(e)}"


# Global factory instance
agent_factory = AgentFactory()
