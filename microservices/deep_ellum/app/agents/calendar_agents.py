from app.agents.base_agent import LangChainAgent, LangGraphAgent
from app.models.schemas import AgentCapability
from app.utils.logger import get_logger
from typing import Dict, Any, List, Optional

logger = get_logger(__name__)


class ContentCalendarAgent(LangGraphAgent):
    """Content Calendar Agent that suggests content ideas using knowledgebase data and requires approval before drafting."""

    def __init__(self):
        super().__init__(
            name="Content Calendar Agent",
            description="AI agent that suggests content ideas using organization knowledge and requires approval before drafting content",
            instructions="""You are a Content Calendar Agent specialized in content planning, creation, and intelligent scheduling.

Your primary responsibilities:
1. Analyze user requests for content suggestions, planning, and scheduling
2. Check if knowledgebase access is enabled for the organization
3. Check if socials database access is enabled for the organization
4. Use knowledgebase data to suggest relevant, informed content ideas
5. Use socials database to analyze past performance, optimal timing, and content patterns
6. Provide content suggestions based on organization's knowledge and social media history
7. Require explicit user approval before proceeding with content drafting or scheduling
8. Create content calendars and schedules when requested
9. Suggest content topics, themes, and formats based on available data
10. Analyze optimal posting times using audience insights and engagement data
11. Schedule content automatically across connected social media platforms
12. Integrate scheduled content with shared calendar views and AI interface logs

Key workflow:
- When knowledgebase is connected: Use organization knowledge to inform content suggestions
- When socials database is connected: Use historical performance data and optimal timing insights
- When neither is connected: Inform user to enable them in settings for better suggestions
- Always require approval before drafting any content or scheduling posts
- Provide structured content suggestions with rationale based on available data sources
- Consider content formats, timing, and audience when making suggestions
- Analyze connected social media platforms for scheduling capabilities
- Use intelligent timing analysis to recommend optimal posting times
- Handle autonomous scheduling with approval workflows
- Integrate with shared calendar view for scheduled content tracking

Advanced capabilities:
- Optimal timing analysis based on audience insights and engagement patterns
- Multi-platform scheduling coordination
- Content optimization for different social media platforms
- Autonomous scheduling decisions with intelligent timing recommendations
- Calendar integration and AI interface logging for all scheduled content

You should be creative, strategic, data-driven, and autonomous in your content suggestions and scheduling while always respecting the approval workflow.""",
            personality="Creative, strategic, and collaborative content strategist",
            capabilities=[
                AgentCapability.CONVERSATION,
                AgentCapability.CONTENT_CREATION,
                AgentCapability.CONTENT_PLANNING,
                AgentCapability.CONTENT_STRATEGY,
                AgentCapability.RESEARCH,
                AgentCapability.PLANNING,
                AgentCapability.TASK_COORDINATION,
                AgentCapability.MICROSERVICE_INTEGRATION
            ]
        )
        self.knowledgebase_service = None
        self.agent_context_service = None
        self.socials_integration_service = None
        self.intelligent_socials_service = None
        self.optimal_timing_service = None

    async def process_message(self, message: str, context: Dict[str, Any] = None) -> str:
        """Process message with content calendar functionality."""
        try:
            # Initialize services if not already done
            if not self.knowledgebase_service:
                from app.services.knowledgebase_service import KnowledgebaseService
                from app.services.agent_context_service import AgentContextService
                self.knowledgebase_service = KnowledgebaseService()
                self.agent_context_service = AgentContextService()

                # Initialize scheduling services with error handling
                try:
                    from app.services.socials_integration_service import socials_integration_service
                    from app.services.intelligent_socials_service import intelligent_socials_service
                    from app.services.optimal_timing_service import optimal_timing_service
                    self.socials_integration_service = socials_integration_service
                    self.intelligent_socials_service = intelligent_socials_service
                    self.optimal_timing_service = optimal_timing_service
                except ImportError as e:
                    logger.warning(f"Scheduling services not available: {e}")
                    self.socials_integration_service = None
                    self.intelligent_socials_service = None
                    self.optimal_timing_service = None

            # Get context information
            organization_id = context.get("organization_id") if context else None
            auth_token = context.get("auth_token") if context else None
            db = context.get("db") if context else None

            if not organization_id or not auth_token or not db:
                return "I need organization context to provide content suggestions. Please ensure you're properly authenticated."

            # Check if knowledgebase is enabled
            kb_enabled = await self.agent_context_service.is_knowledgebase_enabled(db, organization_id)

            # Check if socials database is enabled
            socials_enabled = await self.agent_context_service.is_socials_database_enabled(db, organization_id)

            # Enhance message with knowledge if available
            enhanced_message = message
            knowledge_context = ""
            socials_context = ""

            if kb_enabled:
                # Check if knowledgebase has data
                has_knowledge = await self.knowledgebase_service.has_knowledgebase(organization_id, auth_token)

                if has_knowledge:
                    # Get relevant knowledge for content suggestions
                    knowledge_results = await self.knowledgebase_service.get_knowledge_context(
                        message, organization_id, auth_token
                    )

                    if knowledge_results:
                        knowledge_context = f"\n\nRelevant organization knowledge:\n{chr(10).join(knowledge_results)}"
                        enhanced_message = f"{message}{knowledge_context}"
                else:
                    knowledge_context = "\n\nNote: Knowledgebase is enabled but no data is available yet."
            else:
                knowledge_context = "\n\nNote: Knowledgebase access is not enabled. Enable it in settings for better content suggestions based on your organization's knowledge."

            # Enhance message with socials data if available
            if socials_enabled and self.intelligent_socials_service:
                try:
                    # Get socials context for content suggestions
                    socials_data = await self.intelligent_socials_service.get_socials_context(
                        message, organization_id, auth_token
                    )

                    if socials_data:
                        socials_context = "\n\nSocial media insights:"
                        if 'calendar' in socials_data:
                            scheduled_count = len(socials_data['calendar'].get('scheduled_content', []))
                            if scheduled_count > 0:
                                socials_context += f"\n- {scheduled_count} posts currently scheduled"

                        if 'analytics' in socials_data:
                            socials_context += "\n- Historical performance data available for optimization"

                        if 'optimal_times' in socials_data:
                            socials_context += "\n- Optimal posting times data available"

                        enhanced_message = f"{enhanced_message}{socials_context}"
                    else:
                        socials_context = "\n\nNote: Socials database is enabled but no social media data is available yet."
                except Exception as e:
                    logger.error(f"Error getting socials context: {e}")
                    socials_context = "\n\nNote: Unable to access social media data."
            else:
                socials_context = "\n\nNote: Socials database access is not enabled. Enable it in settings for performance-based content suggestions and optimal timing insights."

            # Determine the type of content request
            request_type = await self._analyze_content_request(message)

            # Generate content suggestions based on request type
            if request_type == "content_ideas":
                return await self._generate_content_ideas(enhanced_message, knowledge_context, context)
            elif request_type == "content_calendar":
                return await self._create_content_calendar(enhanced_message, knowledge_context, context)
            elif request_type == "content_draft":
                return await self._handle_content_drafting(enhanced_message, knowledge_context, context)
            elif request_type == "schedule_content":
                return await self._handle_content_scheduling(enhanced_message, knowledge_context, context)
            elif request_type == "optimal_timing":
                return await self._analyze_optimal_timing(enhanced_message, knowledge_context, context)
            elif request_type == "approve_schedule":
                return await self._execute_approved_scheduling(enhanced_message, knowledge_context, context)
            else:
                # General content assistance
                return await self._provide_general_content_assistance(enhanced_message, knowledge_context, context)

        except Exception as e:
            logger.error(f"Error in Content Calendar Agent: {e}")
            return "I apologize, but I encountered an error while processing your content request. Please try again or check if knowledgebase access is properly configured."

    async def _analyze_content_request(self, message: str) -> str:
        """Analyze the user's message to determine the type of content request."""
        message_lower = message.lower()

        if any(keyword in message_lower for keyword in ["content ideas", "suggest content", "content suggestions", "what should i write"]):
            return "content_ideas"
        elif any(keyword in message_lower for keyword in ["content calendar", "plan content", "calendar"]):
            return "content_calendar"
        elif any(keyword in message_lower for keyword in ["schedule content", "schedule post", "schedule this", "post this", "publish this"]):
            return "schedule_content"
        elif any(keyword in message_lower for keyword in ["optimal time", "best time", "when to post", "timing analysis", "posting time"]):
            return "optimal_timing"
        elif any(keyword in message_lower for keyword in ["approve", "yes schedule", "proceed with scheduling", "schedule this", "✅", "confirm scheduling"]):
            return "approve_schedule"
        elif any(keyword in message_lower for keyword in ["draft", "write", "create content", "help me write"]):
            return "content_draft"
        else:
            return "general"

    async def _generate_content_ideas(self, message: str, knowledge_context: str, context: Dict[str, Any]) -> str:
        """Generate content ideas using knowledgebase data."""
        content_context = f"""
{message}

Content Ideas Generation Context:
- You are generating content ideas based on the user's request
- Use the organization's knowledge to suggest relevant topics
- Provide 5-7 specific, actionable content ideas
- Include suggested formats (blog post, video, infographic, etc.)
- Explain why each idea would be valuable based on the knowledge available
- Structure your response with clear headings and bullet points
{knowledge_context}

Format your response as:
## Content Ideas Based on Your Organization's Knowledge

1. **[Content Title]** - [Format]
   - Why this matters: [Explanation based on knowledge]
   - Suggested approach: [Brief outline]

[Continue for each idea...]

## Next Steps
To proceed with any of these ideas, please let me know which one interests you and I'll help you create a detailed outline or draft.
"""

        response = await self.langchain_service.run_agent_workflow(
            message=content_context,
            instructions=self.instructions,
            conversation_history=context.get("conversation_history", []) if context else [],
            capabilities=[cap.value for cap in self.capabilities]
        )
        return response

    async def _create_content_calendar(self, message: str, knowledge_context: str, context: Dict[str, Any]) -> str:
        """Create a content calendar based on organization knowledge."""
        content_context = f"""
{message}

Content Calendar Creation Context:
- You are creating a content calendar based on the user's request
- Use the organization's knowledge to suggest a strategic content schedule
- Consider content themes, timing, and audience engagement
- Provide a structured calendar with specific dates and content types
- Include rationale for timing and content selection
{knowledge_context}

Format your response as:
## Content Calendar Recommendation

### Overview
[Brief explanation of the calendar strategy]

### Monthly/Weekly Schedule
**Week 1:**
- [Date]: [Content Title] - [Format] - [Brief description]
- [Date]: [Content Title] - [Format] - [Brief description]

[Continue for requested timeframe...]

### Content Themes
- Theme 1: [Description and rationale]
- Theme 2: [Description and rationale]

## Next Steps
Would you like me to help you develop any of these content pieces? Please specify which content you'd like to start with.
"""

        response = await self.langchain_service.run_agent_workflow(
            message=content_context,
            instructions=self.instructions,
            conversation_history=context.get("conversation_history", []) if context else [],
            capabilities=[cap.value for cap in self.capabilities]
        )
        return response
