# OpenAI Deep Research Agent

## Overview

The OpenAI Deep Research Agent is an advanced AI research specialist that leverages OpenAI's cutting-edge deep research models (`o3-deep-research` and `o4-mini-deep-research`) to conduct comprehensive, multi-step research with web search and code interpretation capabilities.

## Features

### Core Capabilities
- **Multi-step Research**: Conducts research using hundreds of sources with intelligent analysis
- **Web Search Integration**: Uses OpenAI's web search tool for real-time information gathering
- **Code Interpretation**: Performs complex data analysis using Python code execution
- **Research-Analyst Level Reports**: Generates comprehensive reports with proper citations
- **Background Processing**: Supports long-running research tasks with webhook notifications

### Research Specializations
- Legal and scientific research
- Market analysis and competitive intelligence
- Technical and academic research
- Policy analysis and regulatory research
- Industry trends and emerging technologies

## Models

### o3-deep-research
- **Use Case**: Complex, comprehensive research tasks
- **Capabilities**: Advanced multi-step analysis, hundreds of sources
- **Best For**: Legal research, scientific research, market analysis, academic research

### o4-mini-deep-research
- **Use Case**: Faster research with good depth
- **Capabilities**: Streamlined research, focused analysis
- **Best For**: Quick market insights, trend analysis, competitive intelligence

## API Endpoints

### Conduct Deep Research
```http
POST /api/v1/deep-research
```

**Request Body:**
```json
{
  "query": "Research the economic impact of AI automation on healthcare",
  "model": "o3-deep-research",
  "background": true,
  "max_tool_calls": 50,
  "include_code_interpreter": true,
  "include_web_search": true,
  "instructions": "Focus on recent data and peer-reviewed sources"
}
```

**Response:**
```json
{
  "success": true,
  "response_id": "research_123",
  "model": "o3-deep-research",
  "output_text": "Comprehensive research results...",
  "citations": [...],
  "tool_calls": [...],
  "reasoning_summary": "Research methodology summary",
  "metadata": {...}
}
```

### Check Research Status
```http
GET /api/v1/deep-research/status/{response_id}
```

### Research with Agent
```http
POST /api/v1/deep-research/agent
```

### Get Available Models
```http
GET /api/v1/deep-research/models
```

### Get Research Examples
```http
GET /api/v1/deep-research/examples
```

## Usage Examples

### Basic Research Request
```python
import httpx

async def conduct_research():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://localhost:8003/api/v1/deep-research",
            json={
                "query": "Analyze the competitive landscape of electric vehicles",
                "model": "o3-deep-research",
                "background": True
            },
            headers={"Authorization": "Bearer YOUR_TOKEN"}
        )
        return response.json()
```

### Quick Research with Mini Model
```python
response = await client.post(
    "http://localhost:8003/api/v1/deep-research/agent",
    params={
        "query": "Quick analysis of AI trends in healthcare",
        "model": "o4-mini-deep-research",
        "background": False
    }
)
```

## Configuration

### Environment Variables
```bash
# Required
OPENAI_API_KEY=your_openai_api_key

# Optional
AI_PROVIDER=openai  # Use OpenAI as primary provider
```

### Model Selection Logic
The agent automatically selects the appropriate model based on:
- Query complexity and length
- Explicit user preferences (keywords like "quick", "mini", "comprehensive")
- Research requirements

## Research Process

1. **Query Analysis**: Understand research scope and objectives
2. **Information Gathering**: Use web search to find relevant sources
3. **Data Analysis**: Apply code interpretation for complex analysis
4. **Source Validation**: Cross-reference and verify information
5. **Synthesis**: Create coherent narrative from multiple sources
6. **Citation**: Provide inline citations and source metadata
7. **Insights**: Generate actionable insights and recommendations

## Best Practices

### Query Formulation
- Be specific about information needs
- Include context about industry or use case
- Specify if you need recent data or historical analysis
- Mention preferred sources (academic, industry, news, etc.)
- Include any constraints or focus areas

### Model Selection
- Use `o3-deep-research` for comprehensive, complex research
- Use `o4-mini-deep-research` for faster, focused research
- Background mode is recommended for complex research
- Include specific metrics or data points you need

### Example Queries
```
Good: "Research the economic impact of semaglutide on global healthcare systems. Include specific figures, trends, statistics, and measurable outcomes from peer-reviewed sources."

Better: "Analyze Tesla's competitive positioning in the electric vehicle market, focusing on market share, production capacity, and strategic partnerships in 2024."

Best: "Conduct comprehensive legal research on recent AI regulation developments in the EU, US, and China. Focus on compliance requirements for healthcare AI applications, including GDPR implications and FDA guidance."
```

## Error Handling

The agent provides graceful error handling for:
- API timeouts (suggests breaking down complex queries)
- Configuration issues (guides to proper setup)
- Rate limiting (recommends retry strategies)
- Model availability (automatic fallback options)

## Background Processing

For long-running research tasks:
1. Tasks are executed in background mode by default
2. Status can be checked using the response ID
3. Webhook notifications can be configured
4. Results are stored in the database for retrieval

## Integration with Agent Factory

The OpenAI Deep Research Agent is automatically registered in the agent factory and can be:
- Used as a sample agent
- Customized for specific organizations
- Integrated with other microservices
- Accessed through the unified agent interface

## Database Schema

### Deep Research Tasks
- Task metadata and configuration
- Status tracking and timestamps
- Results and error handling

### Citations
- Source URLs and titles
- Citation positioning in text
- Relevance scoring

### Tool Calls
- Web search and code execution logs
- Performance metrics
- Debugging information

## Monitoring and Analytics

Track research performance through:
- Task completion rates
- Average execution times
- Citation quality metrics
- User satisfaction scores
- Cost optimization insights

## Security Considerations

- API keys are securely managed
- Research queries are logged for audit
- Citations are validated for reliability
- Background tasks have timeout limits
- Webhook URLs are validated

## Troubleshooting

### Common Issues
1. **API Key Not Configured**: Ensure `OPENAI_API_KEY` is set
2. **Timeout Errors**: Use background mode for complex research
3. **Rate Limiting**: Implement exponential backoff
4. **Model Unavailable**: Check OpenAI service status

### Debug Mode
Enable detailed logging by setting `DEBUG=True` in environment variables.

## Future Enhancements

- Custom MCP server integration for private data
- Advanced citation analysis and scoring
- Research template system
- Collaborative research workflows
- Integration with external knowledge bases
