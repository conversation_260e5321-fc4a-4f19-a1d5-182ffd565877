import asyncio
import json
from datetime import datetime
from typing import Annotated, List, Optional
from urllib.parse import urlencode

import httpx
import psycopg2
from dateutil.relativedelta import relativedelta
from fastapi import APIRouter, Depends, HTTPException, Request, status, Query
from fastapi.responses import RedirectResponse
from sqlalchemy import desc, or_, select, func
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.database.session import get_db
from app.models import model
from app.models.model import Comment, Conversation, Post, SocialMediaAccount
from app.schemas.facebook import (
    FacebookCommentResponse,
    CommentResponse,
    FacebookMessagesResponse,
    FacebookPostItem,
    FacebookPostsResponse,
    FacebookSendMessageResponse,
    SendMessageSchema
)
from app.schemas.instagram_schema import (
    CommentReply,
    CommentResponse as IGCommentResponse,
    ConversationResponse,
    GetConversationWithUserResponse,
)
from app.schemas.schema import InitialiseFacebook, Message
from app.services.facebook_post_perf import FacebookTopPostsService
from app.services.facebook import (
    composite_score,
    download_image_from_url,
    extend_token,
    extract_images,
    fetch_conversations_and_messages,
    get_facebook_page_metrics,
    get_page_token,
    get_post_details,
    save_post_to_database,
    upload_image_to_facebook,
)
from app.utils.dependency import (
    create_JWT_Response,
    get_current_user,
    get_social_details,
    verify_organization,
)
from app.utils.logger import get_logger
from app.utils.redis_cache import redis_client
from app.utils.success_response import fail_response, success_response

router = APIRouter()

FACEBOOK_CLIENT_ID = settings.FACEBOOK_CLIENT_ID
FACEBOOK_CLIENT_SECRET = settings.FACEBOOK_CLIENT_SECRET
FACEBOOK_REDIRECT_URI = settings.FACEBOOK_REDIRECT_URI
FACEBOOK_OAUTH_URL = settings.FACEBOOK_OAUTH_URL
FACEBOOK_TOKEN_URL = settings.FACEBOOK_TOKEN_URL

FACEBOOK_GRAPH_API_URL = settings.FACEBOOK_GRAPH_API_URL

FACEBOOK_CONFIG_ID = settings.FACEBOOK_CONFIGURATION_ID

logger = get_logger(__name__)
timeout = httpx.Timeout(30.0, connect=30.0, read=30.0, write=30.0)

redis_api_expiry_time = settings.REDIS_CACHE_EXPIRY_SECONDS
redis_long_expiry_time = settings.REDIS_LONG_CACHE_EXPIRY_SECONDS


if not redis_client.ping():
    raise ConnectionError("Unable to connect to Redis.")
logger.info("Connected to Redis successfully for facebook.")


# ######### FACEBOOK LOGIN WITH BUSINESS ############
@router.get("/login")
async def login_with_facebook():
    """Redirect user to Facebook for login."""
    logger.info("Redirecting user to Facebook for login")
    params = {
        "client_id": FACEBOOK_CLIENT_ID,
        "redirect_uri": FACEBOOK_REDIRECT_URI,
        "config_id": FACEBOOK_CONFIG_ID,
        "response_type": "code",
        "state": "ellumai",
    }
    url = f"{FACEBOOK_OAUTH_URL}?{urlencode(params)}"
    logger.info(f"Redirecting to {url}")
    return {"success": True, "message": url}


@router.get("/callback")
async def auth_callback(request: Request):
    logger.info("Received callback from Facebook")
    code = request.query_params.get("code")
    error = request.query_params.get("error")
    error_reason = request.query_params.get("error_reason")
    error_description = request.query_params.get("error_description")

    if error:
        logger.error(f"Authentication cancelled: {error_reason} - {error_description}")

        redirect_url = (
            f"{settings.FRONTEND_SOCIALS_ENDPOINT}?"
            f"error={error_reason}&error_description={error_description}&social_type=facebook"
            "&_=_"
        )
        return RedirectResponse(
            url=redirect_url,
            status_code=status.HTTP_302_FOUND
        )

    if not code:
        logger.error("Missing 'code' parameter")
        redirect_url = (
            f"{settings.FRONTEND_SOCIALS_ENDPOINT}?"
            f"error={error_reason}&error_description={error_description}&social_type=facebook&message=Missing code parameter"
        )
        return RedirectResponse(
            url=redirect_url,
            status_code=status.HTTP_302_FOUND
        )

    try:
        logger.info("Querying GraphAPI to convert the code to access token")
        async with httpx.AsyncClient(timeout=timeout) as client:
            token_response = await client.get(
                FACEBOOK_TOKEN_URL,
                params={
                    "client_id": FACEBOOK_CLIENT_ID,
                    "redirect_uri": FACEBOOK_REDIRECT_URI,
                    "client_secret": FACEBOOK_CLIENT_SECRET,
                    "code": code,
                },
            )
            token_response.raise_for_status()
            access_token_info = token_response.json()
            access_token = access_token_info["access_token"]

            logger.info(f"Access_token info: {access_token_info}")
            user_info_response = await client.get(
                f"{FACEBOOK_GRAPH_API_URL}/me",
                params={"access_token": access_token, "fields": "id,name,email"},
            )
            user_info_response.raise_for_status()
            user_info = user_info_response.json()
            logger.info(f"Received user info: {user_info}")

        social_id = user_info.get("id")
        social_type = "facebook"
        name = user_info.get("name")
        email = user_info.get("email")

        logger.info("Redirecting to frontend after successfull response")
        # make a JWT of the response
        response = {
            f"{social_type}_id": social_id,
            "social_type": social_type,
            "name": name,
            "email": email,
            "access_token": access_token,
        }
        encoded_response = await create_JWT_Response(response)
        redirect_url = (
            f"{settings.FRONTEND_SOCIALS_ENDPOINT}?"
            f"token={encoded_response}"
            "&_=_"
        )
        return RedirectResponse(
            url=redirect_url,
            status_code=status.HTTP_302_FOUND
        )
    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error during authentication: {str(e)}")
        redirect_url = (
            f"{settings.FRONTEND_SOCIALS_ENDPOINT}?"
            f"error={e}&"
            "social_type=facebook"
        )
        return RedirectResponse(
            url=redirect_url,
            status_code=status.HTTP_302_FOUND
        )
    except Exception as e:
        logger.error(f"Unexpected error during authentication: {str(e)}")
        redirect_url = (
            f"{settings.FRONTEND_SOCIALS_ENDPOINT}?"
            f"error={e}&"
            "social_type=facebook"
        )
        return RedirectResponse(
            url=redirect_url,
            status_code=status.HTTP_302_FOUND
        )


# ############### CONNECT TO FRONTEND ###############
@router.post("/connect_facebook")
async def connect_facebook_to_backend(
    token: Annotated[str, Depends(get_current_user)],
    user_details: InitialiseFacebook,
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
):
    try:
        # await check_permissions(token.get('user_id'), organisation_id, 'can connect socials')

        # Exchange the access token for a long-lived one before storage
        logger.info("Exchanging the short-lived token for a long-lived one")
        access_token, expires_at = await extend_token(user_details.access_token)
        fb_social_account = SocialMediaAccount(
            platform="facebook",
            username=user_details.username,
            social_media_user_id=user_details.social_media_user_id,
            organisation_id=organisation_id,
            access_token=access_token,
            login_status=True,
        )

        db_session.add(fb_social_account)
        await db_session.commit()
        await db_session.refresh(fb_social_account)

        logger.info(
            f"Created social media account: {fb_social_account.platform} -- {fb_social_account.username}"
        )

        # asyncio.create_task(
        #     update_facebook_metrics_immediately(fb_social_account.id)
        # )

        logger.info("Returning successful response")
        return success_response(
            200,
            "Social media account created successfully",
            {
                "username": fb_social_account.username,
                "organisation_id": fb_social_account.organisation_id,
                "social_media_user_id": fb_social_account.social_media_user_id,
                "platform": fb_social_account.platform,
                "login_status": fb_social_account.login_status,
                "access_token_expires_in": expires_at,
            },
        )
    except IntegrityError as e:
        if isinstance(e.orig, psycopg2.errors.UniqueViolation):
            await db_session.rollback()
            logger.error(f"UniqueViolationError: {str(e)}")
            return fail_response(409, "This social media account has already been linked to an organisation")
        elif getattr(e.orig, 'pgcode', None) == '23505':
            await db_session.rollback()
            logger.error(f"UniqueViolationError: {str(e)}")
            return fail_response(409, "This social media account has already been linked to an organisation")
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


# ############### USER INTERACTIONS ###########


# 2. Get a user pages
@router.get("/my-pages")
async def users_pages(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
):
    """Get a list of Pages of the User.

    User needs to select one page to use as the default page for this app
    """
    try:
        result = await db_session.execute(
            select(SocialMediaAccount).filter(
                SocialMediaAccount.platform == "facebook",
                SocialMediaAccount.organisation_id == organisation_id,
                SocialMediaAccount.access_token is not None,
            )
        )
        db_social_account = result.scalars().first()
        if not db_social_account:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="A facebook account has not been connected",
            )

        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                f"{FACEBOOK_GRAPH_API_URL}/{db_social_account.social_media_user_id}/accounts",
                params={
                    "fields": "id,name,access_token",
                    "access_token": db_social_account.access_token,
                },
            )
            response.raise_for_status()
            pages = response.json()
        return success_response(200, "Pages returned successfully", pages.get("data"))

    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


# selected page details
@router.post("/selected_page")
async def selected_page(
    page_id: str,
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
):
    """Adds the selected page details to the social profile"""

    try:
        # check the social profile exists
        result = await db_session.execute(
            select(SocialMediaAccount).filter(
                SocialMediaAccount.platform == "facebook",
                SocialMediaAccount.organisation_id == organisation_id,
                SocialMediaAccount.access_token is not None,
            )
        )
        db_social = result.scalars().first()
        if not db_social:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="A facebook account has not been connected",
            )

        # generate the access token for the page
        page_access_token = await get_page_token(db_social.access_token, page_id)
        db_social.page_id = page_id
        db_social.access_token = page_access_token
        db_social.page_access_token = page_access_token
        await db_session.commit()
        await db_session.refresh(db_social)

        asyncio.create_task(
                fetch_conversations_and_messages(
                    db_social))
        return success_response(200, "Page selected successfully")
    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


# user details
# STATUS: Works
@router.get("/me")
async def user_details(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
):
    """Returns the full details of a user"""
    try:
        db_social_account = await get_social_details(
            organisation_id, "facebook", db_session
        )

        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                f"{FACEBOOK_GRAPH_API_URL}/{db_social_account.social_media_user_id}",
                params={
                    "access_token": db_social_account.access_token,
                    "fields": "name,id,email",
                },
            )
            response.raise_for_status()
            me = response.json()
        return success_response(200, "User details returned", me)

    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


# get all the post made from this app
@router.get("/post", response_model=FacebookPostsResponse)
async def get_all_posts_facebook(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
):
    """Get all the post made by this user from the app"""
    try:
        # Retrieve the social media account for the organisation
        logger.info('Retrieving the user/organisation social details')
        db_social_account = await get_social_details(
            organisation_id, "facebook", db_session
        )

        # Query posts using the social_media_account_id
        logger.info('social details retrieved, retrieving all post made by that social account')
        result = await db_session.execute(
            select(Post).filter_by(social_media_account_id=db_social_account.id)
        )
        '''date, content, platform, likes, comments, no comments'''
        db_posts = result.scalars().all()
        logger.info(f'All post details fetched from db, {len(db_posts)}')
        logger.info('fetching additional details about post from facebook graph')
        results = []
        for post in db_posts:
            response = await get_post_details(
                organisation_id,
                post.post_id,
                db_social_account.page_access_token,
                db_session
            )
            response['schedulecontent_id'] = post.schedulecontent_id
            results.append(response)

        return success_response(
            200, "All posts from this platform returned", results
        )
    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


@router.get("/post/{post_id}", response_model=FacebookPostItem)
async def get_single_posts_facebook(
    organisation_id: Annotated[str, Depends(verify_organization)],
    post_id: str,
    db_session: AsyncSession = Depends(get_db),
):
    """Get the details of the post made by this user from the app"""
    try:
        # Retrieve the social media account for the organisation
        logger.info('Retrieving the user/organisation social details')
        db_social_account = await get_social_details(
            organisation_id, "facebook", db_session
        )

        # Query posts using the social_media_account_id
        logger.info('social details retrieved, retrieving the post made by that social account')
        result = await db_session.execute(
            select(Post).filter_by(social_media_account_id=db_social_account.id, post_id=post_id)
        )
        db_posts = result.scalars().first()
        logger.info('fetching additional details about post from facebook graph')

        response = await get_post_details(
            organisation_id,
            db_posts.post_id,
            db_social_account.page_access_token,
            db_session
        )
        response['schedulecontent_id'] = db_posts.schedulecontent_id

        return success_response(
            200, "Post details retrieved", response
        )
    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


async def post_to_facebook(
    organisation_id: str,
    schedule_content_id: str,
    db_session: AsyncSession,
    message: str = "",
    img_urls: Optional[List[str]] = None,
):
    """Upload an image based post to a Facebook page"""
    try:
        logger.info("stating upload of content to facebook")
        db_social_account = await get_social_details(
            organisation_id, "facebook", db_session
        )

        # upload images to facebook
        uploaded_image_ids = []
        # loop through each image URL, download and upload to facebook

        if img_urls:
            logger.info("collecting images")
            for img_url in img_urls:
                img_data = await download_image_from_url(img_url)
                image_id = await upload_image_to_facebook(
                    db_social_account.page_id,
                    img_data,
                    db_social_account.page_access_token
                )
                uploaded_image_ids.append(image_id)

        # attach the uploaded images to the post
        params = {
            "message": message,
            "access_token": db_social_account.page_access_token,
        }
        # Attach the uploaded images to the post
        if uploaded_image_ids:
            for image_id in uploaded_image_ids:
                params[f"attached_media[{uploaded_image_ids.index(image_id)}]"] = f'{{"media_fbid":"{image_id}"}}'

        feed_url = f"{FACEBOOK_GRAPH_API_URL}/{db_social_account.page_id}/feed"

        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(feed_url, params=params)
            response.raise_for_status()

            post_response = response.json()
            # save to database
            await save_post_to_database(
                post_id=post_response.get('id'),
                schedule_content_id=schedule_content_id,
                social_media_account_id=db_social_account.id,
                db_session=db_session
            )
            return 'Message successfully posted'

    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        logger.error(f'An error occurred: {str(e)}')
        raise e
    except Exception as e:
        await db_session.rollback()
        logger.error(f'internal error: {str(e)}')
        raise HTTPException(status_code=500, detail='An unexpected error occurred')


# ############################CONVERSATIONS || MESSAGES #########
@router.get("/conversations", response_model=List[ConversationResponse])
async def get_conversations(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db)
):
    """get a list of your app user's conversations for an Instagram professional account"""
    try:
        db_social = await get_social_details(
            organisation_id=organisation_id,
            platform_name="facebook",
            db_session=db_session
        )
        query = (
            select(Conversation)
            .where(Conversation.social_media_account_id == db_social.id)
            .order_by(desc(Conversation.updated_time))
        )
        result = await db_session.execute(query)
        conversations = result.scalars().all()
        return conversations
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f'A server error occurred: {str(e)}')
        return fail_response(500, "An unexpected error occurred")


@router.get("/conversations/{conversation_id}/messages", response_model=GetConversationWithUserResponse)
async def get_messages_in_conversation(
    conversation_id: str,
    _: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db)
):
    """get a list of earliest twenty messages in the db and saves it in the db"""
    query = (
        select(model.Message)
        .where(model.Message.conversation_id == conversation_id)
        .order_by(model.Message.created_time.desc())
        .limit(25)
    )
    result = await db_session.execute(query)
    messages = result.scalars().all()

    return messages


@router.get('/messages', response_model=FacebookMessagesResponse)
async def get_convo(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db)
):
    """Get the conversations from the page initiated with the last 24 hours"""
    try:
        db_social_account = await get_social_details(
            organisation_id, "facebook", db_session
        )
        # make the call to get the conversations
        logger.info('Starting the call to get the conversations')
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                f"{FACEBOOK_GRAPH_API_URL}/{db_social_account.page_id}/conversations",
                params={
                    "fields": "participants{name,id},updated_time,message_count,messages{id,message,from{name,id},attachments,created_time,to{name,id}},senders{name,id}",
                    "access_token": db_social_account.page_access_token
                }
            )
            response.raise_for_status()

            conversations_response = response.json()
            conversation_data = conversations_response.get('data', [])

            enhanced_convos = []
            for convo in conversation_data:
                enhanced = {}
                enhanced['id'] = convo.get('id')

                # Add conversation metadata
                enhanced['last_updated'] = convo.get('updated_time')
                enhanced['message_count'] = convo.get('message_count', 0)

                # Extract and enhance participant details
                participants = convo.get('participants', {}).get('data', [])
                enhanced['participants'] = [
                        {
                            'name': p.get('name'),
                            'id': p.get('id')
                        }
                        for p in participants
                    ]

                # Extract and enhance senders details
                senders = convo.get('senders', {}).get('data', [])
                enhanced['senders'] = [
                        {
                            'name': s.get('name'),
                            'id': s.get('id')
                        }
                        for s in senders
                    ]

                # Extract and enhance messages with full details
                message_obj = convo.get('messages', {})
                messages = message_obj.get('data', [])
                enhanced_messages = []

                for msg in messages:
                    enhanced_msg = {
                        'id': msg.get('id'),
                        'message': msg.get('message'),
                        'created_time': msg.get('created_time')
                    }

                    # Add 'from' details with full structure
                    from_data = msg.get('from', {})
                    if from_data:
                        enhanced_msg['from'] = {
                            'name': from_data.get('name'),
                            'id': from_data.get('id')
                        }

                    # Add 'to' details with full structure
                    to_data = msg.get('to', {}).get('data', [])
                    if to_data:
                        enhanced_msg['to'] = [
                                {
                                    'name': t.get('name'),
                                    'id': t.get('id')
                                }
                                for t in to_data
                            ]

                    # Add attachments if present
                    if 'attachments' in msg:
                        enhanced_msg['attachments'] = msg['attachments']

                    enhanced_messages.append(enhanced_msg)

                enhanced['messages'] = enhanced_messages
                enhanced_convos.append(enhanced)

            # Add conversation-level pagination
            response_data = {
                'data': enhanced_convos
            }

            # Include conversation-level pagination if present
            conversation_paging = conversations_response.get('paging', {})
            if conversation_paging:
                response_data['paging'] = conversation_paging

            logger.info('Enhanced conversations data for frontend consumption')
            return success_response(status_code=200, message='conversations retrieved', data=response_data)

    except HTTPException as e:
        logger.error(f'An error occurred: {str(e)}')
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f'An unexpected error occurred: {str(e)}')
        return fail_response(500, 'an unexpected error occurred')


# TODO: find a way to use another tag apart from the RESPONSE tag
@router.post('/send_message', response_model=FacebookSendMessageResponse)
async def send_a_message_to_customer(
    organisation_id: Annotated[str, Depends(verify_organization)],
    details: SendMessageSchema,
    db_session: AsyncSession = Depends(get_db),
):
    '''
    Sends a message to a customer within the 24-hour standard messaging window.

    This endpoint supports:
    - Text messages
    - Single media attachments (image, video, audio, file)
    - Multiple image attachments (up to 30)

    This follows Facebook's Send API requirements:
    - Uses the Page ID for sending the message
    - Requires the recipient's ID
    - Uses the Page access token
    - Sets messaging_type to RESPONSE (for replies within 24h window)
    - Includes the message content or attachments

    Note: Messages can only be sent within 24 hours of the last user interaction.
    '''
    try:
        # Get credentials
        db_social_account = await get_social_details(
            organisation_id, "facebook", db_session)

        # Prepare recipient
        recipient = {"id": details.receiver_id}

        # Prepare message based on what's provided
        message = {}

        # Case 1: Text message
        if details.message and not details.attachment and not details.attachments:
            message["text"] = details.message

        # Case 2: Single attachment
        elif details.attachment:
            message["attachment"] = {
                "type": details.attachment.type,
                "payload": details.attachment.payload.dict()
            }

        # Case 3: Multiple attachments (only for images)
        elif details.attachments:
            # Validate that all attachments are images (Facebook limitation)
            if not all(att.type == "image" for att in details.attachments):
                return fail_response(400, "Multiple attachments are only supported for images")

            # Validate maximum number of attachments
            if len(details.attachments) > 30:
                return fail_response(400, "Maximum of 30 images can be sent at once")

            message["attachments"] = [
                {
                    "type": att.type,
                    "payload": att.payload.dict()
                }
                for att in details.attachments
            ]

        # If no valid message content was provided
        else:
            return fail_response(400, "Either message text or attachment(s) must be provided")

        # Prepare request payload
        payload = {
            "recipient": recipient,
            "messaging_type": "MESSAGE_TAG",  # For messages within 24h window
            "message": message,
            "tag": "POST_PURCHASE_UPDATE"
        }

        # Send the message
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(
                f"{FACEBOOK_GRAPH_API_URL}/{db_social_account.page_id}/messages",
                json=payload,
                params={"access_token": db_social_account.page_access_token}
            )
            response.raise_for_status()
            message_data = response.json()
            logger.info(f'Successfully sent a message: {message_data}')

        # Transform response to match our schema
        response_data = {
            "recipient_id": message_data.get("recipient_id"),
            "message_id": message_data.get("message_id")
        }

        return success_response(200, 'Message sent successfully', response_data)
    except httpx.HTTPStatusError as e:
        logger.error(f'Facebook API error: {str(e.response.json())}')
        return fail_response(e.response.status_code, f"Facebook API error: {e.response.json().get('error', {}).get('message', 'An unexpected error occurred')}")
    except HTTPException as e:
        logger.error(f'HTTP exception: {str(e)}')
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f'An unexpected error occurred: {str(e)}')
        return fail_response(500, "An unexpected error occurred")


# ################### COMMENTS ###########################
@router.post("/{scheduled_content_id}/comment", response_model=FacebookCommentResponse)
async def create_comment(
    organisation_id: Annotated[str, Depends(verify_organization)],
    message: Message,
    scheduled_content_id: str,
    db_session: AsyncSession = Depends(get_db),
):
    """Comment on a post"""
    try:
        # validate the social details
        logger.info('commenting on a post..|| fetching the social details')
        db_social = await get_social_details(
            organisation_id, "facebook", db_session)
        # get the required post id
        result = await db_session.execute(
            select(Post).where(
                Post.schedulecontent_id == scheduled_content_id,
                Post.social_media_account_id == db_social.id
            )
        )
        post = result.scalars().first()
        if not post:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail='Requested content not found'
            )
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(
                f"{FACEBOOK_GRAPH_API_URL}/{post.post_id}/comments",
                data={
                    "access_token": db_social.page_access_token,
                    "message": message.message,
                },
            )
            logger.info('comment added to facebook, adding to the database now')
            # i need webhooks here to help save to the DB
            response.raise_for_status()
            comment_response = response.json()

            return success_response(
                200, "Comment sent successfully", comment_response)
    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


@router.get("/{scheduled_content_id}/comment", response_model=CommentResponse)
async def get_comments(
    _: Annotated[str, Depends(verify_organization)],
    scheduled_content_id: str,
    limit: int = Query(10, ge=1, le=100),
    offset: int = Query(0, ge=0),
    db_session: AsyncSession = Depends(get_db),
):
    """Get all the comments on a post"""
    try:
        # get the postId for the schedule content
        result = await db_session.execute(
            select(Post)
            .where(
                Post.schedulecontent_id == scheduled_content_id
            )
        )
        db_post = result.scalars().first()
        if not db_post:
            raise HTTPException(
                status_code=400,
                detail="Requested content not found"
            )
        # get the comments
        # Get total count for pagination metadata
        total = await db_session.scalar(
            select(func.count()).where(
                Comment.post_id == db_post.post_id,
                Comment.parent_id.is_(None),
                or_(
                    Comment.extra_data['hidden'].as_boolean() is not False,
                    Comment.extra_data['hidden'] is None
                )
            )
        )
        result = await db_session.execute(
            select(Comment)
            .where(
                Comment.post_id == db_post.post_id,
                Comment.parent_id.is_(None),
                or_(
                    Comment.extra_data['hidden'].as_boolean() is not False,
                    Comment.extra_data['hidden'] is None
                )
            )
            .order_by(Comment.created_time.desc())
            .offset(offset)
            .limit(limit)
        )
        comments = result.scalars().all()
        return {
            "total": total,
            "limit": limit,
            "offset": offset,
            "comments": comments
            }

    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


@router.post("/comment", response_model=IGCommentResponse)
async def reply_to_comment(
    message: CommentReply,
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
):
    """
    Reply to a comment on a specific Facebook post.
    Parameters:
    - comment_id (str): The ID of the comment to reply to.
    - reply_text (str): The text of the reply.
    - organisation_id (str): The organisation ID to fetch the corresponding page token.

    Returns:
    - success (bool): Whether the reply was successfully posted.
    - message (str): Success or error message.
    """
    try:
        # Fetch the Facebook page details for the organisation
        logger.info('replying to a comment')
        db_social_account = await get_social_details(
            organisation_id, "facebook", db_session
        )
        page_access_token = db_social_account.page_access_token

        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(
                f"{FACEBOOK_GRAPH_API_URL}/{message.comment_id}/comments",
                params={
                    "message": message.message,
                    "access_token": page_access_token,
                },
            )
            response.raise_for_status()
            reply_data = response.json()

        return success_response(200, "Reply posted successfully", reply_data)
    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


async def update_a_post(
    organisation_id: Annotated[str, Depends(verify_organization)],
    message: Message,
    page_post_id: str,
    db_session: AsyncSession = Depends(get_db),
):
    """Update a page post"""
    try:
        # validate the social details
        db_social = await get_social_details(organisation_id, "facebook", db_session)

        result = await db_session.execute(select(Post).filter_by(post_id=page_post_id))
        db_post = result.scalars().first()
        if not db_post:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Requested post not found"
            )
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(
                f"{FACEBOOK_GRAPH_API_URL}/{page_post_id}",
                data={
                    "access_token": db_social.page_access_token,
                    "message": message.message,
                },
            )
            response.raise_for_status()
            post_response = response.json()
            logger.info(post_response)

            # log to the database
            if not post_response:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to update post",
                )
            db_post.content = message.message

            await db_session.commit()
            await db_session.refresh(db_post)
            return success_response(200, "Post updated successfully", db_post)
    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


@router.put("/comment/{comment_id}")
async def update_a_comment(
    organisation_id: Annotated[str, Depends(verify_organization)],
    message: CommentReply,
    db_session: AsyncSession = Depends(get_db),
):
    """Update a comment message"""
    try:
        # get the social account details
        logger.info('updating a comment')
        db_social_account = await get_social_details(
            organisation_id, "facebook", db_session
        )
        logger.info('starting the httpx call')
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(
                f"{FACEBOOK_GRAPH_API_URL}/{message.comment_id}",
                data={
                    "access_token": db_social_account.page_access_token,
                    "message": message.message,
                },
            )
            response.raise_for_status()
            comment_response = response.json()
            if not comment_response:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Comment update failed",
                )
            return success_response(
                200, "Comment updated successfully", comment_response)
    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


@router.delete("/comment/{comment_id}")
async def delete_comment(
    organisation_id: Annotated[str, Depends(verify_organization)],
    comment_id: str,
    db_session: AsyncSession = Depends(get_db),
):
    """Delete a comment from a Facebook post"""
    try:
        logger.info('starting to delete a comment')
        db_social_account = await get_social_details(
            organisation_id, "facebook", db_session
        )
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.delete(
                f"{FACEBOOK_GRAPH_API_URL}/{comment_id}",
                params={"access_token": db_social_account.page_access_token},
            )
            response.raise_for_status()
            comment_response = response.json()
            if not comment_response:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Comment update failed",
                )
        return (200, "Comment deleted successfully")
    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")

# #####################
# PAGE METRICS
# #####################


# Get a single metric
@router.get("/overview")
async def get_overview(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
):
    """Get an overview of a Facebook page"""
    try:
        # Try to get data from Redis cache first
        cache_key = f"fb_overview_{organisation_id}"
        cached_data = redis_client.get(cache_key)

        if cached_data:
            # Data found in cache - use it
            logger.info("Returning Facebook overview data from cache")
            return success_response(
                200,
                "Overview retrieved successfully from cache",
                json.loads(cached_data)
            )

        # If not in cache, try to get from database
        result = await db_session.execute(
            select(model.FacebookPageMetrics)
            .filter(model.FacebookPageMetrics.organisation_id == organisation_id)
            .order_by(model.FacebookPageMetrics.collected_at.desc())
            .limit(1)
        )
        db_metrics = result.scalars().first()

        # If we have recent database data, use it and update cache
        if db_metrics and (datetime.now() - db_metrics.collected_at).days < 1:  # Check if data is fresh enough
            logger.info("Returning Facebook overview data from database")
            overview_data = {
                "total_followers": db_metrics.total_followers,
                "total_impressions": db_metrics.total_impressions,
                "total_reach": db_metrics.total_reach,
                "total_engagements": db_metrics.total_engagements,
                "engagement_rate": db_metrics.engagement_rate,
                "updated_at": db_metrics.collected_at.isoformat()
            }

            # Update cache with database data
            redis_client.setex(
                cache_key, redis_api_expiry_time, json.dumps(overview_data))

            return success_response(
                200,
                "Overview retrieved successfully from database",
                overview_data
            )

        # If not in cache or database, fetch fresh data
        db_social_account = await get_social_details(
            organisation_id, "facebook", db_session
        )

        logger.info("Fetching fresh data from Facebook API")
        metrics_data = await get_facebook_page_metrics(
            page_id=db_social_account.page_id,
            access_token=db_social_account.page_access_token,
            period="day",
            days=30
        )

        # Create overview data
        overview_data = {
            "total_followers": metrics_data.get('followers', 0),
            "total_impressions": metrics_data.get('impressions', 0),
            "total_reach": metrics_data.get('reach', 0),
            "total_engagements": metrics_data.get('engagements', 0),
            "engagement_rate": round(metrics_data.get('engagement_rate', 0), 2),
            "updated_at": datetime.now().isoformat()
        }

        # Save to Redis cache
        logger.info("Saving to Redis cache")
        redis_client.setex(cache_key, redis_api_expiry_time, json.dumps(overview_data))

        # Update or create database record
        if db_metrics:
            logger.info("Updating existing database record")
            db_metrics.total_followers = metrics_data.get('followers', 0)
            db_metrics.total_impressions = metrics_data.get('impressions', 0)
            db_metrics.total_reach = metrics_data.get('reach', 0)
            db_metrics.total_engagements = metrics_data.get('engagements', 0)
            db_metrics.engagement_rate = round(metrics_data.get('engagement_rate', 0), 2)
            db_metrics.collected_at = datetime.now()
        else:
            logger.info("Creating new database record")
            new_metrics = model.FacebookPageMetrics(
                organisation_id=organisation_id,
                page_id=db_social_account.page_id,
                total_followers=metrics_data.get('followers', 0),
                total_impressions=metrics_data.get('impressions', 0),
                total_reach=metrics_data.get('reach', 0),
                total_engagements=metrics_data.get('engagements', 0),
                engagement_rate=round(metrics_data.get('engagement_rate', 0), 2)
            )
            db_session.add(new_metrics)

        await db_session.commit()

        return success_response(
            200,
            "Overview retrieved successfully from Facebook API",
            overview_data,
        )
    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


@router.get("/{post_id}/get_post_metric")
async def get_post_metric(
    organisation_id: Annotated[str, Depends(verify_organization)],
    post_id: str,
    db_session: AsyncSession = Depends(get_db),
):
    """
    ## Retrieve metrics for a particular post of a specific page.
    Metrics include:
    - Post Impressions
    - Engagements
    - Click Rate
    - Reach
    - Likes
    - Comments
    - Content Type
    """
    try:
        db_social_account = await get_social_details(
            organisation_id, "facebook", db_session
        )

        # Confirm the post is available in the database
        result = await db_session.execute(select(Post).filter_by(post_id=post_id))
        db_post = result.scalars().first()
        if not db_post:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Requested post not found",
            )
        # Check if data is in the cache
        cache_key = f"fb_get_post_metric{organisation_id}_{post_id}"
        cache_data = redis_client.get(cache_key)
        if cache_data:
            logger.info("Data is available in the cache")
            return success_response(
                200, "Post metrics retrieved successfully", json.loads(cache_data)
            )

        results = []
        async with httpx.AsyncClient(timeout=timeout) as client:
            # Fetch insights for the post
            insights_response = await client.get(
                f"{FACEBOOK_GRAPH_API_URL}/{post_id}/insights",
                params={
                    "access_token": db_social_account.page_access_token,
                    "metric": "post_impressions,post_clicks,post_reactions_like_total,post_impressions_unique",
                },
            )
            insights_response.raise_for_status()
            insights_data = {
                item["name"]: item["values"][0]["value"]
                for item in insights_response.json().get("data", [])
            }

            # Fetch comments count separately
            comments_response = await client.get(
                f"{FACEBOOK_GRAPH_API_URL}/{post_id}/comments",
                params={
                    "access_token": db_social_account.page_access_token,
                    "summary": "true",
                },
            )
            comments_response.raise_for_status()
            comments_data = comments_response.json()
            total_comments = comments_data.get("summary", {}).get("total_count", 0)

            # Aggregate metrics
            post_metrics = {
                "post_id": post_id,
                "message": db_post.content,
                "created_time": db_post.created_at,
                "impressions": insights_data.get("post_impressions", 0),
                "clicks": insights_data.get("post_clicks", 0),
                "likes": insights_data.get("post_reactions_like_total", 0),
                "reach": insights_data.get("post_impressions_unique", 0),
                "comments": total_comments,
                "engagements": insights_data.get("post_clicks", 0)
                + insights_data.get("post_reactions_like_total", 0)
                + total_comments,
                "click_rate": round(
                    (
                        insights_data.get("post_clicks", 0)
                        / insights_data.get("post_impressions", 1)
                    )
                    * 100,
                    2,
                )
                if insights_data.get("post_impressions", 0) > 0
                else 0.0,
            }

            results.append(post_metrics)

        # Save to the cache
        logger.info("Saving to Redis cache")
        redis_client.setex(cache_key, redis_api_expiry_time, json.dumps(results))

        return success_response(200, "Post metrics retrieved successfully", results)

    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


@router.get("/get_full_metrics")
async def get_metrics(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
):
    """get metrics for all post in database"""
    try:
        db_social_account = await get_social_details(
            organisation_id, "facebook", db_session
        )

        # Confirm posts are available in the database
        result = await db_session.execute(
            select(Post).filter_by(social_media_account_id=db_social_account.id)
        )
        db_posts = result.scalars().all()
        if not db_posts:
            return fail_response(404, "No posts found for this account")

        # Check if data is in the cache
        cache_key = f"fb_full_post_metrics_{organisation_id}"
        cache_data = redis_client.get(cache_key)
        if cache_data:
            logger.info("Data is available in the cache")
            return success_response(
                200, "Metrics retrieved successfully", json.loads(cache_data)
            )

        post_metrics = []
        async with httpx.AsyncClient(timeout=timeout) as client:
            for db_post in db_posts:
                post_id = db_post.post_id
                post_message = db_post.content
                try:
                    post_insights_response = await client.get(
                        f"{FACEBOOK_GRAPH_API_URL}/{post_id}/insights",
                        params={
                            "access_token": db_social_account.page_access_token,
                            "metric": "post_impressions,post_engagements,post_clicks,post_reactions_like_total,post_comments,post_impressions_unique",
                        },
                    )
                    post_insights_response.raise_for_status()
                    post_insights = post_insights_response.json().get("data", [])
                    metrics = {
                        "post_id": post_id,
                        "content": post_message,
                        "impressions": next(
                            (
                                item["values"][0]["value"]
                                for item in post_insights
                                if item["name"] == "post_impressions"
                            ),
                            0,
                        ),
                        "engagements": next(
                            (
                                item["values"][0]["value"]
                                for item in post_insights
                                if item["name"] == "post_engagements"
                            ),
                            0,
                        ),
                        "click_rate": next(
                            (
                                item["values"][0]["value"]
                                for item in post_insights
                                if item["name"] == "post_clicks"
                            ),
                            0,
                        ),
                        "likes": next(
                            (
                                item["values"][0]["value"]
                                for item in post_insights
                                if item["name"] == "post_reactions_like_total"
                            ),
                            0,
                        ),
                        "comments": next(
                            (
                                item["values"][0]["value"]
                                for item in post_insights
                                if item["name"] == "post_comments"
                            ),
                            0,
                        ),
                    }
                    post_metrics.append(metrics)
                except httpx.HTTPStatusError as e:
                    logger.error(
                        f"Failed to fetch insights for post {post_id}: {str(e)}"
                    )
                    continue  # Skip this post if metrics fail

        # Save to the cache
        logger.info("Saving to Redis cache")
        redis_client.setex(cache_key, redis_api_expiry_time, json.dumps(post_metrics))
        return success_response(200, "Metrics retrieved successfully", post_metrics)

    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


@router.get("/post_impressions_growth_trend")
async def get_post_impressions_growth_trend(
    organisation_id: Annotated[str, Depends(verify_organization)],
    months: int = 6,  # Default to last 6 months
    db_session: AsyncSession = Depends(get_db),
):
    """
    Get Post Impressions Growth Trend (Monthly).

    Parameters:
    - organisation_id (str): Organisation ID to fetch the corresponding page token.
    - months (int): Number of months to include in the trend (default: 6 months).

    Returns:
    - A JSON response with the monthly growth trend.
    """
    try:
        logger.info(
            f"Fetching post impressions growth trend for organisation: {organisation_id}"
        )
        db_social_account = await get_social_details(
            organisation_id, "facebook", db_session
        )
        page_access_token = db_social_account.page_access_token

        # Check if data is in the cache
        cache_key = f"fb_post_impressions_growth_trend_{organisation_id}_{months}"
        cache_data = redis_client.get(cache_key)
        if cache_data:
            logger.info("Data is available in the cache")
            return success_response(
                200,
                "Post Impressions Growth Trend retrieved successfully",
                json.loads(cache_data),
            )

        # Use naive datetime for consistency
        end_date = datetime.now()
        start_date = end_date - relativedelta(months=months)

        trend_data = []
        async with httpx.AsyncClient(timeout=timeout) as client:
            while start_date < end_date:
                chunk_end_date = start_date + relativedelta(months=1)
                if chunk_end_date > end_date:
                    chunk_end_date = end_date

                since = start_date.strftime("%Y-%m-%d")
                until = chunk_end_date.strftime("%Y-%m-%d")

                response = await client.get(
                    f"{FACEBOOK_GRAPH_API_URL}/{db_social_account.page_id}/insights",
                    params={
                        "metric": "post_impressions",
                        "period": "week",
                        "since": since,
                        "until": until,
                        "access_token": page_access_token,
                    },
                )
                response.raise_for_status()
                impressions_data = response.json()

                if impressions_data.get("data") and len(impressions_data["data"]) > 0:
                    total_impressions = sum(
                        value.get("value", 0)
                        for value in impressions_data["data"][0].get("values", [])
                    )
                else:
                    total_impressions = 0

                trend_data.append(
                    {
                        "month": start_date.strftime("%Y-%m"),
                        "impressions": total_impressions,
                    }
                )

                start_date = chunk_end_date

        growth_trend = []
        for i in range(1, len(trend_data)):
            current = trend_data[i]
            previous = trend_data[i - 1]
            if previous["impressions"] == 0:
                growth = 100.0 if current["impressions"] > 0 else None
            else:
                growth = (
                    (current["impressions"] - previous["impressions"])
                    / previous["impressions"]
                ) * 100
            growth_trend.append(
                {
                    "month": current["month"],
                    "impressions": current["impressions"],
                    "growth": growth,
                }
            )

        # Save to the cache
        logger.info("Saving to Redis cache")
        redis_client.setex(cache_key, redis_long_expiry_time, json.dumps(growth_trend))

        logger.info("Post Impressions Growth Trend retrieved successfully")
        return success_response(
            200, "Post Impressions Growth Trend retrieved successfully", growth_trend
        )
    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


@router.get("/audience_growth_trend")
async def get_audience_growth_trend(
    organisation_id: Annotated[str, Depends(verify_organization)],
    months: int = 6,  # Default to last 6 months
    db_session: AsyncSession = Depends(get_db),
):
    """
    Get General Audience Growth Trend (Monthly).

    Parameters:
    - organisation_id (str): Organisation ID to fetch the corresponding page token.
    - months (int): Number of months to include in the trend (default: 6 months).

    Returns:
    - A JSON response with the audience growth trend.
    """
    try:
        logger.info(
            f"Fetching audience growth trend for organisation: {organisation_id}"
        )
        db_social_account = await get_social_details(
            organisation_id, "facebook", db_session
        )

        # Check if data is in the Redis cache
        cache_key = f"fb_audience_growth_trend_{organisation_id}_{months}"
        cache_data = redis_client.get(cache_key)
        if cache_data:
            logger.info("Data is available in the Redis cache")
            return success_response(
                200,
                "Audience Growth Trend retrieved successfully",
                json.loads(cache_data),
            )

        # Check if data is in the database
        result = await db_session.execute(
            select(model.FacebookGrowthTrend)
            .filter(
                model.FacebookGrowthTrend.organisation_id == organisation_id,
                model.FacebookGrowthTrend.trend_type == "audience"
            )
            .order_by(model.FacebookGrowthTrend.month.desc())
            .limit(months)
        )
        db_growth_trends = result.scalars().all()

        if db_growth_trends and len(db_growth_trends) > 0:
            logger.info("Data is available in the database")
            # Create growth trend data from database
            growth_trend = []
            for trend in sorted(db_growth_trends, key=lambda x: x.month):
                trend_data = {
                    "month": trend.month,
                    "fans": trend.value,
                    "growth": trend.growth_percentage
                }
                growth_trend.append(trend_data)

            # Save to Redis cache
            logger.info("Saving to Redis cache")
            redis_client.setex(cache_key, redis_long_expiry_time, json.dumps(growth_trend))

            return success_response(
                200,
                "Audience Growth Trend retrieved successfully",
                growth_trend,
            )

        # If data is not in Redis or database, fetch from Facebook API
        logger.info("Data not available in cache or database, fetching from Facebook API")
        page_access_token = db_social_account.page_access_token

        # Use timezone-aware datetime for API calls but convert to naive for database
        end_date = datetime.now()
        start_date = end_date - relativedelta(months=months)

        trend_data = []
        async with httpx.AsyncClient(timeout=timeout) as client:
            while start_date < end_date:
                # Define current chunk's start and end dates
                chunk_end_date = start_date + relativedelta(months=1)
                if chunk_end_date > end_date:
                    chunk_end_date = end_date

                # Convert dates to strings
                since = start_date.strftime("%Y-%m-%d")
                until = chunk_end_date.strftime("%Y-%m-%d")

                # Fetch data for the current month
                response = await client.get(
                    f"{FACEBOOK_GRAPH_API_URL}/{db_social_account.page_id}/insights",
                    params={
                        "metric": "page_daily_follows",
                        "period": "day",
                        "since": since,
                        "until": until,
                        "access_token": page_access_token,
                    },
                )
                response.raise_for_status()
                audience_data = response.json()

                # Check and process the response
                if audience_data.get("data") and len(audience_data["data"]) > 0:
                    total_fans = sum(
                        value.get("value", 0)
                        for value in audience_data["data"][0].get("values", [])
                    )
                else:
                    total_fans = 0  # Default to 0 if no data available

                trend_data.append(
                    {"month": start_date.strftime("%Y-%m"), "fans": total_fans}
                )

                # Create and store growth trend record in database
                growth_trend_record = model.FacebookGrowthTrend(
                    organisation_id=organisation_id,
                    page_id=db_social_account.page_id,
                    trend_type="audience",
                    month=start_date.strftime("%Y-%m"),
                    value=total_fans
                )
                db_session.add(growth_trend_record)

                # Move to the next chunk
                start_date = chunk_end_date

        # Calculate growth percentages
        growth_trend = []
        for i in range(1, len(trend_data)):
            current = trend_data[i]
            previous = trend_data[i - 1]
            if previous["fans"] == 0:
                growth = 100.0 if current["fans"] > 0 else None
            else:
                growth = ((current["fans"] - previous["fans"]) / previous["fans"]) * 100
            growth_trend.append(
                {"month": current["month"], "fans": current["fans"], "growth": growth}
            )

            # Update growth percentage in database
            result = await db_session.execute(
                select(model.FacebookGrowthTrend)
                .filter(
                    model.FacebookGrowthTrend.organisation_id == organisation_id,
                    model.FacebookGrowthTrend.trend_type == "audience",
                    model.FacebookGrowthTrend.month == current["month"]
                )
            )
            db_trend = result.scalars().first()
            if db_trend:
                db_trend.growth_percentage = growth

        # Commit database changes
        await db_session.commit()

        # Save to the cache
        logger.info("Saving to Redis cache")
        redis_client.setex(cache_key, redis_long_expiry_time, json.dumps(growth_trend))

        logger.info("Audience Growth Trend retrieved successfully")
        return success_response(
            200, "Audience Growth Trend retrieved successfully", growth_trend
        )
    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


@router.get("/post_engagement_growth_trend")
async def get_post_engagement_growth_trend(
    organisation_id: Annotated[str, Depends(verify_organization)],
    months: int = 6,  # Default to last 6 months
    db_session: AsyncSession = Depends(get_db),
):
    """
    Get Post Engagement Growth Trend (Monthly).

    Parameters:
    - organisation_id (str): Organisation ID to fetch the corresponding page token.
    - months (int): Number of months to include in the trend (default: 6 months).

    Returns:
    - A JSON response with the monthly growth trend.
    """
    try:
        logger.info(
            f"Fetching post engagement growth trend for organisation: {organisation_id}"
        )
        social_account = await get_social_details(
            organisation_id, "facebook", db_session
        )
        access_token = social_account.page_access_token

        # Check if data is in the cache
        cache_key = f"fb_post_engagement_growth_trend_{organisation_id}_{months}"
        cache_data = redis_client.get(cache_key)
        if cache_data:
            logger.info("Data is available in the cache")
            return success_response(
                200,
                "Post Engagement Growth Trend retrieved successfully",
                json.loads(cache_data),
            )

        # Use naive datetime for consistency
        current_date = datetime.now()
        start_date = current_date - relativedelta(months=months)

        engagement_trend_data = []
        async with httpx.AsyncClient(timeout=timeout) as client:
            logger.info("Starting to fetch engagement data from Facebook API")
            while start_date < current_date:
                # Define current chunk's start and end dates
                chunk_end_date = start_date + relativedelta(months=1)
                if chunk_end_date > current_date:
                    chunk_end_date = current_date

                # Convert dates to strings
                since_date = start_date.strftime("%Y-%m-%d")
                until_date = chunk_end_date.strftime("%Y-%m-%d")

                # Fetch data for the current month
                response = await client.get(
                    f"{FACEBOOK_GRAPH_API_URL}/{social_account.page_id}/insights",
                    params={
                        "metric": "page_post_engagements",
                        "period": "week",
                        "since": since_date,
                        "until": until_date,
                        "access_token": access_token,
                    },
                )
                response.raise_for_status()
                engagement_data = response.json()

                # Check and process the response
                if engagement_data.get("data") and len(engagement_data["data"]) > 0:
                    total_engagements = sum(
                        value.get("value", 0)
                        for value in engagement_data["data"][0].get("values", [])
                    )
                else:
                    total_engagements = 0  # Default to 0 if no data available

                engagement_trend_data.append(
                    {
                        "month": start_date.strftime("%Y-%m"),
                        "engagements": total_engagements,
                    }
                )

                # Move to the next chunk
                start_date = chunk_end_date

        # Calculate growth percentages
        engagement_growth_trend = []
        for i in range(1, len(engagement_trend_data)):
            current_month_data = engagement_trend_data[i]
            previous_month_data = engagement_trend_data[i - 1]
            if previous_month_data["engagements"] == 0:
                growth_percentage = (
                    100.0 if current_month_data["engagements"] > 0 else None
                )
            else:
                growth_percentage = (
                    (
                        current_month_data["engagements"]
                        - previous_month_data["engagements"]
                    )
                    / previous_month_data["engagements"]
                ) * 100
            engagement_growth_trend.append(
                {
                    "month": current_month_data["month"],
                    "engagements": current_month_data["engagements"],
                    "growth": growth_percentage,
                }
            )

        # Save to the cache
        logger.info("Saving to Redis cache")
        redis_client.setex(
            cache_key, redis_long_expiry_time, json.dumps(engagement_growth_trend)
        )

        logger.info("Post Engagement Growth Trend retrieved successfully")
        return success_response(
            200,
            "Post Engagement Growth Trend retrieved successfully",
            engagement_growth_trend,
        )
    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


@router.get("/post_click_rate_growth_trend")
async def get_post_click_rate_growth_trend(
    organisation_id: Annotated[str, Depends(verify_organization)],
    months: int = 6,  # Default to last 6 months
    db_session: AsyncSession = Depends(get_db),
):
    """
    Get Post Click Rate Growth Trend (Monthly).

    Parameters:
    - organisation_id (str): Organisation ID to fetch the corresponding page token.
    - months (int): Number of months to include in the trend (default: 6 months).

    Returns:
    - A JSON response with the monthly growth trend.
    """
    try:
        # Fetch the Facebook page details for the organisation
        social_account = await get_social_details(
            organisation_id, "facebook", db_session
        )
        access_token = social_account.page_access_token

        # Check if data is in the cache
        cache_key = f"fb_post_click_rate_growth_trend_{organisation_id}_{months}"
        cache_data = redis_client.get(cache_key)
        if cache_data:
            logger.info("Data is available in the cache")
            return success_response(
                200,
                "Post Click Rate Growth Trend retrieved successfully",
                json.loads(cache_data),
            )

        # Use naive datetime for consistency
        end_date = datetime.now()
        start_date = end_date - relativedelta(months=months)

        trend_data = []
        async with httpx.AsyncClient(timeout=timeout) as client:
            while start_date < end_date:
                # Define current chunk's start and end dates
                chunk_end_date = start_date + relativedelta(months=1)
                if chunk_end_date > end_date:
                    chunk_end_date = end_date

                # Convert dates to strings
                since = start_date.strftime("%Y-%m-%d")
                until = chunk_end_date.strftime("%Y-%m-%d")

                # Fetch data for the current month
                response = await client.get(
                    f"{FACEBOOK_GRAPH_API_URL}/{social_account.page_id}/insights",
                    params={
                        "metric": "post_clicks",
                        "period": "week",
                        "since": since,
                        "until": until,
                        "access_token": access_token,
                    },
                )
                response.raise_for_status()
                click_data = response.json()

                # Check and process the response
                if click_data.get("data") and len(click_data["data"]) > 0:
                    total_clicks = sum(
                        value.get("value", 0)
                        for value in click_data["data"][0].get("values", [])
                    )
                else:
                    total_clicks = 0  # Default to 0 if no data available

                trend_data.append(
                    {
                        "month": start_date.strftime("%Y-%m"),
                        "clicks": total_clicks,
                    }
                )

                # Move to the next chunk
                start_date = chunk_end_date

        # Calculate growth percentages
        growth_trend = []
        for i in range(1, len(trend_data)):
            current = trend_data[i]
            previous = trend_data[i - 1]
            if previous["clicks"] == 0:
                growth = 100.0 if current["clicks"] > 0 else None
            else:
                growth = (
                    (current["clicks"] - previous["clicks"]) / previous["clicks"]
                ) * 100
            growth_trend.append(
                {
                    "month": current["month"],
                    "clicks": current["clicks"],
                    "growth": growth,
                }
            )

        # Save to the cache
        logger.info("Saving to Redis cache")
        redis_client.setex(cache_key, redis_long_expiry_time, json.dumps(growth_trend))

        return success_response(
            200, "Post Click Rate Growth Trend retrieved successfully", growth_trend
        )

    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


@router.get("/post_reach_growth_trend")
async def get_post_reach_growth_trend(
    organisation_id: Annotated[str, Depends(verify_organization)],
    months: int = 6,  # Default to last 6 months
    db_session: AsyncSession = Depends(get_db),
):
    """
    Get Post Reach Growth Trend (Monthly).

    Parameters:
    - organisation_id (str): Organisation ID to fetch the corresponding page token.
    - months (int): Number of months to include in the trend (default: 6 months).

    Returns:
    - A JSON response with the monthly growth trend.
    """
    try:
        # Fetch the Facebook page details for the organisation
        social_account = await get_social_details(
            organisation_id, "facebook", db_session
        )
        access_token = social_account.page_access_token

        # Check if data is in the cache
        cache_key = f"fb_post_reach_growth_trend_{organisation_id}_{months}"
        cache_data = redis_client.get(cache_key)
        if cache_data:
            logger.info("Data is available in the cache")
            return success_response(
                200,
                "Post Reach Growth Trend retrieved successfully",
                json.loads(cache_data),
            )

        # Use naive datetime for consistency
        current_date = datetime.now()
        start_date = current_date - relativedelta(months=months)

        reach_trend_data = []
        async with httpx.AsyncClient(timeout=timeout) as client:
            while start_date < current_date:
                # Define current chunk's start and end dates
                chunk_end_date = start_date + relativedelta(months=1)
                if chunk_end_date > current_date:
                    chunk_end_date = current_date

                # Convert dates to strings
                since_date = start_date.strftime("%Y-%m-%d")
                until_date = chunk_end_date.strftime("%Y-%m-%d")

                # Fetch data for the current month
                response = await client.get(
                    f"{FACEBOOK_GRAPH_API_URL}/{social_account.page_id}/insights",
                    params={
                        "metric": "page_impressions_unique",
                        "period": "week",
                        "since": since_date,
                        "until": until_date,
                        "access_token": access_token,
                    },
                )
                response.raise_for_status()
                reach_data = response.json()

                # Check and process the response
                if reach_data.get("data") and len(reach_data["data"]) > 0:
                    total_reach = sum(
                        value.get("value", 0)
                        for value in reach_data["data"][0].get("values", [])
                    )
                else:
                    total_reach = 0  # Default to 0 if no data available

                reach_trend_data.append(
                    {
                        "month": start_date.strftime("%Y-%m"),
                        "reach": total_reach,
                    }
                )

                # Move to the next chunk
                start_date = chunk_end_date

        # Calculate growth percentages
        reach_growth_trend = []
        for i in range(1, len(reach_trend_data)):
            current_month_data = reach_trend_data[i]
            previous_month_data = reach_trend_data[i - 1]
            if previous_month_data["reach"] == 0:
                growth_percentage = 100.0 if current_month_data["reach"] > 0 else None
            else:
                growth_percentage = (
                    (current_month_data["reach"] - previous_month_data["reach"])
                    / previous_month_data["reach"]
                ) * 100
            reach_growth_trend.append(
                {
                    "month": current_month_data["month"],
                    "reach": current_month_data["reach"],
                    "growth": growth_percentage,
                }
            )

        # Save to the cache
        logger.info("Saving to Redis cache")
        redis_client.setex(cache_key, redis_long_expiry_time, json.dumps(reach_growth_trend))

        return success_response(
            200, "Post Reach Growth Trend retrieved successfully", reach_growth_trend
        )

    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


@router.get("/audience_demographics")
async def get_audience_demographics(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
):
    """
    Get Audience Demographics (Top 10 Countries and Cities).
    """
    try:
        # Fetch the Facebook page details for the organisation
        social_account = await get_social_details(
            organisation_id, "facebook", db_session
        )
        access_token = social_account.page_access_token

        # Check if data is in the Redis cache
        cache_key = f"fb_audience_demographics_{organisation_id}"
        cache_data = redis_client.get(cache_key)
        if cache_data:
            logger.info("Data is available in the Redis cache")
            return success_response(
                200,
                "Audience demographics retrieved successfully",
                json.loads(cache_data),
            )

        # Check if data is in the database
        result = await db_session.execute(
            select(model.FacebookAudienceDemographics)
            .filter(model.FacebookAudienceDemographics.organisation_id == organisation_id)
            .order_by(model.FacebookAudienceDemographics.collected_at.desc())
            .limit(1)
        )
        db_demographics = result.scalars().first()

        if db_demographics:
            logger.info("Data is available in the database")
            # Create demographics data from database
            demographics_data = {
                "top_countries": db_demographics.get_top_countries(),
                "top_cities": db_demographics.get_top_cities(),
                "locales": db_demographics.get_locales(),
                "updated_at": db_demographics.collected_at.isoformat() if db_demographics.collected_at else None
            }

            # Save to Redis cache
            logger.info("Saving to Redis cache")
            redis_client.setex(cache_key, redis_api_expiry_time, json.dumps(demographics_data))

            return success_response(
                200,
                "Audience demographics retrieved successfully",
                demographics_data,
            )

        # If data is not in Redis or database, fetch from Facebook API
        logger.info("Data not available in cache or database, fetching from Facebook API")
        async with httpx.AsyncClient(timeout=timeout) as client:
            # Fetch audience demographics
            response = await client.get(
                f"{FACEBOOK_GRAPH_API_URL}/{social_account.page_id}/insights",
                params={
                    "metric": "page_fans_country,page_fans_city,page_fans_locale",
                    "access_token": access_token,
                },
            )

            response.raise_for_status()
            audience_data = response.json()

            # Process audience data
            if not audience_data.get("data"):
                return success_response(
                    200,
                    "No audience demographic data available for this page.",
                    {
                        "top_countries": [],
                        "top_cities": [],
                        "locales": {},
                    },
                )

            # Extract and sort country data
            country_data = (
                audience_data["data"][0].get("values", [{}])[0].get("value", {})
            )
            top_countries = sorted(
                country_data.items(), key=lambda item: item[1], reverse=True
            )[:10]

            # Extract and sort city data
            city_data = audience_data["data"][1].get("values", [{}])[0].get("value", {})
            top_cities = sorted(
                city_data.items(), key=lambda item: item[1], reverse=True
            )[:10]

            # Extract locale demographics
            locale_data = (
                audience_data["data"][2].get("values", [{}])[0].get("value", {})
            )

            # Create demographics data
            demographics_data = {
                "top_countries": top_countries,
                "top_cities": top_cities,
                "locales": locale_data,
                "updated_at": datetime.now().isoformat()
            }

            # Save to Redis cache
            logger.info("Saving to Redis cache")
            redis_client.setex(cache_key, redis_api_expiry_time, json.dumps(demographics_data))

            # Create a new demographics record in the database
            new_demographics = model.FacebookAudienceDemographics(
                organisation_id=organisation_id,
                page_id=social_account.page_id
            )
            new_demographics.set_top_countries(dict(top_countries))
            new_demographics.set_top_cities(dict(top_cities))
            new_demographics.set_locales(locale_data)

            db_session.add(new_demographics)
            await db_session.commit()

            return success_response(
                200,
                "Audience demographics retrieved successfully",
                demographics_data,
            )

    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


@router.get("/top_performing_post")
async def get_top_performing_post(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
):
    """
    Retrieve the top performing posts for a Facebook page based on impressions or composite score.
    """
    try:
        top_posts = await FacebookTopPostsService.get_top_posts(
            organisation_id, db_session
        )
        return success_response(
            200, "Top-performing posts retrieved successfully.", top_posts
        )
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")
