import asyncio
from datetime import datetime, timedelta
from io import By<PERSON><PERSON>
from typing import Annotated
import httpx
from collections import defaultdict
from fastapi import HTT<PERSON>Exception, status, Depends
from app.services.instagram import to_datetime
from app.utils.dependency import get_social_details, verify_organization
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.config import settings
from app.utils.logger import get_logger
from app.utils.success_response import fail_response, success_response
from app.utils.redis_cache import redis_client
import json
from app.models.model import Comment, Conversation, Message, Post, SocialMediaAccount
from sqlalchemy.dialects.postgresql import insert
from app.database.session import SessionLocal, get_db

logger = get_logger(__name__)
timeout = httpx.Timeout(20.0, connect=20.0, read=20.0, write=20.0)
redis_api_expiry_time = settings.REDIS_CACHE_EXPIRY_SECONDS


async def get_conversations_from_api(
    social_account: SocialMediaAccount,
    db_session: AsyncSession
):
    """get a list of your app user's conversations for an Facebook account"""
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                f"{settings.FACEBOOK_GRAPH_API_URL}/{social_account.page_id}/conversations",
                params={
                    "fields": "participants,updated_time,id",
                    "access_token": social_account.access_token
                }
            )
            response.raise_for_status()
            response_data = response.json()

            for item in response_data["data"]:
                new_convo = Conversation(
                    social_media_account_id=social_account.id,
                    convo_id=item.get('id'),
                    updated_time=to_datetime(item.get('updated_time')),
                    participants=item.get('participants').get('data', [])
                )
                await db_session.merge(new_convo)
            await db_session.commit()
        logger.info("conversations added to the db")

        return
    except httpx.HTTPStatusError as e:
        raise HTTPException(
            status_code=e.response.status_code, detail=e.response.text)
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        logger.error(f'A server error occurred: {str(e)}')
        return fail_response(500, "An unexpected error occurred")


async def get_messages_in_conversations(
    conversation_id: str,
    db_session: AsyncSession,
    social_account_token: str
):
    """get a list of earliest twenty messages in the db and saves it in the db"""
    url = f"{settings.FACEBOOK_GRAPH_API_URL}/{conversation_id}"
    headers = {"Authorization": f"Bearer {social_account_token}"}
    params = {
        "fields": "messages{id,created_time,from,to,message,attachments,story,shares}"}

    async with httpx.AsyncClient(timeout=timeout) as client:
        response = await client.get(
            url=url, headers=headers, params=params
        )
        response.raise_for_status()
        response_data = response.json()

        messages = response_data.get("messages").get("data", [])
        convo_id = response_data.get("id")

    # clean the messages response
    for message in messages:
        sender = message.get("from")
        recipient = message.get("to", {}).get("data", [])[0]
        text_message = message.get("message")
        created_time = to_datetime(message.get("created_time"))
        msg_id = message.get("id")
        attachments = message.get("attachments", {}).get("data", [])
        reactions = message.get("reactions", {}).get("data", [])
        shares = message.get("shares", {}).get("data", [])
        # save to the db
        new_entry = Message(
            platform="facebook",
            sender=sender,
            recipient=recipient,
            message=text_message,
            created_time=created_time,
            message_id=msg_id,
            conversation_id=convo_id,
            attachments=attachments,
            reactions=reactions,
            shares=shares
        )
        await db_session.merge(new_entry)
    await db_session.commit()
    return


async def fetch_messages_for_convo(convo_id: str, social_account_token: str):
    async with SessionLocal() as session:
        await get_messages_in_conversations(
            conversation_id=convo_id,
            social_account_token=social_account_token,
            db_session=session
        )


async def fetch_conversations_and_messages(
    social_media_details: SocialMediaAccount
):
    async with SessionLocal() as db_session:
        # fetch and save conversations from the API
        await get_conversations_from_api(
            db_session=db_session,
            social_account=social_media_details
        )

        # get the convo ids from the db
        result = await db_session.execute(
            select(Conversation.convo_id).where(
                Conversation.social_media_account_id == social_media_details.id
            )
        )
        convo_ids = [rows[0] for rows in result.fetchall()]

    # Step 3: Fetch all messages concurrently
    await asyncio.gather(*[
        fetch_messages_for_convo(convo_id, social_media_details.access_token)
        for convo_id in convo_ids
    ])
    return


# comments services
async def save_comments_to_db(
    media_id: str,
    access_token: str
):
    """
    Gets all comments on a specific Instagram media object and save to the db.
    """
    url = f"{settings.FACEBOOK_GRAPH_API_URL}/{media_id}/comments"
    headers = {"Content-Type": "application/json"}
    params = {
        "access_token": access_token,
        "fields": "id,attachment,created_time,from,is_hidden,like_count,message,object,parent,reactions,likes,comments"
    }

    async with SessionLocal() as db_session:
        try:
            logger.info("updating comments to the db")
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.get(url, headers=headers, params=params)
                response.raise_for_status()
                response_data = response.json()
                comments = response_data.get("data", [])
                logger.info(f"found {len(comments)} comments to update")
                for comment in comments:
                    # save to db
                    metadata = {
                        "replies": comment.get("comments", {}).get("data", []),
                        "hidden": comment.get("hidden", False),
                        "like_count": comment.get("like_count", 0),
                        "likes": comment.get("likes", {}).get("data", [])
                    }
                    try:
                        stmt = insert(Comment).values(
                            content=comment.get("message"),
                            media=comment.get("media", {}),
                            comment_id=comment.get("id"),
                            post_id=media_id,
                            parent_id=comment.get("parent_id"),
                            sender=comment.get("from", {}),
                            created_time=to_datetime(comment.get("created_time")),
                            attachments={
                                "type": comment.get("attachment", {}).get("type"),
                                "url": comment.get("attachment", {}).get("url")
                                } if comment.get("attachment") else None,
                            extra_data=metadata,
                            reactions=comment.get("reactions", {}).get("data", [])
                        ).on_conflict_do_update(
                            index_elements=['post_id', 'comment_id'],
                            set_={
                                "media": comment.get("media", {}),
                                "comment_id": comment.get("id"),
                                "post_id": media_id,
                                "parent_id": comment.get("parent_id"),
                                "sender": comment.get("from", {}),
                                "created_time": to_datetime(comment.get("created_time")),
                                "attachments": {
                                    "type": comment.get("attachment", {}).get("type"),
                                    "url": comment.get("attachment", {}).get("url")
                                    } if comment.get("attachment") else None,
                                "extra_data": metadata,
                                "reactions": comment.get("reactions", {}).get("data", [])
                                # add other fields to update if needed
                            }
                        )
                        await db_session.execute(stmt)
                    except Exception as e:
                        logger.error(f"an exception occurs: {str(e)}")

                await db_session.commit()
            logger.info("saving comments to the db")
            return

        except httpx.HTTPStatusError as e:
            raise HTTPException(
                status_code=e.response.status_code, detail=e.response.text)
        except HTTPException as e:
            return fail_response(e.status_code, e.detail)
        except Exception as e:
            await db_session.rollback()
            return fail_response(500, str(e))


# ####### UTILS ##########
async def extend_token(token: str):
    """extend the user access token"""
    try:
        logger.info("starting the call to the endpoint for exchanging tokens")
        params = {
            "grant_type": "fb_exchange_token",
            "client_id": settings.FACEBOOK_CLIENT_ID,
            "client_secret": settings.FACEBOOK_CLIENT_SECRET,
            "fb_exchange_token": token,
        }
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(f"{settings.FACEBOOK_TOKEN_URL}", params=params)
            response.raise_for_status()
            logger.info(f"token exchange response: {response.json()}")
            long_lived_token = response.json().get("access_token")
            expires = response.json().get("expires_in")
            if not long_lived_token:
                raise HTTPException(
                    status_code=400,
                    detail="Failed to retrieve long-lived token, re-login",
                )

            logger.info("tokens exchanges successfully")
            return long_lived_token, expires
    except httpx.HTTPStatusError as e:
        logger.error(f"An httpx status error occurred: : {str(e)}")
        raise HTTPException(status_code=400, detail="Facebook API error")
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(status_code=400, detail="An unexpected error occurred")


async def get_page_token(token, page_id):
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                f"{settings.FACEBOOK_GRAPH_API_URL}/{page_id}",
                params={"fields": "access_token", "access_token": token},
            )
            response.raise_for_status()
            return response.json().get("access_token")
    except httpx.HTTPStatusError as e:
        logger.error(f"An httpx status error occurred: : {str(e)}")
        return fail_response(400, "Facebook API error")
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        raise HTTPException(status_code=400, detail="An unexpected error occurred")


async def make_post(
    organisation_id: Annotated[str, Depends(verify_organization)],
    message: str,
    schedulecontent_id: str,
    # images: Optional[List[str]] = None,  # this should be optional
    db_session: AsyncSession = Depends(get_db),
):
    """Post a message to a Facebook page"""
    logger.info("Starting make_post function")
    try:
        if not message:  # and not images:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Either a message or at least one image must be provided.",
            )

        logger.info(
            f"Fetching social media account for organisation_id: {organisation_id}"
        )
        db_social_account = await get_social_details(
            organisation_id, "facebook", db_session
        )
        post_data = {
            "message": message,
            # "attached_media": [{"media_fbid": image_url} for image_url in images]
        }

        async with httpx.AsyncClient(timeout=timeout) as client:
            logger.info("Making a post to Facebook")
            response = await client.post(
                f"{settings.FACEBOOK_GRAPH_API_URL}/{db_social_account.page_id}/feed",
                params={"access_token": db_social_account.page_access_token},
                data=post_data,
            )
            response.raise_for_status()
            post_response = response.json()
            logger.info("saving post to the database")

            # Save to the db
            new_post = Post(
                social_media_account_id=db_social_account.id,
                post_id=post_response.get("id"),
                content=message,
                schedulecontent_id=schedulecontent_id,
            )
            db_session.add(new_post)
            await db_session.commit()
            await db_session.refresh(new_post)

        logger.info("Post created successfully")
        post_data = {
            "id": new_post.id,
            "content": new_post.content,
            "post_id": new_post.post_id,
            "schedulecontent_id": new_post.schedulecontent_id,
        }
        return success_response(200, "Post created successfully", post_data)
    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


async def get_page_posts(
    organisation_id: Annotated[str, Depends(verify_organization)],
    db_session: AsyncSession = Depends(get_db),
    limit: int = 10,  # Number of posts to fetch per request
    after_cursor: str = None,  # Cursor for pagination
):
    """
    Fetch all posts for a particular Facebook page.

    Parameters:
    - organisation_id: The organization identifier (validated via dependency)
    - limit: Number of posts to fetch per request
    - after_cursor: Cursor for paginated results

    Returns:
    - List of posts with pagination information.
    """
    try:
        # Retrieve social account details for the organization
        db_social_account = await get_social_details(
            organisation_id, "facebook", db_session
        )

        # Prepare query parameters
        params = {
            "access_token": db_social_account.page_access_token,
            "fields": "id,message,created_time,permalink_url,likes.summary(true),comments.summary(true),attachments",
            "limit": limit,
        }
        if after_cursor:
            params["after"] = after_cursor

        # Fetch posts from Facebook API
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                f"{settings.FACEBOOK_GRAPH_API_URL}/{db_social_account.page_id}/feed",
                params=params,
            )
            response.raise_for_status()
            data = response.json()

        # Extract posts and pagination details
        posts = data.get("data", [])
        paging_info = data.get("paging", {})
        formatted_posts = []
        for post in posts:
            formatted_post = {
                "post_id": post.get("id"),
                "message": post.get("message"),
                "created_time": post.get("created_time"),
                "permalink_url": post.get("permalink_url"),
                "likes_count": post.get("likes", {})
                .get("summary", {})
                .get("total_count"),
                "comments_count": post.get("comments", {})
                .get("summary", {})
                .get("total_count"),
            }

            # Retrieve media attachments if available
            attachments = post.get("attachments", {}).get("data", [])
            if attachments:
                formatted_post["attachments"] = [
                    {
                        "media_url": attachment.get("media", {})
                        .get("image", {})
                        .get("src")
                        or attachment.get("media", {}).get("source"),
                    }
                    for attachment in attachments
                ]
            else:
                formatted_post["attachments"] = []

            formatted_posts.append(formatted_post)

        return success_response(
            200,
            "Posts retrieved successfully",
            {
                "posts": formatted_posts,
                "paging": {
                    "next_cursor": paging_info.get("cursors", {}).get("after"),
                    "previous_cursor": paging_info.get("cursors", {}).get("before"),
                    "next_url": paging_info.get("next"),
                    "previous_url": paging_info.get("previous"),
                },
            },
        )

    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


async def delete_post(
    organisation_id: Annotated[str, Depends(verify_organization)],
    post_id: str,
    db_session: AsyncSession = Depends(get_db),
):
    """Delete a post from a Facebook page"""
    try:
        db_social_account = await get_social_details(
            organisation_id, "facebook", db_session
        )
        # confirm post is in the db
        result = await db_session.execute(select(Post).filter_by(id=post_id))
        db_post = result.scalars().first()
        if not db_post:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Requested post not found"
            )
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.delete(
                f"{settings.FACEBOOK_GRAPH_API_URL}/{db_post.post_id}",
                params={"access_token": db_social_account.page_access_token},
            )
            response.raise_for_status()
            await db_session.delete(db_post)
            await db_session.commit()
            return success_response(200, "Post deleted successfully")
    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        return fail_response(e.status_code, e.detail)
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        return fail_response(500, "an unexpected error occurred")


def extract_images(response):
    images = []
    # Get attachments data if available
    logger.info('extracting images from response')
    attachments = response.get("attachments", {}).get("data", [])
    for attachment in attachments:
        # If there are subattachments (e.g., multiple images)
        if "subattachments" in attachment:
            subattachments = attachment["subattachments"].get("data", [])
            for subattachment in subattachments:
                src = subattachment.get("media", {}).get("image", {}).get("src")
                img_type = subattachment.get("type")
                if src:
                    images.append({"src": src, "type": img_type})
        else:
            # Direct attachment with image info
            src = attachment.get("media", {}).get("image", {}).get("src")
            img_type = attachment.get("type")
            if src:
                images.append({"src": src, "type": img_type})
    return images


async def get_post_details(
    organisation_id: str,
    post_id: str,
    page_access_token,
    db_session: AsyncSession
):
    """
    ## Retrieve metrics for a particular post of a specific page.
    Metrics include:
    - Post Impressions
    - Engagements
    - Click Rate
    - Reach
    - Likes
    - Comments
    - Content Type
    """
    try:
        # Check if data is in the cache
        logger.info("fetching post details from facebook")

        cache_key = f"fb_get_post_details{organisation_id}_{post_id}"
        cache_data = redis_client.get(cache_key)
        if cache_data:
            logger.info("Data is available in the cache")
            return json.loads(cache_data)

        async with httpx.AsyncClient(timeout=timeout) as client:
            # Fetch details for the post
            post_response = await client.get(
                f"{settings.FACEBOOK_GRAPH_API_URL}/{post_id}",
                params={
                    "access_token": page_access_token,
                    "fields": "id,created_time,from,full_picture,message,shares,updated_time,comments.summary(true),insights.metric(page_fans,post_impressions,post_clicks,post_reactions_like_total,post_impressions_unique),reactions.limit(0).summary(true),attachments{subattachments,media}",
                }

            )
            post_response.raise_for_status()
            # convert to json
            response_structure = post_response.json()

            insights_data = {
                item["name"]: item["values"][0]["value"]
                for item in response_structure['insights'].get("data", [])
            }
            logger.info('getting the comments count')
            total_comments = response_structure.get("comments", {}).get("summary", {}).get("total_count", 0)

            logger.info('getting the image data')

            images = extract_images(response_structure)

            # Aggregate metrics
            logger.info('combining response into a better structure')
            # logger.info(response_structure)
            post_metrics = {
                "post_id": post_id,
                "message": response_structure.get("message"),
                "username": response_structure.get("from", {}).get("name"),
                "created_time": response_structure.get("created_time"),
                "primary_image": response_structure.get("full_picture", ""),
                "images": images,
                "impressions": insights_data.get("post_impressions", 0),
                "clicks": insights_data.get("post_clicks", 0),
                "likes": insights_data.get("post_reactions_like_total", 0),
                "reach": insights_data.get("post_impressions_unique", 0),
                "comments": total_comments,
                "engagements": insights_data.get("post_clicks", 0)
                + insights_data.get("post_reactions_like_total", 0)
                + total_comments,
                "click_rate": round(
                    (
                        insights_data.get("post_clicks", 0)
                        / insights_data.get("post_impressions", 1)
                    )
                    * 100,
                    2,
                )
                if insights_data.get("post_impressions", 0) > 0
                else 0.0,
            }

        # Save to the cache
        logger.info("Saving to Redis cache")
        redis_client.setex(
            cache_key, redis_api_expiry_time, json.dumps(post_metrics))

        return post_metrics

    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Internal server error: {str(e)}")
        raise HTTPException(status_code=500, detail="an unexpected error occurred")


def composite_score(insights):
    """
    Compute a composite score for a post using all available insights.
    Key metrics are weighted higher, but all numeric values contribute.
    """
    # Flatten all metrics, summing values for repeated metrics

    metric_totals = defaultdict(float)
    for metric in insights:
        for name, value in metric.items():
            # If value is a list (e.g., period=day), sum all values
            if isinstance(value, list):
                total = 0
                for v in value:
                    if isinstance(v, dict) and "value" in v:
                        total += v["value"]
                    elif isinstance(v, (int, float)):
                        total += v
                metric_totals[name] += total
            elif isinstance(value, (int, float)):
                metric_totals[name] += value

    # Define weights for key metrics, all others get 0.05
    weights = {
        "post_reactions_like_total": 0.5,
        "post_impressions": 0.2,
        "post_clicks": 0.15,
        "post_impressions_unique": 0.15,
        "post_engagements": 0.2,
        "post_comments": 0.1,
        "post_reactions_love_total": 0.1,
        "post_reactions_wow_total": 0.05,
        "post_reactions_haha_total": 0.05,
        "post_reactions_sorry_total": 0.05,
        "post_reactions_anger_total": 0.05,
        "post_shares": 0.1,
        "retweets": 0.1,
    }
    default_weight = 0.05

    score = 0.0
    for metric, value in metric_totals.items():
        weight = weights.get(metric, default_weight)
        score += value * weight

    return score


async def get_facebook_page_metrics(page_id, access_token, period="day", days=30):
    """
    Retrieves Facebook page metrics using the Graph API.

    Args:
        page_id: Facebook Page ID
        access_token: Facebook Graph API access token with read_insights permission
        period: Time period for metrics (default: 'day')
        days: Number of days to look back (default: 30)

    Returns:
        Dictionary containing the metrics data
    """
    # Use only the metrics that were confirmed to be valid
    metrics = [
        'page_follows',           # Followers
        'page_impressions',       # Total impressions
        'page_impressions_unique', # Total reach
        'page_post_engagements'   # Total engagements
    ]

    # Calculate date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)

    # Convert dates to UNIX timestamps
    since = int(start_date.timestamp())
    until = int(end_date.timestamp())

    # Format the metrics string for the API call
    metrics_string = ','.join(metrics)

    # Construct the URL for the API call
    api_version = 'v23.0'
    url = f'https://graph.facebook.com/{api_version}/{page_id}/insights'

    # Set up parameters for the API call
    params = {
        'metric': metrics_string,
        'period': period,
        'since': since,
        'until': until,
        'access_token': access_token
    }

    try:
        # Make the API request
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(url, params=params)

        # Check if the request was successful
        if response.status_code == 200:
            data = response.json()
            results = {}

            # Process the response data
            if 'data' in data:
                for metric_data in data['data']:
                    metric_name = metric_data['name']
                    values = [entry['value'] for entry in metric_data['values']]

                    # For followers, take the most recent value
                    if metric_name == 'page_follows':
                        results[metric_name] = values[-1] if values else 0
                    else:
                        # For other metrics, sum the values over the period
                        results[metric_name] = sum(values)
            followers = results.get('page_follows', 0)
            impressions = results.get('page_impressions', 0)
            reach = results.get('page_impressions_unique', 0)
            engagements = results.get('page_post_engagements', 0)
            raw_data = results
            engagement_rate = (engagements / impressions * 100) if impressions > 0 else 0

            return {
                'followers': followers,
                'impressions': impressions,
                'reach': reach,
                'engagements': engagements,
                'engagement_rate': engagement_rate,
                'raw_data': raw_data
            }

    except httpx.HTTPStatusError as e:
        logger.error(f"Facebook API error: {e.response.json()}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Facebook API error: {e.response.json()['error']['message']}",
        )
    except Exception as e:
        logger.error(f"An error occurred while fetching Facebook metrics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")


# Function to update Facebook metrics immediately after connecting an account
async def update_facebook_metrics_immediately(account_id: str):
    """
    Update metrics for a Facebook account immediately after it's connected
    This function runs in the background and doesn't block the API response
    """
    logger.info(f"Starting immediate metrics collection for Facebook account ID: {account_id}")

    try:
        # Import here to avoid circular imports
        from app.tasks.facebook_metrics import (
            fetch_and_store_facebook_metrics_for_account,
        )

        # Call the task function to update metrics for this specific account
        await fetch_and_store_facebook_metrics_for_account(account_id)

        logger.info(f"Successfully scheduled metrics update for Facebook account ID: {account_id}")

    except Exception as e:
        logger.error(f"Error updating metrics for Facebook account ID {account_id}: {str(e)}")


async def upload_image_to_facebook(page_id: str, file_data: dict, page_access_token: str) -> str:
    """
    Uploads an image file to Facebook using httpx's AsyncClient.
    Returns the Facebook image ID on success.
    """
    try:
        upload_url = f"{settings.FACEBOOK_GRAPH_API_URL}/{page_id}/photos"
        files = {"source": file_data["file"]}
        params = {
            "access_token": page_access_token,
            "published": "false"
        }
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(
                upload_url, params=params, files=files)
            response.raise_for_status()

        result = response.json()

        if response.status_code == 200 and "id" in result:
            logger.info(f'image uploaded successfull with id {result['id']}')
            return result['id']
        else:
            logger.error(f'Error uploading image: {result}')
            raise HTTPException(
                status_code=400,
                detail=f'Error uploading image: {result}'
            )
    except Exception as e:
        logger.error(f'An error occurred: {str(e)}')
        raise HTTPException(
            status_code=status.HTTP_500_INTERVAL_SERVER_ERROR,
            detail='An unexpected error occurred'
        )


async def download_image_from_url(image_url: str) -> dict:
    """Downloads an image from a URL and returns the image details."""
    if not image_url:
        logger.error('No image URL provided')
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="No image URL provided"
        )
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(image_url)

            response.raise_for_status()

            content_type = response.headers.get("Content-Type", "image/jpeg")
            filename = image_url.split("/")[-1].split("?")[0]

            logger.info(f"Downloaded image: {filename} with content type: {content_type}")

            # wrap the binary data in a BytesIO object
            img_bytes = BytesIO(response.content)
            logger.info('image downloaded and generated as BytesIO')
            return {"file": (filename, img_bytes, content_type)}
    except Exception as e:
        logger.exception(f'Error downloading image from URL: {image_url}: {str(e)}')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error downloading image from URL"
        )


async def save_post_to_database(
    post_id: str, schedule_content_id: str,
    social_media_account_id: str, db_session: AsyncSession
):
    """saves the posted content details to the database"""
    try:
        logger.info("saving post to the database")
        new_post = Post(
            post_id=post_id, schedulecontent_id=schedule_content_id,
            social_media_account_id=social_media_account_id
        )

        db_session.add(new_post)
        await db_session.commit()
        await db_session.refresh(new_post)

        logger.info("post data saved to the database")
        return new_post
    except Exception as e:
        await db_session.rollback()
        logger.error(f'An error occurred when saving post details: {str(e)}')
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail='An error occurred during saving to database'
        )
