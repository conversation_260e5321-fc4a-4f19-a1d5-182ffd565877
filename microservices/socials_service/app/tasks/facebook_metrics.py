import asyncio
import json
from datetime import datetime, timedelta
import traceback
from dateutil.relativedelta import relativedelta
from app.services.facebook import (
    composite_score, extract_images,
    fetch_conversations_and_messages, save_comments_to_db
)
from app.services.facebook_post_perf import fetch_all_facebook_posts
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import selectinload
from sqlalchemy.future import select
from app.models import model
from app.database.session import SessionLocal
from app.utils.redis_cache import redis_client
from app.utils.logger import get_logger
from app.core.config import settings
import httpx

logger = get_logger(__name__)

# Constants
FACEBOOK_GRAPH_API_URL = settings.FACEBOOK_GRAPH_API_URL
# Use configurable cache expiry times from settings
redis_expiry_time = settings.REDIS_LONG_CACHE_EXPIRY_SECONDS  # For scheduler data
redis_api_expiry_time = settings.REDIS_CACHE_EXPIRY_SECONDS   # For API endpoints
timeout = httpx.Timeout(10.0, connect=10.0, read=15.0, write=20.0)


async def fetch_and_store_facebook_metrics():
    """
    Fetch all Facebook metrics for all organizations and store them in the database and Redis.
    This function is meant to be called by a scheduler twice a day.
    """
    logger.info("Starting scheduled Facebook metrics update")

    # Track overall statistics
    total_accounts = 0
    successful_accounts = 0
    failed_accounts = 0
    rate_limited_accounts = 0
    async with SessionLocal() as db:
        try:
            # Get all Facebook social media accounts
            try:
                result = await db.execute(
                    select(model.SocialMediaAccount)
                    .options(selectinload(model.SocialMediaAccount.posts))
                    .where(
                        model.SocialMediaAccount.platform == "facebook",
                        model.SocialMediaAccount.login_status == True
                    )
                )
                social_accounts = result.scalars().all()
                total_accounts = len(social_accounts)

                logger.info(f"Found {len(social_accounts)} Facebook accounts to update")
            except SQLAlchemyError as e:
                logger.error(f"Database error when fetching Facebook accounts: {str(e)}")
                raise

            # Process accounts in batches to avoid long transactions
            batch_size = 10
            for i in range(0, total_accounts, batch_size):
                batch = social_accounts[i:i+batch_size]
                logger.info(
                    f"Processing batch {i//batch_size + 1} of "
                    f"{(total_accounts + batch_size - 1)//batch_size} batches")

                for account in batch:
                    # Track success/failure for each account
                    account_success = False
                    rate_limited = False
                    try:
                        await fetch_conversations_and_messages(account)
                        logger.info("conversations and messages updated")
                    except Exception as e:
                        logger.error(f'error occurred in updating conversations and messages: {str(e)}')

                    try:
                        for posts in account.posts:
                            await save_comments_to_db(
                                posts.post_id, account.access_token)
                        # pass
                    except Exception as e:
                        logger.error(f'error occurred in updating comments {str(e)}')

                    try:
                        # await fetch_and_store_page_metrics(account, db)
                        pass
                    except Exception as e:
                        logger.error(f"Error fetching account metrics for {account.username}: {str(e)}")

                    # Fetch and store audience demographics
                    try:
                        # await fetch_and_store_audience_demographics(account, db)
                        pass
                    except Exception as e:
                        logger.error(f"Error fetching audience demographics for {account.username}: {str(e)}")

                    # Fetch and store media metrics
                    try:
                        await fetch_and_store_growth_trends(account, db)
                        # pass
                    except Exception as e:
                        logger.error(f"Error fetching media metrics for {account.username}: {str(e)}")
                        # Continue with commit even if this step fails

                    try:
                        await fetch_all_facebook_posts(db, account.organisation_id)
                        logger.info("successfully updated facebook top perf posts")
                    except Exception as e:
                        logger.error(f"Error fetching media metrics for {account.username}: {str(e)}")
                        # Continue with commit even if this step fails

                    # Mark as successful if we got this far
                    account_success = True
                    # logger.info(f"Successfully updated metrics for Facebook account: {account.username}")

                    # Update statistics
                    if rate_limited:
                        rate_limited_accounts += 1
                    elif account_success:
                        successful_accounts += 1
                    else:
                        failed_accounts += 1

                    # Sleep to avoid rate limiting
                    await asyncio.sleep(1)

                # Commit after each batch
                try:
                    await db.commit()
                    logger.info(f"Committed batch {i//batch_size + 1}")
                except SQLAlchemyError as e:
                    await db.rollback()
                    logger.error(f"Database error when committing batch {i//batch_size + 1}: {str(e)}")
                    # Continue with next batch even if this one fails
            logger.info(f"Completed scheduled Facebook metrics update. Total: {total_accounts}, Successful: {successful_accounts}, Failed: {failed_accounts}, Rate Limited: {rate_limited_accounts}")

        except Exception as e:
            await db.rollback()
            logger.error(f"Error in scheduled Facebook metrics update: {str(e)}")
            logger.error(traceback.format_exc())


async def fetch_and_store_page_metrics(account, db: AsyncSession):
    """Fetch and store page metrics (overview)"""
    try:
        # Define date range (last 30 days)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)

        # Convert dates to UNIX timestamps
        since = int(start_date.timestamp())
        until = int(end_date.timestamp())

        # Define metrics to try - matching the test script
        metrics_to_try = [
            'page_fans_total',       # Try this for followers (alternative name)
            'page_follows',          # Another alternative for followers
            'page_follower_count',   # Another possible name
            'page_impressions',      # Total impressions
            'page_impressions_total', # Alternative name for impressions
            'page_impressions_unique', # Total reach
            'page_engaged_users',    # Total engagements
            'page_post_engagements'  # Alternative for engagements
        ]

        # Results dictionary
        results = {}
        valid_metrics = []

        # Try each metric individually to determine which ones are valid
        for metric in metrics_to_try:
            try:
                logger.info(f"Trying metric: {metric}")
                async with httpx.AsyncClient(timeout=timeout) as client:
                    response = await client.get(
                        f"{FACEBOOK_GRAPH_API_URL}/{account.page_id}/insights",
                        params={
                            "metric": metric,
                            "period": "day",
                            "since": since,
                            "until": until,
                            "access_token": account.page_access_token
                        },
                    )
                    response.raise_for_status()
                    data = response.json()

                    if 'data' in data and len(data['data']) > 0:
                        valid_metrics.append(metric)
                        metric_data = data['data'][0]
                        values = [entry['value'] for entry in metric_data['values']]

                        # For cumulative metrics like followers, take the most recent value
                        if metric in ['page_fans_total', 'page_follows', 'page_follower_count']:
                            results[metric] = values[-1] if values else 0
                        else:
                            # For other metrics, sum the values over the period
                            results[metric] = sum(values)
                    elif 'error' in data:
                        logger.error(f"  Error for {metric}: {data['error'].get('message')}")
                    else:
                        logger.info(f"  No data returned for {metric}")

            except Exception as e:
                logger.error(f"  Exception for {metric}: {str(e)}")

        logger.info(f"Valid metrics found: {valid_metrics}")

        # Get the values using the same approach as the test script
        followers = results.get('page_fans_total',
                    results.get('page_follows',
                    results.get('page_follower_count', 0)))
        impressions = results.get('page_impressions',
                    results.get('page_impressions_total', 0))
        reach = results.get('page_impressions_unique', 0)
        engagements = results.get('page_engaged_users',
                    results.get('page_post_engagements', 0))

        # Calculate engagement rate
        engagement_rate = (engagements / impressions * 100) if impressions > 0 else 0

        # Create metrics record with the new field names
        metrics = model.FacebookPageMetrics(
            organisation_id=account.organisation_id,
            page_id=account.page_id,
            total_followers=followers,
            total_impressions=impressions,
            total_reach=reach,
            total_engagements=engagements,
            engagement_rate=round(engagement_rate, 2)
        )
        logger.info(f"Creating metrics record: {metrics}")
        # Add metrics to database
        db.add(metrics)

        # Cache metrics in Redis with the new field names
        cache_key = f"fb_overview_{account.organisation_id}"
        cache_data = {
            "total_followers": followers,
            "total_impressions": impressions,
            "total_reach": reach,
            "total_engagements": engagements,
            "engagement_rate": round(engagement_rate, 2),
            "updated_at": datetime.now().isoformat()
        }
        logger.info(f"Caching metrics in Redis: {cache_data}")
        redis_client.setex(cache_key, redis_expiry_time, json.dumps(cache_data))

        logger.info(f"Updated page metrics for Facebook account: {account.username}")

    except Exception as e:
        logger.error(f"Error fetching page metrics for {account.username}: {str(e)}")
        raise


async def fetch_and_store_audience_demographics(account, db: AsyncSession):
    """Fetch and store audience demographics"""
    try:
        # Fetch audience demographics from Facebook API
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                f"{FACEBOOK_GRAPH_API_URL}/{account.page_id}/insights",
                params={
                    "metric": "page_fans,page_fans_locale,page_fans_city,page_fans_country,page_fan_adds,page_fan_adds_unique,page_fan_removes,page_fan_removes_unique",
                    "access_token": account.page_access_token,
                },
            )
            response.raise_for_status()
            audience_data = response.json()

        # Process audience data
        if not audience_data.get("data"):
            logger.info(f"No audience demographic data available for {account.username}")
            return

        # Extract and sort country data
        country_data = audience_data["data"][0].get("values", [{}])[0].get("value", {})
        top_countries = sorted(
            country_data.items(), key=lambda item: item[1], reverse=True
        )[:10]

        # Extract and sort city data
        city_data = audience_data["data"][1].get("values", [{}])[0].get("value", {})
        top_cities = sorted(
            city_data.items(), key=lambda item: item[1], reverse=True
        )[:10]

        # Extract locale demographics
        locale_data = audience_data["data"][2].get("values", [{}])[0].get("value", {})

        # Create demographics record
        demographics = model.FacebookAudienceDemographics(
            organisation_id=account.organisation_id,
            page_id=account.page_id
        )
        demographics.set_top_countries(dict(top_countries))
        demographics.set_top_cities(dict(top_cities))
        demographics.set_locales(locale_data)

        # Add demographics to database
        db.add(demographics)

        # # Cache demographics in Redis
        # cache_key = f"fb_audience_demographics_{account.organisation_id}"
        # cache_data = {
        #     "top_countries": top_countries,
        #     "top_cities": top_cities,
        #     "locales": locale_data,
        #     "updated_at": datetime.now().isoformat()
        # }
        # redis_client.setex(cache_key, redis_expiry_time, json.dumps(cache_data))

        logger.info(f"Updated audience demographics for Facebook account: {account.username}")

    except Exception as e:
        logger.error(f"Error fetching audience demographics for {account.username}: {str(e)}")
        raise

async def fetch_and_store_growth_trends(account, db: AsyncSession):
    """Fetch and store growth trends"""
    try:
        # Define date range (last 6 months)
        # Use naive datetime objects consistently
        end_date = datetime.now()
        start_date = end_date - relativedelta(months=6)

        # Fetch audience growth trend
        await fetch_and_store_audience_growth_trend(account, db, start_date, end_date)

        # Fetch engagement growth trend
        await fetch_and_store_engagement_growth_trend(account, db, start_date, end_date)

        # Fetch reach growth trend
        await fetch_and_store_reach_growth_trend(account, db, start_date, end_date)

        # Fetch click rate growth trend
        await fetch_and_store_click_rate_growth_trend(account, db, start_date, end_date)

        logger.info(f"Updated growth trends for Facebook account: {account.username}")

    except Exception as e:
        logger.error(f"Error fetching growth trends for {account.username}: {str(e)}")
        raise

async def fetch_and_store_audience_growth_trend(account, db: AsyncSession, start_date, end_date):
    """Fetch and store audience growth trend"""
    try:
        trend_data = []
        async with httpx.AsyncClient(timeout=timeout) as client:
            current_date = start_date
            while current_date < end_date:
                # Define current chunk's start and end dates
                chunk_end_date = current_date + relativedelta(months=1)
                if chunk_end_date > end_date:
                    chunk_end_date = end_date

                # Convert dates to strings
                since_date = current_date.strftime("%Y-%m-%d")
                until_date = chunk_end_date.strftime("%Y-%m-%d")

                # Fetch data for the current month
                response = await client.get(
                    f"{FACEBOOK_GRAPH_API_URL}/{account.page_id}/insights",
                    params={
                        "metric": "page_fans",
                        "period": "days_28",
                        "since": since_date,
                        "until": until_date,
                        "access_token": account.page_access_token,
                    },
                )
                response.raise_for_status()
                audience_data = response.json()

                # Check and process the response
                if audience_data.get("data") and len(audience_data["data"]) > 0:
                    total_fans = sum(
                        value.get("value", 0)
                        for value in audience_data["data"][0].get("values", [])
                    )
                else:
                    total_fans = 0  # Default to 0 if no data available

                trend_data.append(
                    {"month": current_date.strftime("%Y-%m"), "fans": total_fans}
                )

                # Move to the next chunk
                current_date = chunk_end_date

        # Calculate growth percentages
        growth_trend = []
        for i in range(len(trend_data)):
            current = trend_data[i]

            # Create growth trend record
            growth = model.FacebookGrowthTrend(
                organisation_id=account.organisation_id,
                page_id=account.page_id,
                trend_type="audience",
                month=current["month"],
                value=current["fans"]
            )

            # Calculate growth percentage if not the first month
            if i > 0:
                previous = trend_data[i - 1]
                if previous["fans"] == 0:
                    growth_percentage = 100.0 if current["fans"] > 0 else 0
                else:
                    growth_percentage = ((current["fans"] - previous["fans"]) / previous["fans"]) * 100
                growth.growth_percentage = growth_percentage

            # Add to database
            db.add(growth)

            # Add to list for caching
            growth_trend.append({
                "month": current["month"],
                "fans": current["fans"],
                "growth": growth.growth_percentage
            })

        # Cache growth trend in Redis
        cache_key = f"fb_audience_growth_trend_{account.organisation_id}_6"
        redis_client.setex(cache_key, redis_expiry_time, json.dumps(growth_trend))

        logger.info(f"Updated audience growth trend for Facebook account: {account.username}")

    except Exception as e:
        logger.error(f"Error fetching audience growth trend for {account.username}: {str(e)}")
        raise

async def fetch_and_store_engagement_growth_trend(account, db: AsyncSession, start_date, end_date):
    """Fetch and store engagement growth trend"""
    try:
        engagement_trend_data = []
        async with httpx.AsyncClient(timeout=timeout) as client:
            current_date = start_date
            while current_date < end_date:
                # Define current chunk's start and end dates
                chunk_end_date = current_date + relativedelta(months=1)
                if chunk_end_date > end_date:
                    chunk_end_date = end_date

                # Convert dates to strings
                since_date = current_date.strftime("%Y-%m-%d")
                until_date = chunk_end_date.strftime("%Y-%m-%d")

                # Fetch data for the current month
                response = await client.get(
                    f"{FACEBOOK_GRAPH_API_URL}/{account.page_id}/insights",
                    params={
                        "metric": "page_post_engagements",
                        "period": "week",
                        "since": since_date,
                        "until": until_date,
                        "access_token": account.page_access_token,
                    },
                )
                response.raise_for_status()
                engagement_data = response.json()

                # Process the data
                total_engagements = 0
                if engagement_data.get("data") and len(engagement_data["data"]) > 0:
                    for value in engagement_data["data"][0].get("values", []):
                        total_engagements += value.get("value", 0)

                engagement_trend_data.append({
                    "month": current_date.strftime("%Y-%m"),
                    "engagements": total_engagements
                })

                # Move to the next chunk
                current_date = chunk_end_date

        # Calculate growth percentages and store in database
        growth_trend = []
        for i in range(len(engagement_trend_data)):
            current = engagement_trend_data[i]

            # Create growth trend record
            growth = model.FacebookGrowthTrend(
                organisation_id=account.organisation_id,
                page_id=account.page_id,
                trend_type="engagement",
                month=current["month"],
                value=current["engagements"]
            )

            # Calculate growth percentage if not the first month
            if i > 0:
                previous = engagement_trend_data[i - 1]
                if previous["engagements"] == 0:
                    growth_percentage = 100.0 if current["engagements"] > 0 else 0
                else:
                    growth_percentage = ((current["engagements"] - previous["engagements"]) / previous["engagements"]) * 100
                growth.growth_percentage = growth_percentage

            # Add to database
            db.add(growth)

            # Add to list for caching
            growth_trend.append({
                "month": current["month"],
                "engagements": current["engagements"],
                "growth": growth.growth_percentage
            })

        # Cache growth trend in Redis
        cache_key = f"fb_engagement_growth_trend_{account.organisation_id}_6"
        redis_client.setex(cache_key, redis_expiry_time, json.dumps(growth_trend))

        logger.info(f"Updated engagement growth trend for Facebook account: {account.username}")

    except Exception as e:
        logger.error(f"Error fetching engagement growth trend for {account.username}: {str(e)}")
        raise

async def fetch_and_store_reach_growth_trend(account, db: AsyncSession, start_date, end_date):
    """Fetch and store reach growth trend"""
    try:
        reach_trend_data = []
        async with httpx.AsyncClient(timeout=timeout) as client:
            current_date = start_date
            while current_date < end_date:
                # Define current chunk's start and end dates
                chunk_end_date = current_date + relativedelta(months=1)
                if chunk_end_date > end_date:
                    chunk_end_date = end_date

                # Convert dates to strings
                since_date = current_date.strftime("%Y-%m-%d")
                until_date = chunk_end_date.strftime("%Y-%m-%d")

                # Fetch data for the current month
                response = await client.get(
                    f"{FACEBOOK_GRAPH_API_URL}/{account.page_id}/insights",
                    params={
                        "metric": "page_impressions_unique",
                        "period": "week",
                        "since": since_date,
                        "until": until_date,
                        "access_token": account.page_access_token,
                    },
                )
                response.raise_for_status()
                reach_data = response.json()

                # Process the data
                total_reach = 0
                if reach_data.get("data") and len(reach_data["data"]) > 0:
                    for value in reach_data["data"][0].get("values", []):
                        total_reach += value.get("value", 0)

                reach_trend_data.append({
                    "month": current_date.strftime("%Y-%m"),
                    "reach": total_reach
                })

                # Move to the next chunk
                current_date = chunk_end_date

        # Calculate growth percentages and store in database
        growth_trend = []
        for i in range(len(reach_trend_data)):
            current = reach_trend_data[i]

            # Create growth trend record
            growth = model.FacebookGrowthTrend(
                organisation_id=account.organisation_id,
                page_id=account.page_id,
                trend_type="reach",
                month=current["month"],
                value=current["reach"]
            )

            # Calculate growth percentage if not the first month
            if i > 0:
                previous = reach_trend_data[i - 1]
                if previous["reach"] == 0:
                    growth_percentage = 100.0 if current["reach"] > 0 else 0
                else:
                    growth_percentage = ((current["reach"] - previous["reach"]) / previous["reach"]) * 100
                growth.growth_percentage = growth_percentage

            # Add to database
            db.add(growth)

            # Add to list for caching
            growth_trend.append({
                "month": current["month"],
                "reach": current["reach"],
                "growth": growth.growth_percentage
            })

        # Cache growth trend in Redis
        cache_key = f"fb_reach_growth_trend_{account.organisation_id}_6"
        redis_client.setex(cache_key, redis_expiry_time, json.dumps(growth_trend))

        logger.info(f"Updated reach growth trend for Facebook account: {account.username}")

    except Exception as e:
        logger.error(f"Error fetching reach growth trend for {account.username}: {str(e)}")
        raise

async def fetch_and_store_click_rate_growth_trend(account, db: AsyncSession, start_date, end_date):
    """Fetch and store click rate growth trend"""
    try:
        click_trend_data = []
        async with httpx.AsyncClient(timeout=timeout) as client:
            current_date = start_date
            while current_date < end_date:
                # Define current chunk's start and end dates
                chunk_end_date = current_date + relativedelta(months=1)
                if chunk_end_date > end_date:
                    chunk_end_date = end_date

                # Convert dates to strings
                since_date = current_date.strftime("%Y-%m-%d")
                until_date = chunk_end_date.strftime("%Y-%m-%d")

                # Fetch data for the current month
                response = await client.get(
                    f"{FACEBOOK_GRAPH_API_URL}/{account.page_id}/insights",
                    params={
                        "metric": "post_clicks,page_impressions",
                        "period": "week",
                        "since": since_date,
                        "until": until_date,
                        "access_token": account.page_access_token,
                    },
                )
                response.raise_for_status()
                data = response.json()

                # Process the data
                total_clicks = 0
                total_impressions = 0

                if data.get("data") and len(data["data"]) > 0:
                    # Extract clicks
                    clicks_data = next((item for item in data["data"] if item["name"] == "post_clicks"), None)
                    if clicks_data:
                        for value in clicks_data.get("values", []):
                            total_clicks += value.get("value", 0)

                    # Extract impressions
                    impressions_data = next((item for item in data["data"] if item["name"] == "page_impressions"), None)
                    if impressions_data:
                        for value in impressions_data.get("values", []):
                            total_impressions += value.get("value", 0)

                # Calculate click rate
                click_rate = (total_clicks / total_impressions * 100) if total_impressions > 0 else 0

                click_trend_data.append({
                    "month": current_date.strftime("%Y-%m"),
                    "click_rate": click_rate
                })

                # Move to the next chunk
                current_date = chunk_end_date

        # Calculate growth percentages and store in database
        growth_trend = []
        for i in range(len(click_trend_data)):
            current = click_trend_data[i]

            # Create growth trend record
            growth = model.FacebookGrowthTrend(
                organisation_id=account.organisation_id,
                page_id=account.page_id,
                trend_type="click_rate",
                month=current["month"],
                value=int(current["click_rate"] * 100)  # Store as integer (percentage * 100)
            )

            # Calculate growth percentage if not the first month
            if i > 0:
                previous = click_trend_data[i - 1]
                if previous["click_rate"] == 0:
                    growth_percentage = 100.0 if current["click_rate"] > 0 else 0
                else:
                    growth_percentage = ((current["click_rate"] - previous["click_rate"]) / previous["click_rate"]) * 100
                growth.growth_percentage = growth_percentage

            # Add to database
            db.add(growth)

            # Add to list for caching
            growth_trend.append({
                "month": current["month"],
                "click_rate": current["click_rate"],
                "growth": growth.growth_percentage
            })

        # Cache growth trend in Redis
        cache_key = f"fb_click_rate_growth_trend_{account.organisation_id}_6"
        redis_client.setex(cache_key, redis_expiry_time, json.dumps(growth_trend))

        logger.info(f"Updated click rate growth trend for Facebook account: {account.username}")

    except Exception as e:
        logger.error(f"Error fetching click rate growth trend for {account.username}: {str(e)}")
        raise


async def fetch_and_store_top_performing_posts(account, db: AsyncSession):
    """Fetch and store top performing posts"""
    try:
        # Fetch posts directly from Facebook API
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(
                f"{FACEBOOK_GRAPH_API_URL}/{account.page_id}/posts",
                params={
                    "access_token": account.page_access_token,
                    "fields": "message,id,created_time,full_picture,attachments{subattachments,media,type,title,url},insights.metric(page_fans,post_impressions,post_clicks,post_reactions_like_total,post_impressions_unique),reactions.limit(0).summary(true),comments.limit(0).summary(true)",
                },
            )
            response.raise_for_status()
            posts = response.json().get("data", [])

        if not posts:
            logger.info(f"No posts available for Facebook account: {account.username}")
            return

        formatted_posts = []
        for post in posts:
            try:
                # Deduplicate insights by (name, period)
                insights_data = post.get("insights", {}).get("data", [])
                insights_dict = {}
                for item in insights_data:
                    name = item["name"]
                    period = item["period"]
                    value = item["values"][0]["value"]
                    insights_dict[(name, period)] = {"value": value, "period": period}
                insights = [
                    {name: data} for (name, period), data in insights_dict.items()
                ]

                # Extract reactions and comments
                likes = post.get("reactions", {}).get("summary", {}).get("total_count", 0)
                comments = post.get("comments", {}).get("summary", {}).get("total_count", 0)
                images = extract_images(post)

                post_data = {
                    "id": post.get("id"),
                    "message": post.get("message", ""),
                    "created_time": post.get("created_time"),
                    "full_picture": post.get("full_picture", ""),
                    "attachments": post.get("attachments", {}).get("data", []),
                    "insights": insights,
                    "likes": likes,
                    "comments": comments,
                    "images": images
                }
                formatted_posts.append(post_data)
            except Exception as e:
                logger.error(f"Error processing post {post.get('id')}: {str(e)}")
                continue

        # Sort by composite_score and get the top 5
        top_posts = sorted(
            formatted_posts,
            key=lambda x: composite_score(x["insights"]),
            reverse=True
        )[:5]

        # Store top posts in database
        for post in top_posts:
            created_time = None
            if post.get("created_time"):
                try:
                    created_time = datetime.strptime(post["created_time"], "%Y-%m-%dT%H:%M:%S%z")
                    created_time = created_time.replace(tzinfo=None)
                except ValueError:
                    try:
                        created_time = datetime.strptime(post["created_time"], "%Y-%m-%dT%H:%M:%S+0000")
                    except ValueError:
                        logger.error(f"Could not parse created_time: {post['created_time']}")

            # Extract metrics from insights
            def get_metric_value(insights, metric_name):
                for metric in insights:
                    if metric_name in metric:
                        value = metric[metric_name].get("value")
                        if value is not None:
                            return value
                return 0

            impressions = get_metric_value(post["insights"], "post_impressions")
            clicks = get_metric_value(post["insights"], "post_clicks")
            reach = get_metric_value(post["insights"], "post_impressions_unique")
            likes = post.get("likes", 0)
            comments = post.get("comments", 0)
            engagement_rate = 0
            if reach > 0:
                engagement_rate = ((likes + comments + clicks) / reach) * 100

            top_post = model.FacebookTopPerformingPost(
                organisation_id=account.organisation_id,
                page_id=account.page_id,
                post_id=post["id"],
                message=post.get("message"),
                created_time=created_time,
                permalink_url=f"https://www.facebook.com/{post['id']}",
                full_picture=post.get("full_picture"),
                impressions=impressions,
                reach=reach,
                likes=likes,
                comments=comments,
                clicks=clicks,
                engagement_rate=engagement_rate
            )
            top_post.set_attachments(post.get("attachments"))
            top_post.set_insights(post.get("insights"))
            top_post.set_images(post.get("images"))
            db.add(top_post)

        logger.info(f"Updated top performing posts for Facebook account: {account.username}")

    except Exception as e:
        logger.error(f"Error fetching top performing posts for {account.username}: {str(e)}")


async def fetch_and_store_facebook_metrics_for_account(account_id: str):
    """
    Fetch and store Facebook metrics for a specific account
    This is used when a new account is connected to immediately collect metrics
    """
    logger.info(f"Starting immediate Facebook metrics update for account ID: {account_id}")

    async with SessionLocal() as db:
        try:
            # Get the specific Facebook social media account
            result = await db.execute(
                select(model.SocialMediaAccount).filter(
                    model.SocialMediaAccount.id == account_id,
                    model.SocialMediaAccount.platform == "facebook",
                    model.SocialMediaAccount.login_status == True
                )
            )
            account = result.scalars().first()

            if not account:
                logger.error(f"Facebook account with ID {account_id} not found or not active")
                return

            # Fetch and store page metrics (overview)
            await fetch_and_store_page_metrics(account, db)

            # Fetch and store audience demographics
            await fetch_and_store_audience_demographics(account, db)

            # Fetch and store growth trends
            # Use naive datetime objects consistently
            end_date = datetime.now()
            start_date = end_date - relativedelta(months=6)
            # await fetch_and_store_audience_growth_trend(account, db, start_date, end_date)
            # await fetch_and_store_engagement_growth_trend(account, db, start_date, end_date)
            # await fetch_and_store_reach_growth_trend(account, db, start_date, end_date)
            # await fetch_and_store_click_rate_growth_trend(account, db, start_date, end_date)

            # # Fetch and store top performing posts
            # await fetch_and_store_top_performing_posts(account, db)

            # Commit all changes
            await db.commit()
            logger.info(f"Completed immediate Facebook metrics update for account: {account.username}")

        except Exception as e:
            await db.rollback()
            logger.error(f"Error in immediate Facebook metrics update for account ID {account_id}: {str(e)}")


async def scheduled_facebook_metrics_update():
    """
    Function to be called by the scheduler to update Facebook metrics for all accounts
    """
    await fetch_and_store_facebook_metrics()

# post_clicks_by_type,post_activity_by_action_type,page_total_actions,page_post_engagements,page_daily_follows,page_fan_adds_by_paid_non_paid_unique,page_lifetime_engaged_followers_unique,page_daily_follows_unique,page_daily_unfollows_unique,page_follows
# page_impressions,page_impressions_unique,page_impressions_paid,page_impressions_paid_unique,page_impressions_viral,page_impressions_viral_unique,page_impressions_nonviral,page_impressions_nonviral_unique


async def facebook_audience_growth(account, db):
    async with httpx.AsyncClient(timeout=timeout) as client:
        response = await client.get(
            f"{settings.FACEBOOK_GRAPH_API_URL}/me/insights",
            params={
                "metric": "page_fans_locale,page_fans_city,page_fans_country,page_fan_adds,page_fan_adds_unique,page_fan_removes,page_fan_removes_unique,page_fans",
                "date_preset": "last_90d",
                "period": "day",
                "access_token": account.page_access_token
            }
        )
        response.raise_for_status()
        data = response.json().get("data", [])
        
        for insights in data:
            name = insights.get("name", "")
            period = insights.get("period", "")
            values = insights.get("values", [])
            title = insights.get("title", "")
            description = insights.get("description", "")

            # Query for existing timeseries row
            result = await db.execute(
                select(model.FacebookAudienceMetricTimeSeries)
                .where(
                    model.FacebookAudienceMetricTimeSeries.organisation_id == account.organisation_id,
                    model.FacebookAudienceMetricTimeSeries.page_id == account.page_id,
                    model.FacebookAudienceMetricTimeSeries.metric_name == name,
                    model.FacebookAudienceMetricTimeSeries.period == period,
                )
            )
            row = result.scalars().first()

            # Prepare new values as a dict keyed by end_time for deduplication
            new_values_dict = {v["end_time"]: v for v in values if "end_time" in v}

            if row:
                # Merge with existing values (deduplicate by end_time)
                existing_values = row.values if row.values else []
                existing_dict = {v["end_time"]: v for v in existing_values if "end_time" in v}
                # Update with new values (new values take precedence)
                existing_dict.update(new_values_dict)
                merged_values = list(sorted(existing_dict.values(), key=lambda x: x["end_time"]))
                row.values = merged_values
                row.title = title
                row.description = description
                row.collected_at = datetime.utcnow()
            else:
                # Create new row
                merged_values = list(sorted(new_values_dict.values(), key=lambda x: x["end_time"]))
                row = model.FacebookAudienceMetricTimeSeries(
                    organisation_id=account.organisation_id,
                    page_id=account.page_id,
                    metric_name=name,
                    period=period,
                    values=merged_values,
                    title=title,
                    description=description,
                    collected_at=datetime.utcnow()
                )
                db.add(row)
        await db.commit()
        logger.info(f"Updated Facebook audience metric timeseries for account: {account.username}")


async def get_page_metrics(account, metrics, periods, since, until, access_token=None):
    """
    Fetch Facebook page metrics for the given metrics and periods.
    Returns a nested dict: {metric: {period: value, ...}, ...}
    """
    if access_token is None:
        access_token = getattr(account, "page_access_token", None) or getattr(account, "access_token", None)
    results = {}
    async with httpx.AsyncClient(timeout=timeout) as client:
        for metric in metrics:
            results[metric] = {}
            for period in periods:
                try:
                    logger.info(f"Fetching metric: {metric}, period: {period}")
                    response = await client.get(
                        f"{FACEBOOK_GRAPH_API_URL}/{account.page_id}/insights",
                        params={
                            "metric": metric,
                            "period": period,
                            "since": since,
                            "until": until,
                            "access_token": access_token
                        },
                    )
                    response.raise_for_status()
                    data = response.json()
                    if "data" in data and len(data["data"]) > 0:
                        metric_data = data["data"][0]
                        values = [entry["value"] for entry in metric_data.get("values", [])]
                        # For cumulative metrics, take the most recent value; for others, sum
                        if metric in ["page_fans_total", "page_follows", "page_follower_count", "page_fans"]:
                            results[metric][period] = values[-1] if values else 0
                        else:
                            # If values are dicts (e.g., breakdowns), store as is
                            if values and isinstance(values[0], dict):
                                results[metric][period] = values
                            else:
                                results[metric][period] = sum(values) if values else 0
                    elif "error" in data:
                        logger.error(f"Error for {metric} ({period}): {data['error'].get('message')}")
                        results[metric][period] = None
                    else:
                        logger.info(f"No data for {metric} ({period})")
                        results[metric][period] = None
                except Exception as e:
                    logger.error(f"Exception for {metric} ({period}): {str(e)}")
                    results[metric][period] = None
    return results
