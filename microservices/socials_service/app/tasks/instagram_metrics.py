import asyncio
import json
import traceback
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from app.services.instagram import (
    fetch_conversations_and_messages, save_comments_to_db)
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.orm import selectinload
from app.models import model
from app.database.session import SessionLocal
from app.utils.redis_cache import redis_client
from app.utils.logger import get_logger
import httpx
from redis.exceptions import RedisError
from app.core.config import settings
logger = get_logger(__name__)

# Constants
INSTAGRAM_API_BASE_URL = "https://graph.instagram.com/v23"
redis_expiry_time = 60 * 60 * 12  # 12 hours
timeout = httpx.Timeout(10.0, connect=10.0, read=15.0, write=20.0)

# Error classes for specific handling
class InstagramAPIError(Exception):
    """Exception raised for Instagram API errors"""
    def __init__(self, status_code, message, response_data=None):
        self.status_code = status_code
        self.message = message
        self.response_data = response_data
        super().__init__(f"Instagram API Error ({status_code}): {message}")

class InstagramRateLimitError(InstagramAPIError):
    """Exception raised for Instagram API rate limit errors"""
    def __init__(self, message, retry_after=None):
        super().__init__(429, message)
        self.retry_after = retry_after

class InstagramAuthError(InstagramAPIError):
    """Exception raised for Instagram API authentication errors"""
    def __init__(self, message):
        super().__init__(401, message)

class InstagramDataError(Exception):
    """Exception raised for errors in processing Instagram data"""
    pass

class InstagramCacheError(Exception):
    """Exception raised for errors in caching Instagram data"""
    pass


async def fetch_and_store_instagram_metrics():
    """
    Fetch all Instagram metrics for all organizations and store them in the database and Redis.
    This function is meant to be called by a scheduler twice a day.
    """
    logger.info("Starting scheduled Instagram metrics update")

    # Track overall statistics
    total_accounts = 0
    successful_accounts = 0
    failed_accounts = 0
    rate_limited_accounts = 0

    async with SessionLocal() as db:
        try:
            # Get all Instagram social media accounts
            try:
                result = await db.execute(
                    select(model.SocialMediaAccount)
                    .options(selectinload(model.SocialMediaAccount.posts))
                    .where(
                        model.SocialMediaAccount.platform == "instagram"
                    )
                )
                social_accounts = result.scalars().all()
                total_accounts = len(social_accounts)
            except SQLAlchemyError as e:
                logger.error(f"Database error when fetching Instagram accounts: {str(e)}")
                raise

            logger.info(f"Found {total_accounts} Instagram accounts to update")

            # Process accounts in batches to avoid long transactions
            batch_size = 10
            for i in range(0, total_accounts, batch_size):
                batch = social_accounts[i:i+batch_size]
                logger.info(
                    f"Processing batch {i//batch_size + 1} of "
                    f"{(total_accounts + batch_size - 1)//batch_size} batches")

                for account in batch:
                    # Track success/failure for each account
                    account_success = False
                    rate_limited = False
                    try:
                        # await fetch_conversations_and_messages(account)
                        pass
                    except Exception as e:
                        logger.error(f'error occurred in updating conversations and messages: {str(e)}')

                    try:
                        for posts in account.posts:
                            await save_comments_to_db(
                                posts.post_id, account.access_token)
                    except Exception as e:
                        logger.error(f'error occurred in updating comments {str(e)}')

                    try:
                        # Fetch and store account metrics (overview)
                        # try:
                        #     await fetch_and_store_account_metrics(account, db)
                        # except InstagramRateLimitError as e:
                        #     logger.warning(f"Rate limit hit when fetching account metrics for {account.username}. Retry after {e.retry_after} seconds")
                        #     rate_limited = True
                        #     continue  # Skip to next account if rate limited
                        # except Exception as e:
                        #     logger.error(f"Error fetching account metrics for {account.username}: {str(e)}")
                            # Continue with other metrics even if this one fails

                        # Fetch and store audience demographics
                        # try:
                        #     await fetch_and_store_audience_demographics(account, db)
                        # except Exception as e:
                        #     logger.error(f"Error fetching audience demographics for {account.username}: {str(e)}")
                            # Continue with other metrics even if this one fails

                        # Fetch and store media metrics
                        try:
                            # await fetch_and_store_media_metrics(account, db)
                            pass
                        except InstagramRateLimitError as e:
                            logger.warning(f"Rate limit hit when fetching media metrics for {account.username}. Retry after {e.retry_after} seconds")
                            rate_limited = True
                            # Continue with commit even if this step fails due to rate limiting
                        except Exception as e:
                            logger.error(f"Error fetching media metrics for {account.username}: {str(e)}")
                            # Continue with commit even if this step fails

                        # Mark as successful if we got this far
                        account_success = True
                        logger.info(f"Successfully updated metrics for Instagram account: {account.username}")

                    except Exception as e:
                        logger.error(f"Error updating metrics for Instagram account {account.username}: {str(e)}")
                        logger.error(traceback.format_exc())

                    # Update statistics
                    if rate_limited:
                        rate_limited_accounts += 1
                    elif account_success:
                        successful_accounts += 1
                    else:
                        failed_accounts += 1

                    # Sleep to avoid rate limiting
                    await asyncio.sleep(1)

                # Commit after each batch
                try:
                    await db.commit()
                    logger.info(f"Committed batch {i//batch_size + 1}")
                except SQLAlchemyError as e:
                    await db.rollback()
                    logger.error(f"Database error when committing batch {i//batch_size + 1}: {str(e)}")
                    # Continue with next batch even if this one fails

            logger.info(f"Completed scheduled Instagram metrics update. Total: {total_accounts}, Successful: {successful_accounts}, Failed: {failed_accounts}, Rate Limited: {rate_limited_accounts}")

        except Exception as e:
            await db.rollback()
            logger.error(f"Error in scheduled Instagram metrics update: {str(e)}")
            logger.error(traceback.format_exc())

# async def fetch_and_store_account_metrics(account, db: AsyncSession):
#     """Fetch and store account metrics (overview)"""
#     try:
#         # Fetch metrics from Instagram API
#         async with httpx.AsyncClient(timeout=timeout) as client:
#             try:
#                 response = await client.get(
#                     f"{INSTAGRAM_API_BASE_URL}/{account.social_media_user_id}",
#                     params={
#                         "access_token": account.access_token,
#                         "fields": "username,name,account_type,followers_count,follows_count,media_count,profile_picture_url",
#                     },
#                 )

#                 # Handle HTTP errors with specific error types
#                 if response.status_code == 401 or response.status_code == 403:
#                     error_data = response.json()
#                     error_message = error_data.get('error', {}).get('message', 'Authentication error')
#                     logger.error(f"Instagram auth error for {account.username}: {error_message}")
#                     raise InstagramAuthError(error_message)

#                 elif response.status_code == 429:
#                     retry_after = response.headers.get('Retry-After', '60')
#                     logger.warning(f"Rate limit exceeded for {account.username}. Retry after {retry_after} seconds")
#                     raise InstagramRateLimitError(f"Rate limit exceeded", retry_after=retry_after)

#                 response.raise_for_status()
#                 account_data = response.json()

#             except httpx.RequestError as e:
#                 logger.error(f"Request error for {account.username}: {str(e)}")
#                 raise InstagramAPIError(500, f"Request failed: {str(e)}")

#             except httpx.HTTPStatusError as e:
#                 status_code = e.response.status_code
#                 try:
#                     error_data = e.response.json()
#                     error_message = error_data.get('error', {}).get('message', str(e))
#                 except:
#                     error_message = str(e)

#                 logger.error(f"HTTP error {status_code} for {account.username}: {error_message}")
#                 raise InstagramAPIError(status_code, error_message, e.response.json() if hasattr(e.response, 'json') else None)

#         # Extract metrics with validation
#         try:
#             followers_count = account_data.get("followers_count", 0)
#             follows_count = account_data.get("follows_count", 0)
#             media_count = account_data.get("media_count", 0)

#             # Validate data types
#             if not all(isinstance(x, int) for x in [followers_count, follows_count, media_count]):
#                 logger.warning(f"Non-integer metrics for {account.username}, converting to integers")
#                 followers_count = int(followers_count) if followers_count else 0
#                 follows_count = int(follows_count) if follows_count else 0
#                 media_count = int(media_count) if media_count else 0

#         except (ValueError, TypeError) as e:
#             logger.error(f"Data type error for {account.username}: {str(e)}")
#             # Use default values if conversion fails
#             followers_count = 0
#             follows_count = 0
#             media_count = 0

#         # Create metrics record
#         metrics = model.InstagramAccountMetrics(
#             organisation_id=account.organisation_id,
#             instagram_user_id=account.social_media_user_id,
#             followers_count=followers_count,
#             follows_count=follows_count,
#             media_count=media_count,
#             collected_at=datetime.now()
#         )

#         # Add metrics to database with error handling
#         try:
#             db.add(metrics)
#         except SQLAlchemyError as e:
#             logger.error(f"Database error for {account.username}: {str(e)}")
#             raise

#         # Cache metrics in Redis with error handling
#         try:
#             cache_key = f"ig_overview_{account.organisation_id}"
#             cache_data = {
#                 "followers_count": followers_count,
#                 "follows_count": follows_count,
#                 "media_count": media_count,
#                 "updated_at": datetime.now().isoformat()
#             }
#             redis_client.setex(cache_key, redis_expiry_time, json.dumps(cache_data))
#         except RedisError as e:
#             logger.error(f"Redis caching error for {account.username}: {str(e)}")
#             # Continue even if caching fails
#             pass

#         logger.info(f"Updated account metrics for Instagram account: {account.username}")

#     except InstagramAPIError as e:
#         logger.error(f"Instagram API error for {account.username}: {e.message}")
#         if isinstance(e, InstagramRateLimitError):
#             logger.warning(f"Will retry after {e.retry_after} seconds")
#         raise
#     except InstagramDataError as e:
#         logger.error(f"Data processing error for {account.username}: {str(e)}")
#         raise
#     except Exception as e:
#         logger.error(f"Unexpected error fetching account metrics for {account.username}: {str(e)}")
#         logger.error(traceback.format_exc())
#         raise

# async def fetch_and_store_audience_demographics(account, db: AsyncSession):
#     """Fetch and store audience demographics"""

#     _ = db  # Acknowledge db parameter to avoid unused parameter warning
#     logger.info(f"Audience demographics not available for Instagram account: {account.username}")

# async def fetch_and_store_instagram_metrics_for_account(account_id: str):
#     """
#     Fetch and store Instagram metrics for a specific account
#     This is used when a new account is connected to immediately collect metrics
#     """
#     logger.info(f"Starting immediate Instagram metrics update for account ID: {account_id}")

#     async with SessionLocal() as db:
#         try:
#             # Get the specific Instagram social media account
#             try:
#                 result = await db.execute(
#                     select(model.SocialMediaAccount).filter(
#                         model.SocialMediaAccount.id == account_id,
#                         model.SocialMediaAccount.platform == "instagram",
#                         model.SocialMediaAccount.login_status == True
#                     )
#                 )
#                 account = result.scalars().first()
#             except SQLAlchemyError as e:
#                 logger.error(f"Database error when fetching account {account_id}: {str(e)}")
#                 raise

#             if not account:
#                 logger.error(f"Instagram account with ID {account_id} not found or not active")
#                 return

#             # Track success/failure of each step
#             metrics_success = False
#             demographics_success = False
#             media_success = False

#             # Fetch and store account metrics (overview)
#             try:
#                 await fetch_and_store_account_metrics(account, db)
#                 metrics_success = True
#             except InstagramRateLimitError as e:
#                 logger.warning(f"Rate limit hit when fetching account metrics. Retry after {e.retry_after} seconds")
#                 # Continue with other steps even if this one fails
#             except Exception as e:
#                 logger.error(f"Error fetching account metrics: {str(e)}")
#                 # Continue with other steps even if this one fails

#             # Fetch and store audience demographics
#             try:
#                 await fetch_and_store_audience_demographics(account, db)
#                 demographics_success = True
#             except Exception as e:
#                 logger.error(f"Error fetching audience demographics: {str(e)}")
#                 # Continue with other steps even if this one fails

#             # Fetch and store media metrics
#             try:
#                 await fetch_and_store_media_metrics(account, db)
#                 media_success = True
#             except InstagramRateLimitError as e:
#                 logger.warning(f"Rate limit hit when fetching media metrics. Retry after {e.retry_after} seconds")
#                 # Continue with other steps even if this one fails
#             except Exception as e:
#                 logger.error(f"Error fetching media metrics: {str(e)}")
#                 # Continue with other steps even if this one fails

#             # Commit all changes if at least one step succeeded
#             if metrics_success or demographics_success or media_success:
#                 try:
#                     await db.commit()
#                     logger.info(f"Completed immediate Instagram metrics update for account: {account.username}")
#                     logger.info(f"Success status - Account metrics: {metrics_success}, Demographics: {demographics_success}, Media metrics: {media_success}")
#                 except SQLAlchemyError as e:
#                     await db.rollback()
#                     logger.error(f"Database error when committing changes: {str(e)}")
#             else:
#                 await db.rollback()
#                 logger.error(f"No metrics were successfully collected for account ID {account_id}")

#         except Exception as e:
#             await db.rollback()
#             logger.error(f"Error in immediate Instagram metrics update for account ID {account_id}: {str(e)}")
#             logger.error(traceback.format_exc())


# #################################
# get media insights
async def fetch_and_store_media_metrics(account, db_session: AsyncSession):
    """Fetch and store media metrics on post made from the app"""
    try:
        # fetch all post on the db
        logger.info("fetching media insights")
        result = await db_session.execute(
            select(model.Post)
            .where(
                model.Post.social_media_account_id == account.id
            )
        )
        posts = result.scalars().all()
        params = {
            "metric": ",".join([
                "views",
                "total_interactions",
                "shares",
                "saved",
                "reach",
                "likes",
                "follows",
                "comments",
                "profile_visits"
            ]),
        }
        headers = {"Authorization": f"Bearer {account.access_token}"}

        # 2. Build and fire the request
        async with httpx.AsyncClient(timeout=timeout) as client:
            for post in posts:
                url = f"{settings.INSTAGRAM_API_BASE_URL}/{post.post_id}/insights"

                resp = await client.get(url, params=params, headers=headers)
                resp.raise_for_status()
                payload = resp.json()

                response = payload.get("data", [])
                for item in response:
                    name = item["name"]
                    period = item.get("period")
                    value = item.get("values", [])[0].get("value", 0)

                    stmt = insert(model.MediaMetrics).values(
                        name=name,
                        period=period,
                        value=value,
                        post_id=post.post_id,
                        scheduled_content_id=post.schedulecontent_id
                    ).on_conflict_do_update(
                        index_elements=['post_id', 'scheduled_content_id', 'name', 'period'],
                        set_={
                            "value": value,
                            # add other fields to update if needed
                        }
                    )
                    await db_session.execute(stmt)

    except httpx.RequestError as e:
        logger.error(f"Request error for media insights: {str(e)}")
    except Exception as e:
        logger.error(f"Request error for media insights: {str(e)}")


async def scheduled_instagram_metrics_update():
    """
    Function to be called by the scheduler to update Instagram metrics for all accounts
    """
    await fetch_and_store_instagram_metrics()
